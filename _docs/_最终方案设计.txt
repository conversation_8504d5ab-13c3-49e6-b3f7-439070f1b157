# AI Studio 最终方案设计文档

## 文档信息
- **项目名称**：AI Studio - 本地AI助手桌面应用
- **文档版本**：v2.0 Final
- **目标平台**：Windows 和 macOS 桌面应用
- **主题系统**：深色/浅色主题切换
- **国际化**：中文/英文双语支持
- **样式技术**：Tailwind CSS + SCSS
- **文档状态**：最终版本
- **最后更新**：2024年12月

---

## 1. 技术架构

### 1.1 完整技术栈

#### 1.1.1 前端技术栈
```
核心框架：
├── Vue 3.4+ (Composition API)
├── TypeScript 5.0+
├── Tauri 1.5+ (桌面应用框架)
└── Vite 5.0+ (构建工具)

UI框架：
├── Tailwind CSS 3.4+ (原子化CSS)
├── SCSS (样式预处理器)
├── Headless UI (无样式组件)
└── Heroicons (图标库)

状态管理：
├── Pinia (状态管理)
├── VueUse (组合式工具库)
└── Vue Router 4+ (路由管理)

开发工具：
├── ESLint + Prettier (代码规范)
├── Vitest (单元测试)
├── Playwright (E2E测试)
└── TypeScript (类型检查)
```

#### 1.1.2 后端技术栈
```
核心语言：
├── Rust 1.75+ (系统编程语言)
├── Tokio (异步运行时)
├── Serde (序列化/反序列化)
└── Anyhow (错误处理)

数据存储：
├── SQLite (关系型数据库)
├── ChromaDB (向量数据库)
├── SQLx (数据库ORM)
└── Tantivy (全文搜索引擎)

AI推理引擎：
├── Candle (Rust原生ML框架)
├── llama.cpp (C++推理引擎)
├── ONNX Runtime (跨平台推理)
└── Tokenizers (分词器)

网络通信：
├── Tokio-tungstenite (WebSocket)
├── Reqwest (HTTP客户端)
├── mDNS (服务发现)
└── libp2p (P2P网络)
```

#### 1.1.3 桌面集成技术
```
桌面框架：
├── Tauri (Rust + Web技术)
├── WebView2 (Windows)
├── WKWebView (macOS)
└── 系统托盘集成

系统集成：
├── 文件系统访问
├── 系统通知
├── 快捷键支持
├── 自动更新机制
└── 原生菜单栏
```

### 1.2 系统架构设计

#### 1.2.1 整体架构图
```
┌─────────────────────────────────────────────────────────────┐
│                    AI Studio 桌面应用                        │
├─────────────────────────────────────────────────────────────┤
│                      前端层 (Vue3)                          │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐ ┌─────────┐ │
│  │   聊天界面   │ │  知识库管理  │ │  模型管理   │ │  设置   │ │
│  └─────────────┘ └─────────────┘ └─────────────┘ └─────────┘ │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐ ┌─────────┐ │
│  │  多模态交互  │ │  远程协作   │ │  插件管理   │ │  监控   │ │
│  └─────────────┘ └─────────────┘ └─────────────┘ └─────────┘ │
├─────────────────────────────────────────────────────────────┤
│                    Tauri Bridge Layer                       │
│  ┌─────────────────────────────────────────────────────────┐ │
│  │              IPC 通信层 (JSON-RPC)                      │ │
│  └─────────────────────────────────────────────────────────┘ │
├─────────────────────────────────────────────────────────────┤
│                     后端层 (Rust)                           │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐ ┌─────────┐ │
│  │  聊天服务   │ │  知识库服务  │ │  模型服务   │ │ 系统服务 │ │
│  └─────────────┘ └─────────────┘ └─────────────┘ └─────────┘ │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐ ┌─────────┐ │
│  │  AI推理引擎  │ │  网络服务   │ │  插件引擎   │ │ 安全服务 │ │
│  └─────────────┘ └─────────────┘ └─────────────┘ └─────────┘ │
├─────────────────────────────────────────────────────────────┤
│                      数据层                                 │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐ ┌─────────┐ │
│  │   SQLite    │ │  ChromaDB   │ │  文件系统   │ │  缓存   │ │
│  │  (关系数据)  │ │  (向量数据)  │ │  (模型文件)  │ │ (内存)  │ │
│  └─────────────┘ └─────────────┘ └─────────────┘ └─────────┘ │
└─────────────────────────────────────────────────────────────┘
```

#### 1.2.2 核心模块架构
```
AI Studio Core Architecture:

┌─── 用户界面层 ───┐
│ Vue3 Components │ ← 响应式UI组件
│ Pinia Stores    │ ← 状态管理
│ Router & Guards │ ← 路由控制
└─────────────────┘
         ↕ IPC
┌─── 业务逻辑层 ───┐
│ Chat Service    │ ← 聊天会话管理
│ Knowledge Svc   │ ← 知识库管理
│ Model Service   │ ← 模型生命周期
│ Network Service │ ← P2P网络通信
│ Plugin Engine  │ ← 插件系统
└─────────────────┘
         ↕
┌─── AI推理层 ────┐
│ Inference Mgr   │ ← 推理任务调度
│ Model Cache     │ ← 模型缓存管理
│ Token Manager   │ ← 分词处理
│ Embedding Svc   │ ← 向量化服务
└─────────────────┘
         ↕
┌─── 数据持久层 ───┐
│ SQLite DB       │ ← 结构化数据
│ ChromaDB        │ ← 向量数据库
│ File System     │ ← 文件存储
│ Cache Layer     │ ← 多级缓存
└─────────────────┘
```

### 1.3 技术选型理由

#### 1.3.1 前端技术选型
- **Vue3 + Composition API**：现代化响应式框架，优秀的TypeScript支持
- **Tauri**：轻量级桌面应用框架，安全性高，包体积小
- **Tailwind CSS**：原子化CSS，快速开发，易于维护
- **Pinia**：Vue3官方推荐状态管理，简洁的API设计

#### 1.3.2 后端技术选型
- **Rust**：内存安全，高性能，适合AI推理和系统编程
- **SQLite**：嵌入式数据库，无需额外部署，适合桌面应用
- **ChromaDB**：专业向量数据库，支持语义搜索
- **Candle**：Rust原生ML框架，与系统深度集成

#### 1.3.3 AI引擎选型
- **多引擎支持**：Candle、llama.cpp、ONNX，覆盖不同模型格式
- **本地推理**：保护用户隐私，无需网络依赖
- **硬件加速**：支持CPU、GPU、Metal等加速方案

---

## 2. 项目结构

### 2.1 前端目录结构

```
src/                                    # 前端源代码根目录
├── main.ts                            # 应用入口文件
├── App.vue                            # 根组件
├── style.css                          # 全局样式
├── assets/                            # 静态资源目录
│   ├── images/                        # 图片资源
│   │   ├── icons/                     # 图标文件
│   │   ├── logos/                     # Logo文件
│   │   └── backgrounds/               # 背景图片
│   ├── fonts/                         # 字体文件
│   └── styles/                        # 样式文件
│       ├── globals.scss               # 全局SCSS变量
│       ├── themes.scss                # 主题样式
│       └── components.scss            # 组件样式
├── components/                        # 可复用组件
│   ├── common/                        # 通用组件
│   │   ├── Button.vue                 # 按钮组件
│   │   ├── Input.vue                  # 输入框组件
│   │   ├── Modal.vue                  # 模态框组件
│   │   ├── Loading.vue                # 加载组件
│   │   ├── Toast.vue                  # 提示组件
│   │   ├── Dropdown.vue               # 下拉菜单
│   │   ├── Tabs.vue                   # 标签页组件
│   │   ├── Pagination.vue             # 分页组件
│   │   └── VirtualList.vue            # 虚拟滚动列表
│   ├── layout/                        # 布局组件
│   │   ├── Sidebar.vue                # 侧边栏
│   │   ├── Header.vue                 # 顶部栏
│   │   ├── Footer.vue                 # 底部栏
│   │   ├── Navigation.vue             # 导航组件
│   │   └── Breadcrumb.vue             # 面包屑导航
│   ├── chat/                          # 聊天相关组件
│   │   ├── ChatContainer.vue          # 聊天容器
│   │   ├── MessageList.vue            # 消息列表
│   │   ├── MessageItem.vue            # 消息项
│   │   ├── MessageInput.vue           # 消息输入框
│   │   ├── SessionList.vue            # 会话列表
│   │   ├── SessionItem.vue            # 会话项
│   │   ├── AttachmentUpload.vue       # 附件上传
│   │   ├── CodeBlock.vue              # 代码块显示
│   │   └── MarkdownRenderer.vue       # Markdown渲染
│   ├── knowledge/                     # 知识库组件
│   │   ├── KnowledgeBaseList.vue      # 知识库列表
│   │   ├── DocumentUpload.vue         # 文档上传
│   │   ├── DocumentList.vue           # 文档列表
│   │   ├── DocumentViewer.vue         # 文档查看器
│   │   ├── SearchInterface.vue        # 搜索界面
│   │   └── EmbeddingProgress.vue      # 向量化进度
│   ├── model/                         # 模型管理组件
│   │   ├── ModelList.vue              # 模型列表
│   │   ├── ModelCard.vue              # 模型卡片
│   │   ├── ModelDownload.vue          # 模型下载
│   │   ├── ModelConfig.vue            # 模型配置
│   │   ├── DownloadProgress.vue       # 下载进度
│   │   └── ModelMetrics.vue           # 模型性能指标
│   ├── multimodal/                    # 多模态组件
│   │   ├── ImageUpload.vue            # 图片上传
│   │   ├── AudioRecorder.vue          # 音频录制
│   │   ├── VideoPlayer.vue            # 视频播放
│   │   ├── FilePreview.vue            # 文件预览
│   │   └── MediaGallery.vue           # 媒体画廊
│   ├── network/                       # 网络功能组件
│   │   ├── DeviceList.vue             # 设备列表
│   │   ├── ConnectionStatus.vue       # 连接状态
│   │   ├── ResourceSharing.vue        # 资源共享
│   │   ├── TransferProgress.vue       # 传输进度
│   │   └── NetworkSettings.vue        # 网络设置
│   ├── plugins/                       # 插件系统组件
│   │   ├── PluginList.vue             # 插件列表
│   │   ├── PluginCard.vue             # 插件卡片
│   │   ├── PluginConfig.vue           # 插件配置
│   │   ├── PluginStore.vue            # 插件商店
│   │   └── PluginDeveloper.vue        # 插件开发工具
│   └── settings/                      # 设置组件
│       ├── GeneralSettings.vue        # 通用设置
│       ├── ThemeSettings.vue          # 主题设置
│       ├── LanguageSettings.vue       # 语言设置
│       ├── ModelSettings.vue          # 模型设置
│       ├── NetworkSettings.vue        # 网络设置
│       ├── PrivacySettings.vue        # 隐私设置
│       ├── AdvancedSettings.vue       # 高级设置
│       └── AboutDialog.vue            # 关于对话框
├── views/                             # 页面视图
│   ├── ChatView.vue                   # 聊天页面
│   ├── KnowledgeView.vue              # 知识库页面
│   ├── ModelView.vue                  # 模型管理页面
│   ├── MultimodalView.vue             # 多模态页面
│   ├── NetworkView.vue                # 网络功能页面
│   ├── PluginView.vue                 # 插件管理页面
│   ├── SettingsView.vue               # 设置页面
│   ├── MonitorView.vue                # 监控页面
│   └── WelcomeView.vue                # 欢迎页面
├── stores/                            # Pinia状态管理
│   ├── index.ts                       # Store入口
│   ├── chat.ts                        # 聊天状态
│   ├── knowledge.ts                   # 知识库状态
│   ├── model.ts                       # 模型状态
│   ├── multimodal.ts                  # 多模态状态
│   ├── network.ts                     # 网络状态
│   ├── plugin.ts                      # 插件状态
│   ├── settings.ts                    # 设置状态
│   ├── theme.ts                       # 主题状态
│   ├── i18n.ts                        # 国际化状态
│   └── system.ts                      # 系统状态
├── composables/                       # 组合式函数
│   ├── useChat.ts                     # 聊天功能
│   ├── useKnowledge.ts                # 知识库功能
│   ├── useModel.ts                    # 模型功能
│   ├── useMultimodal.ts               # 多模态功能
│   ├── useNetwork.ts                  # 网络功能
│   ├── usePlugin.ts                   # 插件功能
│   ├── useTheme.ts                    # 主题功能
│   ├── useI18n.ts                     # 国际化功能
│   ├── useNotification.ts             # 通知功能
│   ├── useClipboard.ts                # 剪贴板功能
│   ├── useKeyboard.ts                 # 键盘快捷键
│   ├── useFileSystem.ts               # 文件系统
│   ├── usePerformance.ts              # 性能监控
│   └── useValidation.ts               # 表单验证
├── utils/                             # 工具函数
│   ├── api.ts                         # API调用封装
│   ├── constants.ts                   # 常量定义
│   ├── helpers.ts                     # 辅助函数
│   ├── formatters.ts                  # 格式化函数
│   ├── validators.ts                  # 验证函数
│   ├── storage.ts                     # 本地存储
│   ├── crypto.ts                      # 加密工具
│   ├── file.ts                        # 文件处理
│   ├── date.ts                        # 日期处理
│   ├── string.ts                      # 字符串处理
│   ├── array.ts                       # 数组处理
│   ├── object.ts                      # 对象处理
│   └── debounce.ts                    # 防抖节流
├── types/                             # TypeScript类型定义
│   ├── index.ts                       # 类型入口
│   ├── chat.ts                        # 聊天相关类型
│   ├── knowledge.ts                   # 知识库类型
│   ├── model.ts                       # 模型类型
│   ├── multimodal.ts                  # 多模态类型
│   ├── network.ts                     # 网络类型
│   ├── plugin.ts                      # 插件类型
│   ├── settings.ts                    # 设置类型
│   ├── api.ts                         # API类型
│   ├── common.ts                      # 通用类型
│   └── global.d.ts                    # 全局类型声明
├── router/                            # 路由配置
│   ├── index.ts                       # 路由入口
│   ├── guards.ts                      # 路由守卫
│   └── routes.ts                      # 路由定义
├── i18n/                              # 国际化
│   ├── index.ts                       # i18n配置
│   ├── locales/                       # 语言包
│   │   ├── zh-CN/                     # 中文语言包
│   │   │   ├── common.json            # 通用翻译
│   │   │   ├── chat.json              # 聊天翻译
│   │   │   ├── knowledge.json         # 知识库翻译
│   │   │   ├── model.json             # 模型翻译
│   │   │   ├── settings.json          # 设置翻译
│   │   │   └── errors.json            # 错误翻译
│   │   └── en-US/                     # 英文语言包
│   │       ├── common.json            # 通用翻译
│   │       ├── chat.json              # 聊天翻译
│   │       ├── knowledge.json         # 知识库翻译
│   │       ├── model.json             # 模型翻译
│   │       ├── settings.json          # 设置翻译
│   │       └── errors.json            # 错误翻译
│   └── plugins/                       # i18n插件
├── plugins/                           # Vue插件
│   ├── tauri.ts                       # Tauri集成插件
│   ├── toast.ts                       # 提示插件
│   └── directives.ts                  # 自定义指令
└── tests/                             # 测试文件
    ├── unit/                          # 单元测试
    ├── integration/                   # 集成测试
    ├── e2e/                           # 端到端测试
    └── fixtures/                      # 测试数据
```

### 2.2 后端目录结构

```
src-tauri/                             # Tauri后端根目录
├── Cargo.toml                        # Rust项目配置
├── tauri.conf.json                   # Tauri配置文件
├── build.rs                          # 构建脚本
├── src/                              # Rust源代码
│   ├── main.rs                       # 应用入口
│   ├── lib.rs                        # 库入口
│   ├── commands/                     # Tauri命令
│   │   ├── mod.rs                    # 命令模块入口
│   │   ├── chat.rs                   # 聊天命令
│   │   ├── knowledge.rs              # 知识库命令
│   │   ├── model.rs                  # 模型命令
│   │   ├── multimodal.rs             # 多模态命令
│   │   ├── network.rs                # 网络命令
│   │   ├── plugin.rs                 # 插件命令
│   │   ├── settings.rs               # 设置命令
│   │   ├── system.rs                 # 系统命令
│   │   └── file.rs                   # 文件操作命令
│   ├── services/                     # 业务服务层
│   │   ├── mod.rs                    # 服务模块入口
│   │   ├── chat_service.rs           # 聊天服务
│   │   │   ├── session_manager.rs    # 会话管理
│   │   │   ├── message_handler.rs    # 消息处理
│   │   │   ├── stream_handler.rs     # 流式响应处理
│   │   │   └── context_manager.rs    # 上下文管理
│   │   ├── knowledge_service.rs      # 知识库服务
│   │   │   ├── document_processor.rs # 文档处理
│   │   │   ├── embedding_service.rs  # 向量化服务
│   │   │   ├── search_engine.rs      # 搜索引擎
│   │   │   └── indexing_service.rs   # 索引服务
│   │   ├── model_service.rs          # 模型服务
│   │   │   ├── model_manager.rs      # 模型管理器
│   │   │   ├── download_manager.rs   # 下载管理器
│   │   │   ├── inference_engine.rs   # 推理引擎
│   │   │   └── model_cache.rs        # 模型缓存
│   │   ├── multimodal_service.rs     # 多模态服务
│   │   │   ├── image_processor.rs    # 图像处理
│   │   │   ├── audio_processor.rs    # 音频处理
│   │   │   ├── video_processor.rs    # 视频处理
│   │   │   └── file_converter.rs     # 文件转换
│   │   ├── network_service.rs        # 网络服务
│   │   │   ├── p2p_manager.rs        # P2P管理器
│   │   │   ├── discovery_service.rs  # 设备发现
│   │   │   ├── transfer_service.rs   # 文件传输
│   │   │   └── sync_service.rs       # 数据同步
│   │   ├── plugin_service.rs         # 插件服务
│   │   │   ├── plugin_manager.rs     # 插件管理器
│   │   │   ├── plugin_loader.rs      # 插件加载器
│   │   │   ├── plugin_runtime.rs     # 插件运行时
│   │   │   └── plugin_api.rs         # 插件API
│   │   ├── security_service.rs       # 安全服务
│   │   │   ├── encryption.rs         # 加密服务
│   │   │   ├── authentication.rs     # 认证服务
│   │   │   ├── permission.rs         # 权限管理
│   │   │   └── audit.rs              # 审计日志
│   │   └── system_service.rs         # 系统服务
│   │       ├── config_manager.rs     # 配置管理
│   │       ├── log_manager.rs        # 日志管理
│   │       ├── performance_monitor.rs # 性能监控
│   │       └── update_service.rs     # 更新服务
│   ├── ai/                           # AI推理模块
│   │   ├── mod.rs                    # AI模块入口
│   │   ├── engines/                  # 推理引擎
│   │   │   ├── mod.rs                # 引擎模块入口
│   │   │   ├── candle_engine.rs      # Candle引擎
│   │   │   ├── llama_cpp_engine.rs   # llama.cpp引擎
│   │   │   ├── onnx_engine.rs        # ONNX引擎
│   │   │   └── engine_manager.rs     # 引擎管理器
│   │   ├── models/                   # 模型定义
│   │   │   ├── mod.rs                # 模型模块入口
│   │   │   ├── llama.rs              # LLaMA模型
│   │   │   ├── mistral.rs            # Mistral模型
│   │   │   ├── qwen.rs               # Qwen模型
│   │   │   ├── phi.rs                # Phi模型
│   │   │   └── embedding.rs          # 嵌入模型
│   │   ├── tokenizers/               # 分词器
│   │   │   ├── mod.rs                # 分词器入口
│   │   │   ├── sentencepiece.rs     # SentencePiece
│   │   │   ├── tiktoken.rs           # TikToken
│   │   │   └── huggingface.rs        # HuggingFace分词器
│   │   ├── inference/                # 推理逻辑
│   │   │   ├── mod.rs                # 推理模块入口
│   │   │   ├── text_generation.rs   # 文本生成
│   │   │   ├── embedding_generation.rs # 向量生成
│   │   │   ├── multimodal_inference.rs # 多模态推理
│   │   │   └── batch_inference.rs    # 批量推理
│   │   └── utils/                    # AI工具函数
│   │       ├── mod.rs                # 工具模块入口
│   │       ├── model_loader.rs       # 模型加载器
│   │       ├── tensor_utils.rs       # 张量工具
│   │       ├── memory_manager.rs     # 内存管理
│   │       └── performance_utils.rs  # 性能工具
│   ├── database/                     # 数据库模块
│   │   ├── mod.rs                    # 数据库模块入口
│   │   ├── sqlite/                   # SQLite数据库
│   │   │   ├── mod.rs                # SQLite模块入口
│   │   │   ├── connection.rs         # 连接管理
│   │   │   ├── migrations.rs         # 数据库迁移
│   │   │   ├── models.rs             # 数据模型
│   │   │   └── queries.rs            # 查询语句
│   │   ├── chroma/                   # ChromaDB向量数据库
│   │   │   ├── mod.rs                # ChromaDB模块入口
│   │   │   ├── client.rs             # 客户端
│   │   │   ├── collections.rs        # 集合管理
│   │   │   ├── embeddings.rs         # 向量操作
│   │   │   └── search.rs             # 搜索功能
│   │   └── cache/                    # 缓存层
│   │       ├── mod.rs                # 缓存模块入口
│   │       ├── memory_cache.rs       # 内存缓存
│   │       ├── disk_cache.rs         # 磁盘缓存
│   │       └── cache_manager.rs      # 缓存管理器
│   ├── network/                      # 网络模块
│   │   ├── mod.rs                    # 网络模块入口
│   │   ├── p2p/                      # P2P网络
│   │   │   ├── mod.rs                # P2P模块入口
│   │   │   ├── discovery.rs          # 设备发现
│   │   │   ├── connection.rs         # 连接管理
│   │   │   ├── protocol.rs           # 通信协议
│   │   │   └── security.rs           # 安全通信
│   │   ├── http/                     # HTTP客户端
│   │   │   ├── mod.rs                # HTTP模块入口
│   │   │   ├── client.rs             # HTTP客户端
│   │   │   ├── download.rs           # 下载功能
│   │   │   └── upload.rs             # 上传功能
│   │   └── websocket/                # WebSocket
│   │       ├── mod.rs                # WebSocket模块入口
│   │       ├── server.rs             # WebSocket服务器
│   │       ├── client.rs             # WebSocket客户端
│   │       └── handlers.rs           # 消息处理器
│   ├── plugins/                      # 插件系统
│   │   ├── mod.rs                    # 插件模块入口
│   │   ├── runtime/                  # 插件运行时
│   │   │   ├── mod.rs                # 运行时入口
│   │   │   ├── wasm_runtime.rs       # WASM运行时
│   │   │   ├── js_runtime.rs         # JavaScript运行时
│   │   │   └── sandbox.rs            # 沙箱环境
│   │   ├── api/                      # 插件API
│   │   │   ├── mod.rs                # API模块入口
│   │   │   ├── chat_api.rs           # 聊天API
│   │   │   ├── knowledge_api.rs      # 知识库API
│   │   │   ├── model_api.rs          # 模型API
│   │   │   └── system_api.rs         # 系统API
│   │   └── store/                    # 插件商店
│   │       ├── mod.rs                # 商店模块入口
│   │       ├── registry.rs           # 插件注册表
│   │       ├── installer.rs          # 插件安装器
│   │       └── updater.rs            # 插件更新器
│   ├── utils/                        # 工具模块
│   │   ├── mod.rs                    # 工具模块入口
│   │   ├── config.rs                 # 配置工具
│   │   ├── logger.rs                 # 日志工具
│   │   ├── crypto.rs                 # 加密工具
│   │   ├── file.rs                   # 文件工具
│   │   ├── time.rs                   # 时间工具
│   │   ├── string.rs                 # 字符串工具
│   │   ├── json.rs                   # JSON工具
│   │   ├── hash.rs                   # 哈希工具
│   │   └── validation.rs             # 验证工具
│   ├── types/                        # 类型定义
│   │   ├── mod.rs                    # 类型模块入口
│   │   ├── chat.rs                   # 聊天类型
│   │   ├── knowledge.rs              # 知识库类型
│   │   ├── model.rs                  # 模型类型
│   │   ├── multimodal.rs             # 多模态类型
│   │   ├── network.rs                # 网络类型
│   │   ├── plugin.rs                 # 插件类型
│   │   ├── config.rs                 # 配置类型
│   │   ├── error.rs                  # 错误类型
│   │   └── common.rs                 # 通用类型
│   └── error/                        # 错误处理
│       ├── mod.rs                    # 错误模块入口
│       ├── app_error.rs              # 应用错误
│       ├── ai_error.rs               # AI错误
│       ├── db_error.rs               # 数据库错误
│       ├── network_error.rs          # 网络错误
│       ├── plugin_error.rs           # 插件错误
│       └── validation_error.rs       # 验证错误
├── migrations/                       # 数据库迁移
│   ├── 001_initial.sql               # 初始化迁移
│   ├── 002_chat_tables.sql           # 聊天表
│   ├── 003_knowledge_tables.sql      # 知识库表
│   ├── 004_model_tables.sql          # 模型表
│   ├── 005_network_tables.sql        # 网络表
│   ├── 006_plugin_tables.sql         # 插件表
│   └── 007_system_tables.sql         # 系统表
├── resources/                        # 资源文件
│   ├── models/                       # 预置模型
│   ├── plugins/                      # 预置插件
│   ├── configs/                      # 配置文件
│   └── assets/                       # 静态资源
├── tests/                            # 测试文件
│   ├── unit/                         # 单元测试
│   ├── integration/                  # 集成测试
│   ├── performance/                  # 性能测试
│   └── fixtures/                     # 测试数据
└── docs/                             # 文档
    ├── api/                          # API文档
    ├── architecture/                 # 架构文档
    └── deployment/                   # 部署文档
```

---

## 3. 系统设计

### 3.1 数据库设计

#### 3.1.1 SQLite关系型数据库设计

**核心表结构：**

```sql
-- 聊天会话表
CREATE TABLE chat_sessions (
    id TEXT PRIMARY KEY,                    -- 会话ID
    title TEXT NOT NULL,                    -- 会话标题
    model_id TEXT,                          -- 使用的模型ID
    system_prompt TEXT,                     -- 系统提示词
    temperature REAL DEFAULT 0.7,          -- 创造性参数
    max_tokens INTEGER DEFAULT 2048,       -- 最大token数
    is_archived BOOLEAN DEFAULT FALSE,     -- 是否归档
    is_pinned BOOLEAN DEFAULT FALSE,       -- 是否置顶
    message_count INTEGER DEFAULT 0,       -- 消息数量
    total_tokens INTEGER DEFAULT 0,        -- 总token消耗
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    metadata TEXT                           -- JSON格式的元数据
);

-- 聊天消息表
CREATE TABLE chat_messages (
    id TEXT PRIMARY KEY,                    -- 消息ID
    session_id TEXT NOT NULL,              -- 会话ID
    parent_id TEXT,                         -- 父消息ID（用于分支对话）
    role TEXT NOT NULL CHECK (role IN ('user', 'assistant', 'system')),
    content TEXT NOT NULL,                  -- 消息内容
    attachments TEXT,                       -- JSON格式的附件信息
    tokens_used INTEGER DEFAULT 0,         -- 使用的token数
    response_time REAL,                     -- 响应时间（秒）
    model_info TEXT,                        -- JSON格式的模型信息
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    status TEXT DEFAULT 'completed' CHECK (status IN ('pending', 'streaming', 'completed', 'failed')),
    error_message TEXT,                     -- 错误信息
    is_edited BOOLEAN DEFAULT FALSE,       -- 是否被编辑过
    edit_history TEXT,                      -- JSON格式的编辑历史
    FOREIGN KEY (session_id) REFERENCES chat_sessions(id) ON DELETE CASCADE
);

-- 知识库表
CREATE TABLE knowledge_bases (
    id TEXT PRIMARY KEY,                    -- 知识库ID
    name TEXT NOT NULL,                     -- 知识库名称
    description TEXT,                       -- 描述
    embedding_model TEXT NOT NULL,         -- 嵌入模型
    chunk_size INTEGER DEFAULT 512,        -- 分块大小
    chunk_overlap INTEGER DEFAULT 50,      -- 分块重叠
    document_count INTEGER DEFAULT 0,      -- 文档数量
    total_chunks INTEGER DEFAULT 0,        -- 总块数
    total_size INTEGER DEFAULT 0,          -- 总大小（字节）
    status TEXT DEFAULT 'active' CHECK (status IN ('active', 'processing', 'error', 'archived')),
    config TEXT,                            -- JSON格式的配置
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    last_indexed_at DATETIME               -- 最后索引时间
);

-- 文档表
CREATE TABLE documents (
    id TEXT PRIMARY KEY,                    -- 文档ID
    kb_id TEXT NOT NULL,                   -- 知识库ID
    name TEXT NOT NULL,                     -- 文档名称
    original_name TEXT NOT NULL,           -- 原始文件名
    file_type TEXT NOT NULL,               -- 文件类型
    mime_type TEXT,                         -- MIME类型
    file_size INTEGER NOT NULL,            -- 文件大小
    file_path TEXT NOT NULL,               -- 文件路径
    content_preview TEXT,                   -- 内容预览
    page_count INTEGER,                     -- 页数
    word_count INTEGER,                     -- 字数
    language TEXT,                          -- 文档语言
    status TEXT DEFAULT 'pending' CHECK (status IN ('pending', 'processing', 'completed', 'failed', 'archived')),
    processing_progress REAL DEFAULT 0.0,  -- 处理进度
    error_message TEXT,                     -- 错误信息
    chunks_count INTEGER DEFAULT 0,        -- 分块数量
    metadata TEXT,                          -- JSON格式的元数据
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    processed_at DATETIME,                  -- 处理完成时间
    FOREIGN KEY (kb_id) REFERENCES knowledge_bases(id) ON DELETE CASCADE
);

-- 文档块表
CREATE TABLE document_chunks (
    id TEXT PRIMARY KEY,                    -- 块ID
    document_id TEXT NOT NULL,             -- 文档ID
    chunk_index INTEGER NOT NULL,          -- 块索引
    content TEXT NOT NULL,                 -- 块内容
    token_count INTEGER NOT NULL,          -- Token数量
    page_number INTEGER,                    -- 页码
    section_title TEXT,                     -- 章节标题
    metadata TEXT,                          -- JSON格式的元数据
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (document_id) REFERENCES documents(id) ON DELETE CASCADE
);

-- 模型表
CREATE TABLE models (
    id TEXT PRIMARY KEY,                    -- 模型ID
    name TEXT NOT NULL,                     -- 模型名称
    display_name TEXT NOT NULL,            -- 显示名称
    description TEXT,                       -- 描述
    architecture TEXT NOT NULL,            -- 架构类型
    parameter_count TEXT,                   -- 参数数量
    context_length INTEGER,                -- 上下文长度
    file_size INTEGER,                      -- 文件大小
    file_path TEXT,                         -- 本地文件路径
    download_url TEXT,                      -- 下载URL
    model_type TEXT NOT NULL CHECK (model_type IN ('chat', 'embedding', 'multimodal')),
    engine_type TEXT NOT NULL CHECK (engine_type IN ('candle', 'llama_cpp', 'onnx')),
    quantization TEXT,                      -- 量化类型
    is_local BOOLEAN DEFAULT FALSE,        -- 是否本地模型
    is_downloaded BOOLEAN DEFAULT FALSE,   -- 是否已下载
    status TEXT DEFAULT 'available' CHECK (status IN ('available', 'downloading', 'loaded', 'error')),
    config TEXT,                            -- JSON格式的配置
    performance_metrics TEXT,               -- JSON格式的性能指标
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    last_used_at DATETIME                  -- 最后使用时间
);

-- 模型下载任务表
CREATE TABLE download_tasks (
    id TEXT PRIMARY KEY,                    -- 任务ID
    model_id TEXT NOT NULL,               -- 模型ID
    url TEXT NOT NULL,                      -- 下载URL
    file_path TEXT NOT NULL,               -- 目标文件路径
    total_size INTEGER,                     -- 总大小
    downloaded_size INTEGER DEFAULT 0,     -- 已下载大小
    progress REAL DEFAULT 0.0,             -- 下载进度
    speed INTEGER DEFAULT 0,               -- 下载速度（字节/秒）
    status TEXT DEFAULT 'pending' CHECK (status IN ('pending', 'downloading', 'paused', 'completed', 'failed', 'cancelled')),
    error_message TEXT,                     -- 错误信息
    started_at DATETIME,                    -- 开始时间
    completed_at DATETIME,                  -- 完成时间
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (model_id) REFERENCES models(id) ON DELETE CASCADE
);

-- 网络节点表
CREATE TABLE network_nodes (
    id TEXT PRIMARY KEY,                    -- 节点ID
    name TEXT NOT NULL,                     -- 节点名称
    device_type TEXT NOT NULL,             -- 设备类型
    ip_address TEXT NOT NULL,              -- IP地址
    port INTEGER NOT NULL,                 -- 端口
    public_key TEXT,                        -- 公钥
    capabilities TEXT,                      -- JSON格式的能力列表
    status TEXT DEFAULT 'offline' CHECK (status IN ('online', 'offline', 'connecting')),
    last_seen_at DATETIME,                 -- 最后在线时间
    trust_level INTEGER DEFAULT 0,         -- 信任级别
    metadata TEXT,                          -- JSON格式的元数据
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
);

-- 共享资源表
CREATE TABLE shared_resources (
    id TEXT PRIMARY KEY,                    -- 资源ID
    node_id TEXT NOT NULL,                 -- 节点ID
    resource_type TEXT NOT NULL CHECK (resource_type IN ('model', 'knowledge_base', 'file')),
    resource_id TEXT NOT NULL,             -- 资源ID
    name TEXT NOT NULL,                     -- 资源名称
    description TEXT,                       -- 描述
    size INTEGER,                           -- 大小
    checksum TEXT,                          -- 校验和
    permissions TEXT,                       -- JSON格式的权限
    is_public BOOLEAN DEFAULT FALSE,       -- 是否公开
    download_count INTEGER DEFAULT 0,      -- 下载次数
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (node_id) REFERENCES network_nodes(id) ON DELETE CASCADE
);

-- 文件传输任务表
CREATE TABLE transfer_tasks (
    id TEXT PRIMARY KEY,                    -- 任务ID
    source_node_id TEXT,                   -- 源节点ID
    target_node_id TEXT,                   -- 目标节点ID
    resource_id TEXT NOT NULL,             -- 资源ID
    transfer_type TEXT NOT NULL CHECK (transfer_type IN ('upload', 'download')),
    file_path TEXT NOT NULL,               -- 文件路径
    total_size INTEGER,                     -- 总大小
    transferred_size INTEGER DEFAULT 0,    -- 已传输大小
    progress REAL DEFAULT 0.0,             -- 传输进度
    speed INTEGER DEFAULT 0,               -- 传输速度
    status TEXT DEFAULT 'pending' CHECK (status IN ('pending', 'transferring', 'paused', 'completed', 'failed', 'cancelled')),
    error_message TEXT,                     -- 错误信息
    started_at DATETIME,                    -- 开始时间
    completed_at DATETIME,                  -- 完成时间
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP
);

-- 插件表
CREATE TABLE plugins (
    id TEXT PRIMARY KEY,                    -- 插件ID
    name TEXT NOT NULL,                     -- 插件名称
    display_name TEXT NOT NULL,            -- 显示名称
    description TEXT,                       -- 描述
    version TEXT NOT NULL,                 -- 版本
    author TEXT,                            -- 作者
    homepage TEXT,                          -- 主页
    repository TEXT,                        -- 仓库地址
    license TEXT,                           -- 许可证
    plugin_type TEXT NOT NULL CHECK (plugin_type IN ('chat', 'knowledge', 'model', 'ui', 'system')),
    runtime_type TEXT NOT NULL CHECK (runtime_type IN ('wasm', 'javascript', 'native')),
    file_path TEXT NOT NULL,               -- 插件文件路径
    entry_point TEXT NOT NULL,             -- 入口点
    permissions TEXT,                       -- JSON格式的权限
    config_schema TEXT,                     -- JSON格式的配置模式
    is_enabled BOOLEAN DEFAULT TRUE,       -- 是否启用
    is_system BOOLEAN DEFAULT FALSE,       -- 是否系统插件
    status TEXT DEFAULT 'installed' CHECK (status IN ('installed', 'loading', 'loaded', 'error', 'disabled')),
    error_message TEXT,                     -- 错误信息
    install_size INTEGER,                   -- 安装大小
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    last_used_at DATETIME                  -- 最后使用时间
);

-- 插件配置表
CREATE TABLE plugin_configs (
    id TEXT PRIMARY KEY,                    -- 配置ID
    plugin_id TEXT NOT NULL,              -- 插件ID
    config_key TEXT NOT NULL,             -- 配置键
    config_value TEXT,                      -- 配置值
    config_type TEXT NOT NULL CHECK (config_type IN ('string', 'number', 'boolean', 'object', 'array')),
    is_encrypted BOOLEAN DEFAULT FALSE,    -- 是否加密
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (plugin_id) REFERENCES plugins(id) ON DELETE CASCADE,
    UNIQUE(plugin_id, config_key)
);

-- 系统日志表
CREATE TABLE system_logs (
    id TEXT PRIMARY KEY,                    -- 日志ID
    timestamp DATETIME DEFAULT CURRENT_TIMESTAMP,
    level TEXT NOT NULL CHECK (level IN ('trace', 'debug', 'info', 'warn', 'error', 'fatal')),
    module TEXT NOT NULL,                  -- 模块名称
    message TEXT NOT NULL,                 -- 日志消息
    metadata TEXT,                          -- JSON格式的元数据
    request_id TEXT,                        -- 请求ID
    user_id TEXT,                          -- 用户ID
    session_id TEXT                         -- 会话ID
);

-- 性能指标表
CREATE TABLE performance_metrics (
    id TEXT PRIMARY KEY,                    -- 指标ID
    metric_name TEXT NOT NULL,             -- 指标名称
    metric_value REAL NOT NULL,            -- 指标值
    metric_unit TEXT,                       -- 单位
    tags TEXT,                              -- JSON格式的标签
    recorded_at DATETIME DEFAULT CURRENT_TIMESTAMP
);

-- 用户设置表
CREATE TABLE user_settings (
    id TEXT PRIMARY KEY,                    -- 设置ID
    category TEXT NOT NULL,                -- 设置分类
    setting_key TEXT NOT NULL,             -- 设置键
    setting_value TEXT,                     -- 设置值
    setting_type TEXT NOT NULL CHECK (setting_type IN ('string', 'number', 'boolean', 'object', 'array')),
    is_encrypted BOOLEAN DEFAULT FALSE,    -- 是否加密
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(category, setting_key)
);

-- 备份记录表
CREATE TABLE backup_records (
    id TEXT PRIMARY KEY,                    -- 备份ID
    backup_type TEXT NOT NULL CHECK (backup_type IN ('full', 'incremental', 'differential')),
    file_path TEXT NOT NULL,               -- 备份文件路径
    file_size INTEGER NOT NULL,            -- 备份文件大小
    checksum TEXT NOT NULL,                -- 校验和
    compression_type TEXT,                  -- 压缩类型
    encryption_enabled BOOLEAN DEFAULT FALSE, -- 是否加密
    status TEXT DEFAULT 'completed' CHECK (status IN ('pending', 'running', 'completed', 'failed')),
    error_message TEXT,                     -- 错误信息
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    completed_at DATETIME                   -- 完成时间
);
```

#### 3.1.2 ChromaDB向量数据库设计

**集合结构设计：**

```python
# 知识库向量集合
knowledge_collections = {
    "collection_name": "kb_{knowledge_base_id}",
    "metadata": {
        "kb_id": "知识库ID",
        "kb_name": "知识库名称",
        "embedding_model": "嵌入模型名称",
        "chunk_size": "分块大小",
        "created_at": "创建时间"
    },
    "documents": [
        {
            "id": "chunk_id",
            "document": "文档内容",
            "metadata": {
                "document_id": "文档ID",
                "document_name": "文档名称",
                "chunk_index": "块索引",
                "page_number": "页码",
                "section_title": "章节标题",
                "file_type": "文件类型",
                "created_at": "创建时间"
            },
            "embedding": [0.1, 0.2, ...]  # 向量嵌入
        }
    ]
}

# 聊天历史向量集合
chat_collections = {
    "collection_name": "chat_history",
    "metadata": {
        "description": "聊天历史向量化存储",
        "embedding_model": "嵌入模型名称"
    },
    "documents": [
        {
            "id": "message_id",
            "document": "消息内容",
            "metadata": {
                "session_id": "会话ID",
                "role": "角色",
                "timestamp": "时间戳",
                "model_id": "模型ID"
            },
            "embedding": [0.1, 0.2, ...]
        }
    ]
}
```

#### 3.1.3 数据库关系图

```
数据库关系图：

chat_sessions (1) ──────── (N) chat_messages
     │
     └── model_id ──────── models (1)

knowledge_bases (1) ──── (N) documents
     │                        │
     │                        └── (N) document_chunks
     │
     └── embedding_model ──── models (1)

network_nodes (1) ──────── (N) shared_resources
     │                        │
     │                        └── transfer_tasks (N)
     │
     └── (N) transfer_tasks

plugins (1) ────────────── (N) plugin_configs

models (1) ─────────────── (N) download_tasks

独立表：
- system_logs
- performance_metrics
- user_settings
- backup_records
```

### 3.2 界面设计规范

#### 3.2.1 设计系统

**颜色系统：**
```scss
// 主色调
$primary-colors: (
  50: #f0f9ff,
  100: #e0f2fe,
  200: #bae6fd,
  300: #7dd3fc,
  400: #38bdf8,
  500: #0ea5e9,  // 主色
  600: #0284c7,
  700: #0369a1,
  800: #075985,
  900: #0c4a6e
);

// 中性色
$neutral-colors: (
  50: #fafafa,
  100: #f5f5f5,
  200: #e5e5e5,
  300: #d4d4d4,
  400: #a3a3a3,
  500: #737373,
  600: #525252,
  700: #404040,
  800: #262626,
  900: #171717
);

// 语义色彩
$semantic-colors: (
  success: #10b981,
  warning: #f59e0b,
  error: #ef4444,
  info: #3b82f6
);

// 深色主题
$dark-theme: (
  background: #0f172a,
  surface: #1e293b,
  surface-variant: #334155,
  on-background: #f1f5f9,
  on-surface: #e2e8f0,
  primary: #38bdf8,
  secondary: #64748b
);

// 浅色主题
$light-theme: (
  background: #ffffff,
  surface: #f8fafc,
  surface-variant: #f1f5f9,
  on-background: #0f172a,
  on-surface: #334155,
  primary: #0ea5e9,
  secondary: #64748b
);
```

**字体系统：**
```scss
// 字体族
$font-families: (
  sans: ('Inter', 'SF Pro Display', 'Helvetica Neue', sans-serif),
  mono: ('JetBrains Mono', 'SF Mono', 'Consolas', monospace),
  chinese: ('PingFang SC', 'Microsoft YaHei', 'Hiragino Sans GB', sans-serif)
);

// 字体大小
$font-sizes: (
  xs: 0.75rem,    // 12px
  sm: 0.875rem,   // 14px
  base: 1rem,     // 16px
  lg: 1.125rem,   // 18px
  xl: 1.25rem,    // 20px
  2xl: 1.5rem,    // 24px
  3xl: 1.875rem,  // 30px
  4xl: 2.25rem,   // 36px
  5xl: 3rem       // 48px
);

// 行高
$line-heights: (
  tight: 1.25,
  normal: 1.5,
  relaxed: 1.75
);

// 字重
$font-weights: (
  light: 300,
  normal: 400,
  medium: 500,
  semibold: 600,
  bold: 700
);
```

**间距系统：**
```scss
// 间距比例 (基于 4px)
$spacing: (
  0: 0,
  1: 0.25rem,  // 4px
  2: 0.5rem,   // 8px
  3: 0.75rem,  // 12px
  4: 1rem,     // 16px
  5: 1.25rem,  // 20px
  6: 1.5rem,   // 24px
  8: 2rem,     // 32px
  10: 2.5rem,  // 40px
  12: 3rem,    // 48px
  16: 4rem,    // 64px
  20: 5rem,    // 80px
  24: 6rem     // 96px
);
```

**组件设计规范：**

```scss
// 按钮组件
.btn {
  // 基础样式
  @apply inline-flex items-center justify-center px-4 py-2 text-sm font-medium rounded-lg transition-all duration-200;

  // 尺寸变体
  &.btn-sm { @apply px-3 py-1.5 text-xs; }
  &.btn-lg { @apply px-6 py-3 text-base; }

  // 颜色变体
  &.btn-primary {
    @apply bg-primary-500 text-white hover:bg-primary-600 focus:ring-primary-500;
  }

  &.btn-secondary {
    @apply bg-neutral-200 text-neutral-900 hover:bg-neutral-300 focus:ring-neutral-500;
    .dark & { @apply bg-neutral-700 text-neutral-100 hover:bg-neutral-600; }
  }

  &.btn-outline {
    @apply border border-primary-500 text-primary-500 hover:bg-primary-50 focus:ring-primary-500;
    .dark & { @apply hover:bg-primary-900/20; }
  }

  // 状态
  &:disabled {
    @apply opacity-50 cursor-not-allowed;
  }
}

// 输入框组件
.input {
  @apply w-full px-3 py-2 text-sm border border-neutral-300 rounded-lg;
  @apply focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent;
  @apply placeholder-neutral-400 transition-all duration-200;

  .dark & {
    @apply bg-neutral-800 border-neutral-600 text-neutral-100;
    @apply placeholder-neutral-500;
  }

  &.input-error {
    @apply border-red-500 focus:ring-red-500;
  }
}

// 卡片组件
.card {
  @apply bg-white rounded-xl shadow-sm border border-neutral-200 p-6;

  .dark & {
    @apply bg-neutral-800 border-neutral-700;
  }

  &.card-hover {
    @apply hover:shadow-md transition-shadow duration-200;
  }
}

// 模态框组件
.modal {
  @apply fixed inset-0 z-50 flex items-center justify-center p-4;

  &-backdrop {
    @apply absolute inset-0 bg-black/50 backdrop-blur-sm;
  }

  &-content {
    @apply relative bg-white rounded-xl shadow-xl max-w-md w-full max-h-[90vh] overflow-auto;

    .dark & {
      @apply bg-neutral-800;
    }
  }
}
```

#### 3.2.2 布局规范

**网格系统：**
```scss
// 12列网格系统
.container {
  @apply max-w-7xl mx-auto px-4 sm:px-6 lg:px-8;
}

.grid {
  @apply grid grid-cols-12 gap-6;
}

.col-1 { @apply col-span-1; }
.col-2 { @apply col-span-2; }
.col-3 { @apply col-span-3; }
.col-4 { @apply col-span-4; }
.col-6 { @apply col-span-6; }
.col-8 { @apply col-span-8; }
.col-9 { @apply col-span-9; }
.col-12 { @apply col-span-12; }

// 响应式断点
@screen sm {
  .sm\:col-6 { @apply col-span-6; }
  .sm\:col-12 { @apply col-span-12; }
}

@screen md {
  .md\:col-4 { @apply col-span-4; }
  .md\:col-8 { @apply col-span-8; }
}

@screen lg {
  .lg\:col-3 { @apply col-span-3; }
  .lg\:col-9 { @apply col-span-9; }
}
```

**页面布局：**
```vue
<!-- 主布局模板 -->
<template>
  <div class="app-layout">
    <!-- 侧边栏 -->
    <aside class="sidebar">
      <nav class="sidebar-nav">
        <!-- 导航菜单 -->
      </nav>
    </aside>

    <!-- 主内容区 -->
    <main class="main-content">
      <!-- 顶部栏 -->
      <header class="header">
        <!-- 标题和操作按钮 -->
      </header>

      <!-- 内容区域 -->
      <div class="content">
        <router-view />
      </div>
    </main>
  </div>
</template>

<style scoped>
.app-layout {
  @apply flex h-screen bg-neutral-50;

  .dark & {
    @apply bg-neutral-900;
  }
}

.sidebar {
  @apply w-64 bg-white border-r border-neutral-200 flex-shrink-0;

  .dark & {
    @apply bg-neutral-800 border-neutral-700;
  }
}

.main-content {
  @apply flex-1 flex flex-col overflow-hidden;
}

.header {
  @apply h-16 bg-white border-b border-neutral-200 flex items-center justify-between px-6;

  .dark & {
    @apply bg-neutral-800 border-neutral-700;
  }
}

.content {
  @apply flex-1 overflow-auto p-6;
}
</style>
```

### 3.3 系统流程图

#### 3.3.1 聊天流程图

```
用户聊天流程：

用户输入消息
    ↓
验证输入内容
    ↓
保存用户消息到数据库
    ↓
检查是否需要知识库检索
    ↓
[需要检索] → 执行语义搜索 → 获取相关文档
    ↓                           ↓
[不需要检索] ←─────────────────────┘
    ↓
构建完整提示词
    ↓
选择合适的AI模型
    ↓
检查模型是否已加载
    ↓
[未加载] → 加载模型到内存
    ↓              ↓
[已加载] ←─────────┘
    ↓
执行AI推理
    ↓
[流式响应] → 实时返回token → 前端实时显示
    ↓                           ↓
[完整响应] ←─────────────────────┘
    ↓
保存AI响应到数据库
    ↓
更新会话统计信息
    ↓
返回完成状态
```

#### 3.3.2 知识库处理流程图

```
文档上传处理流程：

用户上传文档
    ↓
文件安全检查
    ↓
[检查失败] → 返回错误信息
    ↓
[检查通过]
    ↓
保存文档到文件系统
    ↓
创建文档记录
    ↓
文档内容提取
    ↓
[提取失败] → 标记处理失败
    ↓
[提取成功]
    ↓
文本预处理
    ↓
文档分块处理
    ↓
批量向量化
    ↓
保存向量到ChromaDB
    ↓
更新文档状态
    ↓
更新知识库统计
    ↓
处理完成
```

#### 3.3.3 模型管理流程图

```
模型下载安装流程：

用户选择模型
    ↓
检查本地是否存在
    ↓
[已存在] → 直接加载使用
    ↓
[不存在]
    ↓
创建下载任务
    ↓
开始下载模型文件
    ↓
[下载中] → 更新进度 → 前端显示进度
    ↓                    ↓
[下载完成] ←─────────────┘
    ↓
验证文件完整性
    ↓
[验证失败] → 删除文件 → 标记失败
    ↓
[验证成功]
    ↓
更新模型状态
    ↓
可选：预加载到内存
    ↓
安装完成
```

---

## 4. 实现细节

### 4.1 核心代码实现逻辑

#### 4.1.1 聊天服务核心实现

```rust
// 聊天服务主要结构
use tokio::sync::{RwLock, mpsc};
use std::sync::Arc;
use uuid::Uuid;

pub struct ChatService {
    db_pool: SqlitePool,
    ai_engine: Arc<dyn InferenceEngine>,
    knowledge_service: Arc<KnowledgeService>,
    session_manager: Arc<RwLock<SessionManager>>,
    message_cache: Arc<RwLock<LruCache<String, ChatMessage>>>,
}

impl ChatService {
    pub fn new(
        db_pool: SqlitePool,
        ai_engine: Arc<dyn InferenceEngine>,
        knowledge_service: Arc<KnowledgeService>,
    ) -> Self {
        Self {
            db_pool,
            ai_engine,
            knowledge_service,
            session_manager: Arc::new(RwLock::new(SessionManager::new())),
            message_cache: Arc::new(RwLock::new(LruCache::new(1000))),
        }
    }

    // 创建新的聊天会话
    pub async fn create_session(
        &self,
        title: Option<String>,
        model_id: Option<String>,
    ) -> Result<ChatSession, ChatError> {
        let session = ChatSession {
            id: Uuid::new_v4().to_string(),
            title: title.unwrap_or_else(|| "新对话".to_string()),
            model_id: model_id.unwrap_or_else(|| "default".to_string()),
            system_prompt: None,
            temperature: 0.7,
            max_tokens: 2048,
            is_archived: false,
            is_pinned: false,
            message_count: 0,
            total_tokens: 0,
            created_at: chrono::Utc::now(),
            updated_at: chrono::Utc::now(),
            metadata: serde_json::Value::Null,
        };

        // 保存到数据库
        sqlx::query!(
            "INSERT INTO chat_sessions (id, title, model_id, system_prompt, temperature, max_tokens, created_at, updated_at)
             VALUES (?, ?, ?, ?, ?, ?, ?, ?)",
            session.id,
            session.title,
            session.model_id,
            session.system_prompt,
            session.temperature,
            session.max_tokens,
            session.created_at,
            session.updated_at
        )
        .execute(&self.db_pool)
        .await?;

        // 添加到会话管理器
        let mut manager = self.session_manager.write().await;
        manager.add_session(session.clone());

        Ok(session)
    }

    // 发送消息
    pub async fn send_message(
        &self,
        request: SendMessageRequest,
    ) -> Result<String, ChatError> {
        // 验证会话存在
        let session = self.get_session(&request.session_id).await?;

        // 创建用户消息
        let user_message = ChatMessage {
            id: Uuid::new_v4().to_string(),
            session_id: request.session_id.clone(),
            parent_id: None,
            role: "user".to_string(),
            content: request.message.clone(),
            attachments: request.attachments.map(|a| serde_json::to_string(&a).unwrap()),
            tokens_used: 0,
            response_time: None,
            model_info: None,
            created_at: chrono::Utc::now(),
            status: "completed".to_string(),
            error_message: None,
            is_edited: false,
            edit_history: None,
        };

        // 保存用户消息
        self.save_message(&user_message).await?;

        // 检查是否需要知识库检索
        let context_documents = if let Some(kb_config) = &request.knowledge_config {
            self.knowledge_service
                .search(&kb_config.kb_id, &request.message, Some(5), Some(0.7), None)
                .await
                .unwrap_or_default()
        } else {
            Vec::new()
        };

        // 构建推理请求
        let inference_request = self.build_inference_request(
            &session,
            &request,
            &context_documents,
        ).await?;

        // 执行AI推理
        let start_time = std::time::Instant::now();
        let response = self.ai_engine.inference(inference_request).await?;
        let response_time = start_time.elapsed().as_secs_f64();

        // 创建AI响应消息
        let ai_message = ChatMessage {
            id: Uuid::new_v4().to_string(),
            session_id: request.session_id.clone(),
            parent_id: Some(user_message.id.clone()),
            role: "assistant".to_string(),
            content: response.content,
            attachments: None,
            tokens_used: response.usage.total_tokens as i32,
            response_time: Some(response_time),
            model_info: Some(serde_json::to_string(&response.model_info).unwrap()),
            created_at: chrono::Utc::now(),
            status: "completed".to_string(),
            error_message: None,
            is_edited: false,
            edit_history: None,
        };

        // 保存AI消息
        self.save_message(&ai_message).await?;

        // 更新会话统计
        self.update_session_stats(&request.session_id, 2, response.usage.total_tokens as i32).await?;

        Ok(ai_message.id)
    }

    // 流式响应处理
    pub async fn send_message_stream(
        &self,
        request: SendMessageRequest,
    ) -> Result<mpsc::Receiver<StreamResponse>, ChatError> {
        let (tx, rx) = mpsc::channel(100);

        // 异步处理流式响应
        let service = self.clone();
        tokio::spawn(async move {
            if let Err(e) = service.handle_stream_message(request, tx).await {
                tracing::error!("Stream message error: {}", e);
            }
        });

        Ok(rx)
    }

    async fn handle_stream_message(
        &self,
        request: SendMessageRequest,
        tx: mpsc::Sender<StreamResponse>,
    ) -> Result<(), ChatError> {
        // 保存用户消息
        let user_message = self.create_user_message(&request).await?;
        self.save_message(&user_message).await?;

        // 发送用户消息确认
        let _ = tx.send(StreamResponse::UserMessage(user_message)).await;

        // 构建推理请求
        let session = self.get_session(&request.session_id).await?;
        let inference_request = self.build_inference_request(&session, &request, &[]).await?;

        // 开始流式推理
        let mut stream = self.ai_engine.inference_stream(inference_request).await?;
        let mut accumulated_content = String::new();
        let ai_message_id = Uuid::new_v4().to_string();

        // 发送AI消息开始事件
        let _ = tx.send(StreamResponse::MessageStart {
            message_id: ai_message_id.clone(),
            session_id: request.session_id.clone(),
        }).await;

        // 处理流式token
        while let Some(token) = stream.recv().await {
            accumulated_content.push_str(&token.content);

            let _ = tx.send(StreamResponse::Token {
                message_id: ai_message_id.clone(),
                content: token.content,
                is_final: token.is_final,
            }).await;

            if token.is_final {
                break;
            }
        }

        // 保存完整的AI消息
        let ai_message = ChatMessage {
            id: ai_message_id.clone(),
            session_id: request.session_id.clone(),
            parent_id: Some(user_message.id),
            role: "assistant".to_string(),
            content: accumulated_content,
            // ... 其他字段
        };

        self.save_message(&ai_message).await?;

        // 发送完成事件
        let _ = tx.send(StreamResponse::MessageComplete {
            message_id: ai_message_id,
            total_tokens: 0, // 实际计算
        }).await;

        Ok(())
    }

    // 获取会话消息
    pub async fn get_messages(
        &self,
        session_id: &str,
        limit: i32,
        offset: i32,
    ) -> Result<Vec<ChatMessage>, ChatError> {
        let messages = sqlx::query_as!(
            ChatMessage,
            "SELECT * FROM chat_messages
             WHERE session_id = ?
             ORDER BY created_at ASC
             LIMIT ? OFFSET ?",
            session_id, limit, offset
        )
        .fetch_all(&self.db_pool)
        .await?;

        Ok(messages)
    }

    // 私有辅助方法
    async fn save_message(&self, message: &ChatMessage) -> Result<(), ChatError> {
        sqlx::query!(
            "INSERT INTO chat_messages (id, session_id, parent_id, role, content, attachments, tokens_used, response_time, model_info, created_at, status)
             VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)",
            message.id,
            message.session_id,
            message.parent_id,
            message.role,
            message.content,
            message.attachments,
            message.tokens_used,
            message.response_time,
            message.model_info,
            message.created_at,
            message.status
        )
        .execute(&self.db_pool)
        .await?;

        // 添加到缓存
        let mut cache = self.message_cache.write().await;
        cache.put(message.id.clone(), message.clone());

        Ok(())
    }

    async fn build_inference_request(
        &self,
        session: &ChatSession,
        request: &SendMessageRequest,
        context_docs: &[SearchResult],
    ) -> Result<InferenceRequest, ChatError> {
        // 获取历史消息
        let history = self.get_messages(&session.id, 10, 0).await?;

        // 构建系统提示词
        let mut system_prompt = session.system_prompt.clone()
            .unwrap_or_else(|| "你是一个有用的AI助手。".to_string());

        // 添加知识库上下文
        if !context_docs.is_empty() {
            system_prompt.push_str("\n\n相关文档内容：\n");
            for doc in context_docs {
                system_prompt.push_str(&format!("- {}\n", doc.content));
            }
        }

        // 构建对话上下文
        let mut context = vec![ChatMessage {
            role: "system".to_string(),
            content: system_prompt,
            ..Default::default()
        }];
        context.extend(history);

        Ok(InferenceRequest {
            model_id: session.model_id.clone(),
            prompt: request.message.clone(),
            system_prompt: Some(system_prompt),
            max_tokens: Some(session.max_tokens as u32),
            temperature: Some(session.temperature),
            top_p: None,
            stop_sequences: None,
            stream: false,
            context: Some(context),
        })
    }
}

// 流式响应类型
#[derive(Debug, Clone, Serialize)]
#[serde(tag = "type")]
pub enum StreamResponse {
    UserMessage(ChatMessage),
    MessageStart {
        message_id: String,
        session_id: String,
    },
    Token {
        message_id: String,
        content: String,
        is_final: bool,
    },
    MessageComplete {
        message_id: String,
        total_tokens: u32,
    },
    Error {
        message: String,
    },
}
```

### 4.2 API接口设计规范

#### 4.2.1 RESTful API设计

**基础规范：**
```yaml
# API基础信息
openapi: 3.0.3
info:
  title: AI Studio API
  description: AI Studio 桌面应用后端API
  version: 2.0.0
  contact:
    name: AI Studio Team
    email: <EMAIL>

servers:
  - url: http://localhost:8080/api/v1
    description: 本地开发服务器

# 通用响应格式
components:
  schemas:
    ApiResponse:
      type: object
      properties:
        code:
          type: integer
          description: 响应状态码
          example: 200
        message:
          type: string
          description: 响应消息
          example: "操作成功"
        data:
          type: object
          description: 响应数据
        timestamp:
          type: string
          format: date-time
          description: 响应时间
        request_id:
          type: string
          description: 请求ID
          example: "req_123456789"

    PaginatedResponse:
      allOf:
        - $ref: '#/components/schemas/ApiResponse'
        - type: object
          properties:
            data:
              type: object
              properties:
                items:
                  type: array
                  description: 数据项列表
                total:
                  type: integer
                  description: 总数量
                page:
                  type: integer
                  description: 当前页码
                limit:
                  type: integer
                  description: 每页数量
                has_more:
                  type: boolean
                  description: 是否有更多数据

    ErrorResponse:
      type: object
      properties:
        code:
          type: integer
          description: 错误码
        message:
          type: string
          description: 错误消息
        details:
          type: object
          description: 错误详情
        timestamp:
          type: string
          format: date-time
        request_id:
          type: string
```

**聊天API接口：**
```yaml
# 聊天会话管理
paths:
  /chat/sessions:
    get:
      tags:
        - Chat
      summary: 获取聊天会话列表
      parameters:
        - name: page
          in: query
          description: 页码
          schema:
            type: integer
            default: 1
        - name: limit
          in: query
          description: 每页数量
          schema:
            type: integer
            default: 20
            maximum: 100
        - name: search
          in: query
          description: 搜索关键词
          schema:
            type: string
        - name: is_archived
          in: query
          description: 是否归档
          schema:
            type: boolean
      responses:
        '200':
          description: 成功获取会话列表
          content:
            application/json:
              schema:
                allOf:
                  - $ref: '#/components/schemas/PaginatedResponse'
                  - type: object
                    properties:
                      data:
                        type: object
                        properties:
                          items:
                            type: array
                            items:
                              $ref: '#/components/schemas/ChatSession'

    post:
      tags:
        - Chat
      summary: 创建新的聊天会话
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                title:
                  type: string
                  description: 会话标题
                  example: "新对话"
                model_id:
                  type: string
                  description: 模型ID
                  example: "llama2-7b"
                system_prompt:
                  type: string
                  description: 系统提示词
                temperature:
                  type: number
                  description: 创造性参数
                  minimum: 0
                  maximum: 2
                  default: 0.7
                max_tokens:
                  type: integer
                  description: 最大token数
                  minimum: 1
                  maximum: 8192
                  default: 2048
      responses:
        '201':
          description: 成功创建会话
          content:
            application/json:
              schema:
                allOf:
                  - $ref: '#/components/schemas/ApiResponse'
                  - type: object
                    properties:
                      data:
                        $ref: '#/components/schemas/ChatSession'

  /chat/sessions/{sessionId}:
    get:
      tags:
        - Chat
      summary: 获取会话详情
      parameters:
        - name: sessionId
          in: path
          required: true
          schema:
            type: string
      responses:
        '200':
          description: 成功获取会话详情
          content:
            application/json:
              schema:
                allOf:
                  - $ref: '#/components/schemas/ApiResponse'
                  - type: object
                    properties:
                      data:
                        $ref: '#/components/schemas/ChatSession'

    put:
      tags:
        - Chat
      summary: 更新会话信息
      parameters:
        - name: sessionId
          in: path
          required: true
          schema:
            type: string
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                title:
                  type: string
                system_prompt:
                  type: string
                temperature:
                  type: number
                max_tokens:
                  type: integer
                is_pinned:
                  type: boolean
      responses:
        '200':
          description: 成功更新会话
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ApiResponse'

    delete:
      tags:
        - Chat
      summary: 删除会话
      parameters:
        - name: sessionId
          in: path
          required: true
          schema:
            type: string
      responses:
        '200':
          description: 成功删除会话
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ApiResponse'

  /chat/sessions/{sessionId}/messages:
    get:
      tags:
        - Chat
      summary: 获取会话消息列表
      parameters:
        - name: sessionId
          in: path
          required: true
          schema:
            type: string
        - name: limit
          in: query
          description: 消息数量限制
          schema:
            type: integer
            default: 50
            maximum: 200
        - name: before
          in: query
          description: 获取指定消息之前的消息
          schema:
            type: string
        - name: after
          in: query
          description: 获取指定消息之后的消息
          schema:
            type: string
      responses:
        '200':
          description: 成功获取消息列表
          content:
            application/json:
              schema:
                allOf:
                  - $ref: '#/components/schemas/ApiResponse'
                  - type: object
                    properties:
                      data:
                        type: object
                        properties:
                          messages:
                            type: array
                            items:
                              $ref: '#/components/schemas/ChatMessage'
                          has_more:
                            type: boolean

    post:
      tags:
        - Chat
      summary: 发送消息
      parameters:
        - name: sessionId
          in: path
          required: true
          schema:
            type: string
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required:
                - message
              properties:
                message:
                  type: string
                  description: 消息内容
                  example: "你好，请介绍一下人工智能"
                attachments:
                  type: array
                  description: 附件列表
                  items:
                    type: object
                    properties:
                      type:
                        type: string
                        enum: [image, document, audio]
                      url:
                        type: string
                      name:
                        type: string
                      size:
                        type: integer
                stream:
                  type: boolean
                  description: 是否使用流式响应
                  default: false
                knowledge_config:
                  type: object
                  description: 知识库配置
                  properties:
                    kb_id:
                      type: string
                      description: 知识库ID
                    search_limit:
                      type: integer
                      description: 搜索结果数量
                      default: 5
                    similarity_threshold:
                      type: number
                      description: 相似度阈值
                      default: 0.7
                model_config:
                  type: object
                  description: 模型配置覆盖
                  properties:
                    temperature:
                      type: number
                    max_tokens:
                      type: integer
                    top_p:
                      type: number
      responses:
        '200':
          description: 成功发送消息（非流式）
          content:
            application/json:
              schema:
                allOf:
                  - $ref: '#/components/schemas/ApiResponse'
                  - type: object
                    properties:
                      data:
                        type: object
                        properties:
                          message_id:
                            type: string
                            description: 消息ID
                          response:
                            $ref: '#/components/schemas/ChatMessage'
        '202':
          description: 开始流式响应
          content:
            text/event-stream:
              schema:
                type: string
                description: Server-Sent Events流
```

### 4.3 工具文件说明

#### 4.3.1 前端工具函数

**API调用封装 (utils/api.ts)：**
```typescript
import { invoke } from '@tauri-apps/api/tauri'

// API响应类型
interface ApiResponse<T = any> {
  code: number
  message: string
  data: T
  timestamp: string
  request_id: string
}

// API错误类型
class ApiError extends Error {
  constructor(
    public code: number,
    message: string,
    public details?: any
  ) {
    super(message)
    this.name = 'ApiError'
  }
}

// 基础API客户端
class ApiClient {
  private baseURL = '/api/v1'
  private timeout = 30000

  // 通用请求方法
  async request<T>(
    command: string,
    payload?: any,
    options?: {
      timeout?: number
      retries?: number
    }
  ): Promise<T> {
    const requestId = this.generateRequestId()

    try {
      const response = await invoke<ApiResponse<T>>(command, {
        payload: {
          ...payload,
          request_id: requestId
        }
      })

      if (response.code !== 200) {
        throw new ApiError(response.code, response.message)
      }

      return response.data
    } catch (error) {
      if (error instanceof ApiError) {
        throw error
      }

      // 处理Tauri调用错误
      throw new ApiError(500, '网络请求失败', error)
    }
  }

  // GET请求
  async get<T>(endpoint: string, params?: Record<string, any>): Promise<T> {
    return this.request<T>('api_get', { endpoint, params })
  }

  // POST请求
  async post<T>(endpoint: string, data?: any): Promise<T> {
    return this.request<T>('api_post', { endpoint, data })
  }

  // PUT请求
  async put<T>(endpoint: string, data?: any): Promise<T> {
    return this.request<T>('api_put', { endpoint, data })
  }

  // DELETE请求
  async delete<T>(endpoint: string): Promise<T> {
    return this.request<T>('api_delete', { endpoint })
  }

  // 流式请求
  async stream(
    endpoint: string,
    data: any,
    onMessage: (data: any) => void,
    onError?: (error: Error) => void,
    onComplete?: () => void
  ): Promise<void> {
    try {
      await invoke('api_stream', {
        endpoint,
        data,
        callback: onMessage
      })
      onComplete?.()
    } catch (error) {
      onError?.(error as Error)
    }
  }

  private generateRequestId(): string {
    return `req_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
  }
}

// 导出API实例
export const api = new ApiClient()

// 聊天API
export const chatAPI = {
  // 获取会话列表
  getSessions: (params?: {
    page?: number
    limit?: number
    search?: string
    is_archived?: boolean
  }) => api.get<PaginatedResponse<ChatSession>>('/chat/sessions', params),

  // 创建会话
  createSession: (data: {
    title?: string
    model_id?: string
    system_prompt?: string
    temperature?: number
    max_tokens?: number
  }) => api.post<ChatSession>('/chat/sessions', data),

  // 获取会话详情
  getSession: (sessionId: string) =>
    api.get<ChatSession>(`/chat/sessions/${sessionId}`),

  // 更新会话
  updateSession: (sessionId: string, data: Partial<ChatSession>) =>
    api.put<void>(`/chat/sessions/${sessionId}`, data),

  // 删除会话
  deleteSession: (sessionId: string) =>
    api.delete<void>(`/chat/sessions/${sessionId}`),

  // 获取消息列表
  getMessages: (sessionId: string, params?: {
    limit?: number
    before?: string
    after?: string
  }) => api.get<{ messages: ChatMessage[], has_more: boolean }>(
    `/chat/sessions/${sessionId}/messages`,
    params
  ),

  // 发送消息
  sendMessage: (sessionId: string, data: {
    message: string
    attachments?: Attachment[]
    stream?: boolean
    knowledge_config?: KnowledgeConfig
    model_config?: ModelConfig
  }) => api.post<{ message_id: string, response: ChatMessage }>(
    `/chat/sessions/${sessionId}/messages`,
    data
  ),

  // 流式发送消息
  sendMessageStream: (
    sessionId: string,
    data: {
      message: string
      attachments?: Attachment[]
      knowledge_config?: KnowledgeConfig
      model_config?: ModelConfig
    },
    onMessage: (response: StreamResponse) => void,
    onError?: (error: Error) => void,
    onComplete?: () => void
  ) => api.stream(
    `/chat/sessions/${sessionId}/messages`,
    { ...data, stream: true },
    onMessage,
    onError,
    onComplete
  )
}

// 知识库API
export const knowledgeAPI = {
  // 获取知识库列表
  getKnowledgeBases: (params?: {
    page?: number
    limit?: number
    search?: string
    status?: string
  }) => api.get<PaginatedResponse<KnowledgeBase>>('/knowledge', params),

  // 创建知识库
  createKnowledgeBase: (data: {
    name: string
    description?: string
    embedding_model?: string
    chunk_size?: number
    chunk_overlap?: number
  }) => api.post<KnowledgeBase>('/knowledge', data),

  // 上传文档
  uploadDocument: (kbId: string, file: File, options?: {
    auto_process?: boolean
  }) => {
    const formData = new FormData()
    formData.append('file', file)
    if (options?.auto_process !== undefined) {
      formData.append('auto_process', String(options.auto_process))
    }
    return api.post<Document>(`/knowledge/${kbId}/documents`, formData)
  },

  // 搜索知识库
  search: (kbId: string, data: {
    query: string
    limit?: number
    threshold?: number
    filters?: SearchFilters
    search_type?: 'semantic' | 'hybrid' | 'keyword'
  }) => api.post<{ results: SearchResult[], total: number }>(
    `/knowledge/${kbId}/search`,
    data
  )
}

// 模型API
export const modelAPI = {
  // 获取模型列表
  getModels: (params?: {
    page?: number
    limit?: number
    model_type?: string
    is_local?: boolean
    status?: string
  }) => api.get<PaginatedResponse<Model>>('/models', params),

  // 下载模型
  downloadModel: (modelId: string) =>
    api.post<{ task_id: string }>(`/models/${modelId}/download`),

  // 获取下载进度
  getDownloadProgress: (taskId: string) =>
    api.get<DownloadTask>(`/models/download/${taskId}`),

  // 加载模型
  loadModel: (modelId: string) =>
    api.post<void>(`/models/${modelId}/load`),

  // 卸载模型
  unloadModel: (modelId: string) =>
    api.post<void>(`/models/${modelId}/unload`)
}
```

**格式化工具 (utils/formatters.ts)：**
```typescript
// 时间格式化
export const formatTime = (timestamp: string | Date): string => {
  const date = typeof timestamp === 'string' ? new Date(timestamp) : timestamp
  const now = new Date()
  const diff = now.getTime() - date.getTime()

  // 小于1分钟
  if (diff < 60 * 1000) {
    return '刚刚'
  }

  // 小于1小时
  if (diff < 60 * 60 * 1000) {
    const minutes = Math.floor(diff / (60 * 1000))
    return `${minutes}分钟前`
  }

  // 小于1天
  if (diff < 24 * 60 * 60 * 1000) {
    const hours = Math.floor(diff / (60 * 60 * 1000))
    return `${hours}小时前`
  }

  // 小于7天
  if (diff < 7 * 24 * 60 * 60 * 1000) {
    const days = Math.floor(diff / (24 * 60 * 60 * 1000))
    return `${days}天前`
  }

  // 超过7天显示具体日期
  return date.toLocaleDateString('zh-CN', {
    year: 'numeric',
    month: 'short',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit'
  })
}

// 文件大小格式化
export const formatFileSize = (bytes: number): string => {
  if (bytes === 0) return '0 B'

  const k = 1024
  const sizes = ['B', 'KB', 'MB', 'GB', 'TB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))

  return `${parseFloat((bytes / Math.pow(k, i)).toFixed(1))} ${sizes[i]}`
}

// 数字格式化
export const formatNumber = (num: number): string => {
  if (num >= 1000000) {
    return `${(num / 1000000).toFixed(1)}M`
  }
  if (num >= 1000) {
    return `${(num / 1000).toFixed(1)}K`
  }
  return num.toString()
}

// Token数量格式化
export const formatTokens = (tokens: number): string => {
  return formatNumber(tokens) + ' tokens'
}

// 百分比格式化
export const formatPercentage = (value: number, total: number): string => {
  if (total === 0) return '0%'
  return `${Math.round((value / total) * 100)}%`
}

// 持续时间格式化
export const formatDuration = (seconds: number): string => {
  if (seconds < 60) {
    return `${seconds.toFixed(1)}秒`
  }

  const minutes = Math.floor(seconds / 60)
  const remainingSeconds = seconds % 60

  if (minutes < 60) {
    return `${minutes}分${remainingSeconds.toFixed(0)}秒`
  }

  const hours = Math.floor(minutes / 60)
  const remainingMinutes = minutes % 60

  return `${hours}小时${remainingMinutes}分`
}

// 速度格式化
export const formatSpeed = (bytesPerSecond: number): string => {
  return `${formatFileSize(bytesPerSecond)}/s`
}
```

---

## 5. 质量保障

### 5.1 错误处理机制

#### 5.1.1 分层错误处理架构

```rust
// 错误类型定义
use thiserror::Error;
use serde::{Serialize, Deserialize};

#[derive(Error, Debug, Serialize, Deserialize)]
pub enum AppError {
    // 数据库错误
    #[error("数据库错误: {message}")]
    Database { message: String, source: Option<String> },

    // AI推理错误
    #[error("AI推理错误: {message}")]
    Inference { message: String, model_id: String },

    // 网络错误
    #[error("网络错误: {message}")]
    Network { message: String, endpoint: Option<String> },

    // 文件系统错误
    #[error("文件系统错误: {message}")]
    FileSystem { message: String, path: Option<String> },

    // 验证错误
    #[error("验证错误: {field}: {message}")]
    Validation { field: String, message: String },

    // 权限错误
    #[error("权限不足: {action}")]
    Permission { action: String },

    // 配置错误
    #[error("配置错误: {message}")]
    Configuration { message: String },

    // 插件错误
    #[error("插件错误: {plugin_id}: {message}")]
    Plugin { plugin_id: String, message: String },

    // 通用错误
    #[error("内部错误: {message}")]
    Internal { message: String },
}

impl AppError {
    // 获取错误码
    pub fn error_code(&self) -> u32 {
        match self {
            AppError::Database { .. } => 1001,
            AppError::Inference { .. } => 1002,
            AppError::Network { .. } => 1003,
            AppError::FileSystem { .. } => 1004,
            AppError::Validation { .. } => 1005,
            AppError::Permission { .. } => 1006,
            AppError::Configuration { .. } => 1007,
            AppError::Plugin { .. } => 1008,
            AppError::Internal { .. } => 1999,
        }
    }

    // 获取错误级别
    pub fn severity(&self) -> ErrorSeverity {
        match self {
            AppError::Validation { .. } => ErrorSeverity::Warning,
            AppError::Permission { .. } => ErrorSeverity::Warning,
            AppError::Network { .. } => ErrorSeverity::Error,
            AppError::Database { .. } => ErrorSeverity::Critical,
            AppError::Internal { .. } => ErrorSeverity::Critical,
            _ => ErrorSeverity::Error,
        }
    }

    // 是否可重试
    pub fn is_retryable(&self) -> bool {
        matches!(self,
            AppError::Network { .. } |
            AppError::Database { .. } |
            AppError::Inference { .. }
        )
    }

    // 获取用户友好的错误消息
    pub fn user_message(&self) -> String {
        match self {
            AppError::Database { .. } => "数据保存失败，请稍后重试".to_string(),
            AppError::Inference { .. } => "AI处理失败，请检查模型状态".to_string(),
            AppError::Network { .. } => "网络连接失败，请检查网络设置".to_string(),
            AppError::FileSystem { .. } => "文件操作失败，请检查文件权限".to_string(),
            AppError::Validation { message, .. } => message.clone(),
            AppError::Permission { .. } => "权限不足，无法执行此操作".to_string(),
            AppError::Configuration { .. } => "配置错误，请检查设置".to_string(),
            AppError::Plugin { .. } => "插件执行失败".to_string(),
            AppError::Internal { .. } => "系统内部错误，请联系技术支持".to_string(),
        }
    }
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum ErrorSeverity {
    Info,
    Warning,
    Error,
    Critical,
}

// 错误处理中间件
pub struct ErrorHandler {
    logger: Arc<Logger>,
    metrics: Arc<MetricsCollector>,
}

impl ErrorHandler {
    pub fn new(logger: Arc<Logger>, metrics: Arc<MetricsCollector>) -> Self {
        Self { logger, metrics }
    }

    // 处理错误
    pub async fn handle_error(&self, error: &AppError, context: ErrorContext) -> ErrorResponse {
        // 记录错误日志
        self.log_error(error, &context).await;

        // 收集错误指标
        self.collect_metrics(error).await;

        // 发送告警（如果需要）
        if error.severity() == ErrorSeverity::Critical {
            self.send_alert(error, &context).await;
        }

        // 构建响应
        ErrorResponse {
            code: error.error_code(),
            message: error.user_message(),
            details: self.build_error_details(error, &context),
            severity: error.severity(),
            retryable: error.is_retryable(),
            timestamp: chrono::Utc::now(),
            request_id: context.request_id,
        }
    }

    async fn log_error(&self, error: &AppError, context: &ErrorContext) {
        let log_entry = ErrorLogEntry {
            error_code: error.error_code(),
            error_message: error.to_string(),
            severity: error.severity(),
            context: context.clone(),
            stack_trace: std::backtrace::Backtrace::capture().to_string(),
            timestamp: chrono::Utc::now(),
        };

        self.logger.log_error(log_entry).await;
    }

    async fn collect_metrics(&self, error: &AppError) {
        self.metrics.increment_counter(
            "errors_total",
            &[
                ("error_type", &format!("{:?}", error)),
                ("error_code", &error.error_code().to_string()),
                ("severity", &format!("{:?}", error.severity())),
            ]
        ).await;
    }

    async fn send_alert(&self, error: &AppError, context: &ErrorContext) {
        // 实现告警逻辑
        // 可以发送到监控系统、邮件、Slack等
    }

    fn build_error_details(&self, error: &AppError, context: &ErrorContext) -> serde_json::Value {
        serde_json::json!({
            "error_type": format!("{:?}", error),
            "context": context,
            "timestamp": chrono::Utc::now(),
            "suggestions": self.get_error_suggestions(error)
        })
    }

    fn get_error_suggestions(&self, error: &AppError) -> Vec<String> {
        match error {
            AppError::Database { .. } => vec![
                "检查数据库连接".to_string(),
                "确认磁盘空间充足".to_string(),
                "重启应用程序".to_string(),
            ],
            AppError::Inference { .. } => vec![
                "检查模型文件完整性".to_string(),
                "确认内存充足".to_string(),
                "尝试重新加载模型".to_string(),
            ],
            AppError::Network { .. } => vec![
                "检查网络连接".to_string(),
                "确认防火墙设置".to_string(),
                "稍后重试".to_string(),
            ],
            _ => vec!["联系技术支持".to_string()],
        }
    }
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ErrorContext {
    pub request_id: String,
    pub user_id: Option<String>,
    pub session_id: Option<String>,
    pub operation: String,
    pub parameters: serde_json::Value,
    pub timestamp: chrono::DateTime<chrono::Utc>,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct ErrorResponse {
    pub code: u32,
    pub message: String,
    pub details: serde_json::Value,
    pub severity: ErrorSeverity,
    pub retryable: bool,
    pub timestamp: chrono::DateTime<chrono::Utc>,
    pub request_id: String,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct ErrorLogEntry {
    pub error_code: u32,
    pub error_message: String,
    pub severity: ErrorSeverity,
    pub context: ErrorContext,
    pub stack_trace: String,
    pub timestamp: chrono::DateTime<chrono::Utc>,
}
```

#### 5.1.2 重试机制

```rust
use tokio::time::{sleep, Duration};
use std::future::Future;

// 重试策略
#[derive(Debug, Clone)]
pub enum RetryStrategy {
    Fixed { interval: Duration },
    Exponential { base: Duration, max: Duration, multiplier: f64 },
    Linear { base: Duration, increment: Duration, max: Duration },
}

// 重试配置
#[derive(Debug, Clone)]
pub struct RetryConfig {
    pub strategy: RetryStrategy,
    pub max_attempts: u32,
    pub timeout: Option<Duration>,
    pub retryable_errors: Vec<u32>, // 可重试的错误码
}

impl Default for RetryConfig {
    fn default() -> Self {
        Self {
            strategy: RetryStrategy::Exponential {
                base: Duration::from_millis(100),
                max: Duration::from_secs(30),
                multiplier: 2.0,
            },
            max_attempts: 3,
            timeout: Some(Duration::from_secs(60)),
            retryable_errors: vec![1001, 1002, 1003], // 数据库、推理、网络错误
        }
    }
}

// 重试执行器
pub struct RetryExecutor {
    config: RetryConfig,
}

impl RetryExecutor {
    pub fn new(config: RetryConfig) -> Self {
        Self { config }
    }

    // 执行带重试的操作
    pub async fn execute<F, T, E>(&self, operation: F) -> Result<T, E>
    where
        F: Fn() -> Box<dyn Future<Output = Result<T, E>> + Send + Unpin>,
        E: Into<AppError> + Clone,
    {
        let mut attempt = 0;
        let mut last_error = None;

        while attempt < self.config.max_attempts {
            attempt += 1;

            // 执行操作
            match operation().await {
                Ok(result) => return Ok(result),
                Err(error) => {
                    let app_error: AppError = error.clone().into();

                    // 检查是否可重试
                    if !self.is_retryable(&app_error) || attempt >= self.config.max_attempts {
                        return Err(error);
                    }

                    last_error = Some(error);

                    // 计算等待时间
                    let wait_time = self.calculate_wait_time(attempt);

                    tracing::warn!(
                        "操作失败，第{}次重试，等待{:?}后重试: {}",
                        attempt,
                        wait_time,
                        app_error
                    );

                    sleep(wait_time).await;
                }
            }
        }

        Err(last_error.unwrap())
    }

    fn is_retryable(&self, error: &AppError) -> bool {
        self.config.retryable_errors.contains(&error.error_code())
    }

    fn calculate_wait_time(&self, attempt: u32) -> Duration {
        match &self.config.strategy {
            RetryStrategy::Fixed { interval } => *interval,
            RetryStrategy::Exponential { base, max, multiplier } => {
                let wait = Duration::from_millis(
                    (base.as_millis() as f64 * multiplier.powi(attempt as i32 - 1)) as u64
                );
                std::cmp::min(wait, *max)
            }
            RetryStrategy::Linear { base, increment, max } => {
                let wait = *base + *increment * (attempt - 1);
                std::cmp::min(wait, *max)
            }
        }
    }
}

// 使用示例
pub async fn example_with_retry() -> Result<String, AppError> {
    let retry_config = RetryConfig::default();
    let executor = RetryExecutor::new(retry_config);

    executor.execute(|| {
        Box::new(async {
            // 模拟可能失败的操作
            if fastrand::f32() < 0.7 {
                Err(AppError::Network {
                    message: "连接超时".to_string(),
                    endpoint: Some("http://example.com".to_string()),
                })
            } else {
                Ok("操作成功".to_string())
            }
        })
    }).await
}
```

### 5.2 性能优化策略

#### 5.2.1 内存管理优化

```rust
use std::sync::Arc;
use tokio::sync::RwLock;
use lru::LruCache;

// 内存池管理器
pub struct MemoryPoolManager {
    // 模型缓存池
    model_cache: Arc<RwLock<LruCache<String, Arc<dyn Model>>>>,
    // 向量缓存池
    embedding_cache: Arc<RwLock<LruCache<String, Vec<f32>>>>,
    // 消息缓存池
    message_cache: Arc<RwLock<LruCache<String, ChatMessage>>>,
    // 内存使用统计
    memory_stats: Arc<RwLock<MemoryStats>>,
    // 配置
    config: MemoryConfig,
}

#[derive(Debug, Clone)]
pub struct MemoryConfig {
    pub max_model_cache_size: usize,
    pub max_embedding_cache_size: usize,
    pub max_message_cache_size: usize,
    pub memory_threshold: f64, // 内存使用阈值
    pub gc_interval: Duration, // 垃圾回收间隔
}

#[derive(Debug, Default)]
pub struct MemoryStats {
    pub total_allocated: usize,
    pub model_memory: usize,
    pub embedding_memory: usize,
    pub message_memory: usize,
    pub last_gc_time: Option<chrono::DateTime<chrono::Utc>>,
}

impl MemoryPoolManager {
    pub fn new(config: MemoryConfig) -> Self {
        let manager = Self {
            model_cache: Arc::new(RwLock::new(LruCache::new(config.max_model_cache_size))),
            embedding_cache: Arc::new(RwLock::new(LruCache::new(config.max_embedding_cache_size))),
            message_cache: Arc::new(RwLock::new(LruCache::new(config.max_message_cache_size))),
            memory_stats: Arc::new(RwLock::new(MemoryStats::default())),
            config,
        };

        // 启动内存监控任务
        manager.start_memory_monitor();
        manager
    }

    // 获取模型（带缓存）
    pub async fn get_model(&self, model_id: &str) -> Option<Arc<dyn Model>> {
        let cache = self.model_cache.read().await;
        cache.peek(model_id).cloned()
    }

    // 缓存模型
    pub async fn cache_model(&self, model_id: String, model: Arc<dyn Model>) {
        let mut cache = self.model_cache.write().await;

        // 检查内存使用情况
        if self.should_evict_memory().await {
            self.evict_least_used_models(&mut cache).await;
        }

        cache.put(model_id, model);
        self.update_memory_stats().await;
    }

    // 智能内存回收
    async fn smart_garbage_collection(&self) {
        let current_usage = self.get_memory_usage().await;

        if current_usage > self.config.memory_threshold {
            tracing::info!("内存使用率{}%，开始智能回收", current_usage * 100.0);

            // 1. 清理过期的嵌入向量缓存
            self.cleanup_embedding_cache().await;

            // 2. 清理旧消息缓存
            self.cleanup_message_cache().await;

            // 3. 如果仍然超过阈值，卸载最少使用的模型
            if self.get_memory_usage().await > self.config.memory_threshold {
                self.unload_least_used_models().await;
            }

            // 4. 强制垃圾回收
            self.force_gc().await;
        }
    }

    async fn cleanup_embedding_cache(&self) {
        let mut cache = self.embedding_cache.write().await;
        let original_size = cache.len();

        // 清理一半的缓存
        let target_size = original_size / 2;
        while cache.len() > target_size {
            cache.pop_lru();
        }

        tracing::info!("清理嵌入向量缓存: {} -> {}", original_size, cache.len());
    }

    async fn unload_least_used_models(&self) {
        let mut cache = self.model_cache.write().await;
        let original_size = cache.len();

        // 只保留最近使用的模型
        let keep_count = std::cmp::max(1, original_size / 3);
        while cache.len() > keep_count {
            if let Some((model_id, _)) = cache.pop_lru() {
                tracing::info!("卸载模型: {}", model_id);
            }
        }
    }

    fn start_memory_monitor(&self) {
        let manager = self.clone();
        tokio::spawn(async move {
            let mut interval = tokio::time::interval(manager.config.gc_interval);

            loop {
                interval.tick().await;
                manager.smart_garbage_collection().await;
            }
        });
    }
}

// 对象池模式
pub struct ObjectPool<T> {
    pool: Arc<RwLock<Vec<T>>>,
    factory: Arc<dyn Fn() -> T + Send + Sync>,
    max_size: usize,
}

impl<T> ObjectPool<T>
where
    T: Send + Sync + 'static
{
    pub fn new<F>(factory: F, max_size: usize) -> Self
    where
        F: Fn() -> T + Send + Sync + 'static
    {
        Self {
            pool: Arc::new(RwLock::new(Vec::new())),
            factory: Arc::new(factory),
            max_size,
        }
    }

    // 获取对象
    pub async fn acquire(&self) -> PooledObject<T> {
        let mut pool = self.pool.write().await;

        let object = if let Some(obj) = pool.pop() {
            obj
        } else {
            (self.factory)()
        };

        PooledObject {
            object: Some(object),
            pool: Arc::clone(&self.pool),
            max_size: self.max_size,
        }
    }

    // 获取池大小
    pub async fn size(&self) -> usize {
        let pool = self.pool.read().await;
        pool.len()
    }
}

// 池化对象包装器
pub struct PooledObject<T> {
    object: Option<T>,
    pool: Arc<RwLock<Vec<T>>>,
    max_size: usize,
}

impl<T> PooledObject<T> {
    // 获取对象引用
    pub fn as_ref(&self) -> &T {
        self.object.as_ref().unwrap()
    }

    // 获取可变引用
    pub fn as_mut(&mut self) -> &mut T {
        self.object.as_mut().unwrap()
    }
}

impl<T> Drop for PooledObject<T> {
    fn drop(&mut self) {
        if let Some(object) = self.object.take() {
            let pool = Arc::clone(&self.pool);
            let max_size = self.max_size;

            tokio::spawn(async move {
                let mut pool = pool.write().await;
                if pool.len() < max_size {
                    pool.push(object);
                }
                // 如果池已满，对象会被丢弃
            });
        }
    }
}
```

#### 5.2.2 数据库性能优化

```rust
// 数据库连接池优化
use sqlx::{Pool, Sqlite, SqlitePool};
use std::time::Duration;

pub struct DatabaseManager {
    pool: SqlitePool,
    read_pool: SqlitePool,  // 只读连接池
    write_pool: SqlitePool, // 写入连接池
    cache: Arc<RwLock<QueryCache>>,
}

impl DatabaseManager {
    pub async fn new(database_url: &str) -> Result<Self, sqlx::Error> {
        // 主连接池配置
        let pool = SqlitePool::connect_with(
            sqlx::sqlite::SqliteConnectOptions::from_str(database_url)?
                .create_if_missing(true)
                .journal_mode(sqlx::sqlite::SqliteJournalMode::Wal)
                .synchronous(sqlx::sqlite::SqliteSynchronous::Normal)
                .busy_timeout(Duration::from_secs(30))
                .pragma("cache_size", "10000")
                .pragma("temp_store", "memory")
                .pragma("mmap_size", "268435456") // 256MB
        ).await?;

        // 只读连接池（用于查询）
        let read_pool = SqlitePool::connect_with(
            sqlx::sqlite::SqliteConnectOptions::from_str(database_url)?
                .read_only(true)
                .journal_mode(sqlx::sqlite::SqliteJournalMode::Wal)
                .pragma("cache_size", "5000")
                .pragma("query_only", "true")
        ).await?;

        // 写入连接池（用于写操作）
        let write_pool = SqlitePool::connect_with(
            sqlx::sqlite::SqliteConnectOptions::from_str(database_url)?
                .journal_mode(sqlx::sqlite::SqliteJournalMode::Wal)
                .synchronous(sqlx::sqlite::SqliteSynchronous::Normal)
                .busy_timeout(Duration::from_secs(60))
        ).await?;

        Ok(Self {
            pool,
            read_pool,
            write_pool,
            cache: Arc::new(RwLock::new(QueryCache::new(1000))),
        })
    }

    // 批量插入优化
    pub async fn batch_insert_messages(
        &self,
        messages: Vec<ChatMessage>,
    ) -> Result<(), sqlx::Error> {
        let mut tx = self.write_pool.begin().await?;

        // 使用预编译语句
        let mut query = sqlx::QueryBuilder::new(
            "INSERT INTO chat_messages (id, session_id, role, content, created_at) "
        );

        query.push_values(messages.iter(), |mut b, message| {
            b.push_bind(&message.id)
             .push_bind(&message.session_id)
             .push_bind(&message.role)
             .push_bind(&message.content)
             .push_bind(&message.created_at);
        });

        query.build().execute(&mut *tx).await?;
        tx.commit().await?;

        Ok(())
    }

    // 分页查询优化
    pub async fn get_messages_paginated(
        &self,
        session_id: &str,
        limit: i32,
        offset: i32,
    ) -> Result<Vec<ChatMessage>, sqlx::Error> {
        // 检查缓存
        let cache_key = format!("messages:{}:{}:{}", session_id, limit, offset);
        if let Some(cached) = self.get_from_cache(&cache_key).await {
            return Ok(cached);
        }

        // 使用索引优化的查询
        let messages = sqlx::query_as!(
            ChatMessage,
            r#"
            SELECT * FROM chat_messages
            WHERE session_id = ?
            ORDER BY created_at DESC
            LIMIT ? OFFSET ?
            "#,
            session_id, limit, offset
        )
        .fetch_all(&self.read_pool)
        .await?;

        // 缓存结果
        self.cache_result(&cache_key, &messages).await;

        Ok(messages)
    }

    // 查询缓存
    async fn get_from_cache(&self, key: &str) -> Option<Vec<ChatMessage>> {
        let cache = self.cache.read().await;
        cache.get(key).cloned()
    }

    async fn cache_result(&self, key: &str, messages: &[ChatMessage]) {
        let mut cache = self.cache.write().await;
        cache.put(key.to_string(), messages.to_vec());
    }
}

// 查询缓存
struct QueryCache {
    cache: LruCache<String, Vec<ChatMessage>>,
    ttl: Duration,
}

impl QueryCache {
    fn new(capacity: usize) -> Self {
        Self {
            cache: LruCache::new(capacity),
            ttl: Duration::from_secs(300), // 5分钟TTL
        }
    }

    fn get(&mut self, key: &str) -> Option<&Vec<ChatMessage>> {
        self.cache.get(key)
    }

    fn put(&mut self, key: String, value: Vec<ChatMessage>) {
        self.cache.put(key, value);
    }
}
```

#### 5.2.3 前端性能优化

```typescript
// 虚拟滚动优化
import { ref, computed, onMounted, onUnmounted } from 'vue'

export function useVirtualScroll<T>(
  items: Ref<T[]>,
  itemHeight: number,
  containerHeight: number,
  overscan: number = 5
) {
  const scrollTop = ref(0)
  const containerRef = ref<HTMLElement>()

  // 计算可见范围
  const visibleRange = computed(() => {
    const start = Math.floor(scrollTop.value / itemHeight)
    const end = Math.min(
      start + Math.ceil(containerHeight / itemHeight),
      items.value.length
    )

    return {
      start: Math.max(0, start - overscan),
      end: Math.min(items.value.length, end + overscan)
    }
  })

  // 可见项目
  const visibleItems = computed(() => {
    const { start, end } = visibleRange.value
    return items.value.slice(start, end).map((item, index) => ({
      item,
      index: start + index
    }))
  })

  // 总高度
  const totalHeight = computed(() => items.value.length * itemHeight)

  // 偏移量
  const offsetY = computed(() => visibleRange.value.start * itemHeight)

  // 滚动处理
  const handleScroll = (event: Event) => {
    const target = event.target as HTMLElement
    scrollTop.value = target.scrollTop
  }

  onMounted(() => {
    containerRef.value?.addEventListener('scroll', handleScroll)
  })

  onUnmounted(() => {
    containerRef.value?.removeEventListener('scroll', handleScroll)
  })

  return {
    containerRef,
    visibleItems,
    totalHeight,
    offsetY
  }
}

// 防抖和节流
export function useDebounce<T extends (...args: any[]) => any>(
  fn: T,
  delay: number
): T {
  let timeoutId: NodeJS.Timeout

  return ((...args: Parameters<T>) => {
    clearTimeout(timeoutId)
    timeoutId = setTimeout(() => fn(...args), delay)
  }) as T
}

export function useThrottle<T extends (...args: any[]) => any>(
  fn: T,
  delay: number
): T {
  let lastCall = 0

  return ((...args: Parameters<T>) => {
    const now = Date.now()
    if (now - lastCall >= delay) {
      lastCall = now
      return fn(...args)
    }
  }) as T
}

// 图片懒加载
export function useLazyLoad() {
  const observer = new IntersectionObserver(
    (entries) => {
      entries.forEach((entry) => {
        if (entry.isIntersecting) {
          const img = entry.target as HTMLImageElement
          const src = img.dataset.src
          if (src) {
            img.src = src
            img.removeAttribute('data-src')
            observer.unobserve(img)
          }
        }
      })
    },
    { threshold: 0.1 }
  )

  const observe = (element: HTMLElement) => {
    observer.observe(element)
  }

  const unobserve = (element: HTMLElement) => {
    observer.unobserve(element)
  }

  return { observe, unobserve }
}

// 组件缓存策略
export const componentCache = new Map<string, any>()

export function useCachedComponent(key: string, factory: () => any) {
  if (!componentCache.has(key)) {
    componentCache.set(key, factory())
  }
  return componentCache.get(key)
}

// 内存泄漏检测
export function useMemoryLeakDetection() {
  const listeners = new Set<() => void>()
  const timers = new Set<NodeJS.Timeout>()
  const observers = new Set<IntersectionObserver | MutationObserver>()

  const addListener = (element: EventTarget, event: string, handler: EventListener) => {
    element.addEventListener(event, handler)
    const cleanup = () => element.removeEventListener(event, handler)
    listeners.add(cleanup)
    return cleanup
  }

  const addTimer = (callback: () => void, delay: number) => {
    const timer = setTimeout(callback, delay)
    timers.add(timer)
    return timer
  }

  const addObserver = (observer: IntersectionObserver | MutationObserver) => {
    observers.add(observer)
    return observer
  }

  const cleanup = () => {
    listeners.forEach(cleanup => cleanup())
    timers.forEach(timer => clearTimeout(timer))
    observers.forEach(observer => observer.disconnect())

    listeners.clear()
    timers.clear()
    observers.clear()
  }

  onUnmounted(cleanup)

  return {
    addListener,
    addTimer,
    addObserver,
    cleanup
  }
}
```

### 5.3 测试策略

#### 5.3.1 单元测试

```rust
// Rust后端单元测试
#[cfg(test)]
mod tests {
    use super::*;
    use tokio_test;

    #[tokio::test]
    async fn test_chat_service_create_session() {
        // 准备测试数据
        let db_pool = create_test_db_pool().await;
        let ai_engine = Arc::new(MockInferenceEngine::new());
        let knowledge_service = Arc::new(MockKnowledgeService::new());

        let chat_service = ChatService::new(db_pool, ai_engine, knowledge_service);

        // 执行测试
        let result = chat_service.create_session(
            Some("测试会话".to_string()),
            Some("test-model".to_string())
        ).await;

        // 验证结果
        assert!(result.is_ok());
        let session = result.unwrap();
        assert_eq!(session.title, "测试会话");
        assert_eq!(session.model_id, "test-model");
    }

    #[tokio::test]
    async fn test_chat_service_send_message() {
        let chat_service = create_test_chat_service().await;
        let session = create_test_session(&chat_service).await;

        let request = SendMessageRequest {
            session_id: session.id.clone(),
            message: "Hello, AI!".to_string(),
            attachments: None,
            knowledge_config: None,
            model_config: None,
        };

        let result = chat_service.send_message(request).await;
        assert!(result.is_ok());

        // 验证消息已保存
        let messages = chat_service.get_messages(&session.id, 10, 0).await.unwrap();
        assert_eq!(messages.len(), 2); // 用户消息 + AI响应
    }

    #[tokio::test]
    async fn test_error_handling() {
        let chat_service = create_test_chat_service().await;

        // 测试无效会话ID
        let request = SendMessageRequest {
            session_id: "invalid-session".to_string(),
            message: "Test".to_string(),
            attachments: None,
            knowledge_config: None,
            model_config: None,
        };

        let result = chat_service.send_message(request).await;
        assert!(result.is_err());

        match result.unwrap_err() {
            ChatError::SessionNotFound { .. } => {}, // 期望的错误类型
            _ => panic!("期望SessionNotFound错误"),
        }
    }

    // 性能测试
    #[tokio::test]
    async fn test_concurrent_message_sending() {
        let chat_service = Arc::new(create_test_chat_service().await);
        let session = create_test_session(&chat_service).await;

        let mut handles = vec![];

        // 并发发送100条消息
        for i in 0..100 {
            let service = Arc::clone(&chat_service);
            let session_id = session.id.clone();

            let handle = tokio::spawn(async move {
                let request = SendMessageRequest {
                    session_id,
                    message: format!("消息 {}", i),
                    attachments: None,
                    knowledge_config: None,
                    model_config: None,
                };

                service.send_message(request).await
            });

            handles.push(handle);
        }

        // 等待所有任务完成
        let results = futures::future::join_all(handles).await;

        // 验证所有消息都成功发送
        for result in results {
            assert!(result.is_ok());
            assert!(result.unwrap().is_ok());
        }
    }

    // 辅助函数
    async fn create_test_db_pool() -> SqlitePool {
        SqlitePool::connect(":memory:").await.unwrap()
    }

    async fn create_test_chat_service() -> ChatService {
        let db_pool = create_test_db_pool().await;
        let ai_engine = Arc::new(MockInferenceEngine::new());
        let knowledge_service = Arc::new(MockKnowledgeService::new());

        ChatService::new(db_pool, ai_engine, knowledge_service)
    }

    async fn create_test_session(service: &ChatService) -> ChatSession {
        service.create_session(
            Some("测试会话".to_string()),
            Some("test-model".to_string())
        ).await.unwrap()
    }
}

// Mock对象
struct MockInferenceEngine;

impl MockInferenceEngine {
    fn new() -> Self {
        Self
    }
}

#[async_trait::async_trait]
impl InferenceEngine for MockInferenceEngine {
    async fn inference(&self, request: InferenceRequest) -> Result<InferenceResponse, InferenceError> {
        Ok(InferenceResponse {
            content: format!("Mock response to: {}", request.prompt),
            usage: TokenUsage {
                prompt_tokens: 10,
                completion_tokens: 20,
                total_tokens: 30,
            },
            model_info: ModelInfo {
                model_id: request.model_id,
                version: "mock-1.0".to_string(),
            },
        })
    }

    async fn inference_stream(&self, request: InferenceRequest) -> Result<mpsc::Receiver<StreamToken>, InferenceError> {
        let (tx, rx) = mpsc::channel(100);

        tokio::spawn(async move {
            let response = format!("Mock response to: {}", request.prompt);
            for (i, char) in response.chars().enumerate() {
                let _ = tx.send(StreamToken {
                    content: char.to_string(),
                    is_final: i == response.len() - 1,
                }).await;
                tokio::time::sleep(Duration::from_millis(10)).await;
            }
        });

        Ok(rx)
    }
}
```

#### 5.3.2 集成测试

```typescript
// 前端集成测试 (Playwright)
import { test, expect } from '@playwright/test'

test.describe('聊天功能集成测试', () => {
  test.beforeEach(async ({ page }) => {
    await page.goto('/')
    await page.waitForLoadState('networkidle')
  })

  test('创建新会话并发送消息', async ({ page }) => {
    // 点击新建会话按钮
    await page.click('[data-testid="new-session-btn"]')

    // 等待会话创建完成
    await page.waitForSelector('[data-testid="chat-input"]')

    // 输入消息
    const message = '你好，这是一条测试消息'
    await page.fill('[data-testid="chat-input"]', message)

    // 发送消息
    await page.click('[data-testid="send-btn"]')

    // 验证用户消息显示
    await expect(page.locator('[data-testid="user-message"]').last()).toContainText(message)

    // 等待AI响应
    await page.waitForSelector('[data-testid="ai-message"]', { timeout: 10000 })

    // 验证AI响应存在
    await expect(page.locator('[data-testid="ai-message"]').last()).toBeVisible()
  })

  test('流式响应显示', async ({ page }) => {
    await page.click('[data-testid="new-session-btn"]')
    await page.waitForSelector('[data-testid="chat-input"]')

    // 启用流式响应
    await page.check('[data-testid="stream-toggle"]')

    await page.fill('[data-testid="chat-input"]', '请写一首诗')
    await page.click('[data-testid="send-btn"]')

    // 验证流式响应动画
    await expect(page.locator('[data-testid="typing-indicator"]')).toBeVisible()

    // 等待响应完成
    await page.waitForSelector('[data-testid="ai-message"]', { timeout: 15000 })
    await expect(page.locator('[data-testid="typing-indicator"]')).not.toBeVisible()
  })

  test('知识库搜索集成', async ({ page }) => {
    // 先上传测试文档到知识库
    await page.goto('/knowledge')
    await page.click('[data-testid="upload-document-btn"]')

    const fileInput = page.locator('input[type="file"]')
    await fileInput.setInputFiles('test-files/sample.pdf')

    await page.waitForSelector('[data-testid="upload-success"]')

    // 回到聊天页面
    await page.goto('/')
    await page.click('[data-testid="new-session-btn"]')

    // 启用知识库搜索
    await page.click('[data-testid="knowledge-toggle"]')
    await page.selectOption('[data-testid="knowledge-select"]', 'test-kb')

    // 发送相关问题
    await page.fill('[data-testid="chat-input"]', '文档中提到了什么？')
    await page.click('[data-testid="send-btn"]')

    // 验证知识库引用显示
    await page.waitForSelector('[data-testid="knowledge-reference"]')
    await expect(page.locator('[data-testid="knowledge-reference"]')).toBeVisible()
  })

  test('多模态文件上传', async ({ page }) => {
    await page.click('[data-testid="new-session-btn"]')
    await page.waitForSelector('[data-testid="chat-input"]')

    // 上传图片
    const fileInput = page.locator('[data-testid="file-input"]')
    await fileInput.setInputFiles('test-files/sample.jpg')

    // 验证文件预览
    await expect(page.locator('[data-testid="file-preview"]')).toBeVisible()

    // 发送带附件的消息
    await page.fill('[data-testid="chat-input"]', '请描述这张图片')
    await page.click('[data-testid="send-btn"]')

    // 验证消息包含附件
    await expect(page.locator('[data-testid="message-attachment"]')).toBeVisible()
  })
})

test.describe('性能测试', () => {
  test('大量消息加载性能', async ({ page }) => {
    // 创建包含大量消息的会话
    await page.goto('/chat/test-session-with-many-messages')

    // 测量加载时间
    const startTime = Date.now()
    await page.waitForSelector('[data-testid="message-list"]')
    const loadTime = Date.now() - startTime

    // 验证加载时间在合理范围内
    expect(loadTime).toBeLessThan(2000) // 2秒内

    // 验证虚拟滚动工作正常
    const visibleMessages = await page.locator('[data-testid="message-item"]').count()
    expect(visibleMessages).toBeLessThan(50) // 只渲染可见消息
  })

  test('内存使用监控', async ({ page }) => {
    // 监控内存使用
    const initialMemory = await page.evaluate(() => {
      return (performance as any).memory?.usedJSHeapSize || 0
    })

    // 执行大量操作
    for (let i = 0; i < 10; i++) {
      await page.click('[data-testid="new-session-btn"]')
      await page.fill('[data-testid="chat-input"]', `测试消息 ${i}`)
      await page.click('[data-testid="send-btn"]')
      await page.waitForSelector('[data-testid="ai-message"]')
    }

    const finalMemory = await page.evaluate(() => {
      return (performance as any).memory?.usedJSHeapSize || 0
    })

    // 验证内存增长在合理范围内
    const memoryIncrease = finalMemory - initialMemory
    expect(memoryIncrease).toBeLessThan(50 * 1024 * 1024) // 50MB
  })
})
```

---

## 6. 总结

### 6.1 方案特点

本AI Studio最终方案设计具有以下核心特点：

1. **技术先进性**：采用Vue3 + Tauri + Rust技术栈，结合现代化的AI推理引擎
2. **架构完整性**：从前端UI到后端服务，从数据存储到AI推理的完整技术架构
3. **功能丰富性**：涵盖聊天、知识库、模型管理、多模态、网络协作等全方位功能
4. **性能优化**：多层次的性能优化策略，确保应用流畅运行
5. **质量保障**：完善的错误处理、测试策略和监控机制
6. **用户体验**：支持深色/浅色主题、中英文双语、响应式设计

### 6.2 技术优势

- **本地化部署**：保护用户隐私，无需网络依赖
- **跨平台支持**：Windows和macOS桌面应用
- **模块化设计**：易于扩展和维护
- **高性能推理**：支持多种AI引擎和硬件加速
- **智能缓存**：多级缓存策略提升响应速度
- **插件生态**：可扩展的插件系统

### 6.3 实施建议

1. **分阶段开发**：按功能模块分阶段实施，优先核心功能
2. **持续集成**：建立完善的CI/CD流程
3. **用户反馈**：及时收集用户反馈，迭代优化
4. **性能监控**：建立完善的性能监控体系
5. **文档维护**：保持技术文档的及时更新

本方案设计为AI Studio项目提供了完整的技术实现路径，确保项目能够高质量、高效率地完成开发和部署。