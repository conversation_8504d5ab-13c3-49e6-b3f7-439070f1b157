# AI Studio 完整方案设计

## 1. 项目概述

### 1.1 项目背景

AI Studio 是一个基于 Vue3 + TypeScript + Vite + Tauri 2.x 技术栈的中文AI助手桌面应用，专为 Windows/macOS 平台设计。项目旨在提供本地大模型部署、知识库管理、局域网共享等企业级功能，实现完全离线的AI助手解决方案。

### 1.2 项目目标

- **本地化部署**：支持本地大模型推理，无需依赖云端服务
- **知识库管理**：提供文档解析、向量搜索、RAG增强等功能
- **局域网协作**：实现设备间模型、知识库、配置的共享
- **多模态支持**：集成OCR、TTS、语音识别等多媒体处理能力
- **企业级质量**：提供生产环境可用的稳定性和性能
- **插件生态**：支持第三方插件扩展和云端模型API集成

### 1.3 核心功能特性

1. **智能对话系统**
   - 支持流式对话、多模态输入、会话管理
   - Markdown渲染、代码高亮、数学公式支持
   - 多会话并行处理、会话历史持久化

2. **知识库管理系统**
   - 支持PDF、Word、TXT等多种格式文档上传
   - 基于ChromaDB实现向量检索和RAG增强
   - 智能文档分块、语义搜索、知识图谱构建

3. **模型管理系统**
   - 本地模型加载卸载、在线模型下载
   - 性能监控、模型量化、GPU加速支持
   - HuggingFace集成、断点续传下载

4. **多模态处理系统**
   - 图像识别、语音识别(STT)、语音合成(TTS)
   - OCR文字识别、视频分析、格式转换
   - 批量处理、任务队列管理

5. **远程配置系统**
   - API密钥管理、远程模型配置、代理设置
   - 支持OpenAI、Anthropic、Google等主流AI服务商
   - 配置同步、安全存储、连通性验证

6. **局域网共享系统**
   - mDNS设备发现、P2P文件传输、分布式推理
   - 资源共享、权限管理、安全认证
   - 跨设备协同、实时同步

7. **插件扩展系统**
   - WASM插件运行环境、插件市场
   - 沙箱隔离、权限管理、热插拔支持
   - 自定义API集成、JavaScript脚本支持

8. **系统管理功能**
   - 主题切换、语言切换、用户信息管理
   - 性能监控、日志管理、自动更新
   - 数据备份、健康检查、故障诊断

## 2. 技术架构

### 2.1 整体架构设计

AI Studio 采用现代化的桌面应用架构，基于 Tauri 框架实现跨平台支持：

```
┌─────────────────────────────────────────────────────────────┐
│                    前端层 (Frontend)                        │
│  Vue3 + TypeScript + Vite + Naive UI + Tailwind CSS       │
├─────────────────────────────────────────────────────────────┤
│                    应用层 (Application)                     │
│              Tauri 2.x + Rust Backend                     │
├─────────────────────────────────────────────────────────────┤
│                    服务层 (Services)                       │
│  AI推理引擎 │ 向量数据库 │ 文件处理 │ 网络通信 │ 插件系统    │
├─────────────────────────────────────────────────────────────┤
│                    数据层 (Data)                           │
│     SQLite 主数据库 │ ChromaDB 向量库 │ 本地文件存储       │
└─────────────────────────────────────────────────────────────┘
```

### 2.2 前端技术栈

**核心框架**
- **Vue 3.4+**：采用 Composition API，提供响应式数据管理
- **TypeScript 5.0+**：强类型支持，提升代码质量和开发效率
- **Vite 7.0+**：快速构建工具，支持热更新和模块化开发

**UI 组件库**
- **Naive UI 2.0+**：现代化 Vue3 组件库，提供丰富的UI组件
- **Tailwind CSS 3.0+**：原子化CSS框架，快速样式开发
- **SCSS**：CSS预处理器，支持变量和混入

**状态管理**
- **Pinia 2.0+**：Vue3 官方推荐的状态管理库
- **Vue Router 4.0+**：单页应用路由管理

**工具库**
- **Vue I18n 9.0+**：国际化支持
- **Iconify**：图标库，提供丰富的图标资源
- **@vueuse/markdown**：Markdown渲染支持
- **Prism.js**：代码语法高亮
- **ECharts 5.0+**：数据可视化图表库

### 2.3 后端技术栈

**核心框架**
- **Tauri 2.x**：跨平台桌面应用框架
- **Rust 1.75+**：系统级编程语言，提供高性能和内存安全

**数据存储**
- **SQLite 3.45+**：轻量级关系型数据库，用于主要数据存储
- **ChromaDB**：向量数据库，用于知识库向量存储和检索

**AI 推理引擎**
- **Candle-core**：Rust原生AI推理框架
- **llama.cpp**：高性能大语言模型推理引擎

**网络通信**
- **Tokio**：异步运行时，支持高并发网络操作
- **Reqwest**：HTTP客户端库
- **mdns**：多播DNS服务发现
- **tokio-tungstenite**：WebSocket支持

**文档处理**
- **pdf-extract**：PDF文档解析
- **docx-rs**：Word文档处理
- **calamine**：Excel文档处理

**多媒体处理**
- **image + imageproc**：图像处理和分析
- **rodio**：音频播放和处理
- **whisper-rs**：语音识别引擎

**工具库**
- **Serde + serde_json**：序列化和反序列化
- **thiserror + anyhow**：错误处理
- **tracing + tracing-subscriber**：日志系统
- **aes-gcm + ring**：加密和安全

### 2.4 开发工具链

**包管理**
- **npm/pnpm**：前端包管理器
- **Cargo**：Rust包管理器

**代码质量**
- **Prettier**：前端代码格式化
- **ESLint**：前端代码检查
- **rustfmt**：Rust代码格式化
- **Clippy**：Rust代码检查

**测试框架**
- **Vitest**：前端单元测试
- **cargo test**：Rust单元测试

**构建工具**
- **Tauri CLI**：应用构建和打包
- **Git + Git LFS**：版本控制（支持大文件）

## 3. 系统设计

### 3.1 数据库设计

**主数据库 (SQLite)**

系统采用 SQLite 作为主数据库，存储应用的核心业务数据：

```sql
-- 会话表
CREATE TABLE chat_sessions (
    id TEXT PRIMARY KEY,
    title TEXT NOT NULL,
    model_id TEXT,
    system_prompt TEXT,
    temperature REAL DEFAULT 0.7,
    max_tokens INTEGER DEFAULT 2048,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
);

-- 消息表
CREATE TABLE chat_messages (
    id TEXT PRIMARY KEY,
    session_id TEXT REFERENCES chat_sessions(id),
    role TEXT NOT NULL, -- 'user' | 'assistant' | 'system'
    content TEXT NOT NULL,
    attachments JSON,
    tokens_used INTEGER,
    response_time REAL,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP
);

-- 知识库表
CREATE TABLE knowledge_bases (
    id TEXT PRIMARY KEY,
    name TEXT NOT NULL,
    description TEXT,
    embedding_model TEXT DEFAULT 'sentence-transformers/all-MiniLM-L6-v2',
    chunk_size INTEGER DEFAULT 512,
    chunk_overlap INTEGER DEFAULT 50,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
);

-- 文档表
CREATE TABLE documents (
    id TEXT PRIMARY KEY,
    kb_id TEXT REFERENCES knowledge_bases(id),
    name TEXT NOT NULL,
    type TEXT NOT NULL, -- 'pdf', 'docx', 'txt', 'md'
    size INTEGER,
    path TEXT NOT NULL,
    status TEXT DEFAULT 'processing', -- 'processing', 'completed', 'failed'
    chunks_count INTEGER DEFAULT 0,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
);

-- 模型表
CREATE TABLE models (
    id TEXT PRIMARY KEY,
    name TEXT NOT NULL,
    display_name TEXT NOT NULL,
    description TEXT,
    author TEXT,
    version TEXT,
    size INTEGER,
    quantization TEXT, -- 'none', 'q4_0', 'q4_1', 'q8_0'
    architecture TEXT, -- 'llama', 'mistral', 'qwen'
    context_length INTEGER DEFAULT 2048,
    status TEXT DEFAULT 'available', -- 'available', 'downloading', 'loaded', 'error'
    local_path TEXT,
    download_url TEXT,
    huggingface_id TEXT,
    config JSON,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
);
```

**向量数据库 (ChromaDB)**

用于存储文档向量和实现语义搜索：

- **Collection 结构**：每个知识库对应一个 Collection
- **向量维度**：根据 embedding 模型确定（通常为 384 或 768 维）
- **元数据存储**：文档ID、块索引、页码等信息
- **距离计算**：使用余弦相似度进行语义匹配

### 3.2 安全设计

**数据加密**
- **敏感数据加密**：API密钥、代理密码等使用 AES-GCM 加密存储
- **传输加密**：网络通信使用 TLS 1.3 加密
- **本地存储**：支持数据库文件加密选项

**权限管理**
- **插件沙箱**：插件运行在隔离环境中，限制系统访问
- **网络权限**：细粒度控制网络访问权限
- **文件权限**：限制文件系统访问范围

**身份认证**
- **设备认证**：局域网设备间使用公钥加密认证
- **API认证**：支持多种API认证方式（API Key、OAuth等）
- **会话管理**：安全的会话状态管理

### 3.3 性能设计

**内存管理**
- **模型加载优化**：支持模型分层加载，减少内存占用
- **缓存策略**：智能缓存常用数据，提升响应速度
- **垃圾回收**：定期清理无用数据和临时文件

**并发处理**
- **异步架构**：基于 Tokio 的异步处理，支持高并发
- **任务队列**：后台任务队列处理耗时操作
- **资源池**：数据库连接池、HTTP连接池等资源复用

**性能监控**
- **实时监控**：CPU、内存、GPU使用率实时监控
- **性能指标**：推理速度、响应时间等关键指标
- **性能优化**：自动性能调优和资源分配

## 4. 功能模块设计

### 4.1 聊天模块 (Chat)

**功能描述**
提供AI对话、会话管理、流式响应等核心聊天功能，支持多模态输入和智能上下文管理。

**核心特性**
- **流式响应**：基于SSE (Server-Sent Events) 实现实时token流输出
- **会话管理**：支持多会话并行、会话历史持久化、会话导出
- **RAG集成**：自动检索相关知识库内容增强回复质量
- **多模态输入**：支持文本、图片、音频等多种输入方式
- **Markdown渲染**：支持代码高亮、数学公式、表格等富文本显示

**技术实现**
- **前端**：Vue3 + TypeScript，使用 EventSource 接收流式数据
- **后端**：Rust + Tokio，异步处理推理请求
- **数据存储**：SQLite 存储会话和消息数据
- **推理引擎**：支持本地模型（Candle/llama.cpp）和远程API

**数据库设计**
```sql
-- 会话表
CREATE TABLE chat_sessions (
    id TEXT PRIMARY KEY,
    title TEXT NOT NULL,
    model_id TEXT,
    system_prompt TEXT,
    temperature REAL DEFAULT 0.7,
    max_tokens INTEGER DEFAULT 2048,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
);

-- 消息表
CREATE TABLE chat_messages (
    id TEXT PRIMARY KEY,
    session_id TEXT REFERENCES chat_sessions(id),
    role TEXT NOT NULL, -- 'user' | 'assistant' | 'system'
    content TEXT NOT NULL,
    attachments JSON, -- 附件信息
    tokens_used INTEGER,
    response_time REAL,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP
);
```

**API接口**
```typescript
// 发送消息
POST /api/chat/send
Request: {
  session_id: string;
  message: string;
  attachments?: Array<{type: string; url: string; name: string}>;
  model_config?: {model_id: string; temperature: number; max_tokens: number};
}

// 流式响应
GET /api/chat/stream/{message_id}
Response: Server-Sent Events

// 会话管理
GET /api/chat/sessions
POST /api/chat/sessions
PUT /api/chat/sessions/{id}
DELETE /api/chat/sessions/{id}
```

### 4.2 知识库模块 (Knowledge Base)

**功能描述**
提供文档管理、向量搜索、RAG检索等知识库功能，支持多种文档格式和智能语义搜索。

**核心特性**
- **文档解析**：支持PDF、Word、Excel、Markdown、TXT等多种格式
- **向量化存储**：使用ChromaDB进行向量存储和检索
- **增量索引**：支持文档变更检测和增量更新
- **语义搜索**：基于embedding模型的高质量语义搜索
- **文档切分**：智能文档分块，保持语义完整性
- **知识图谱**：构建实体关系图，增强检索效果

**技术实现**
- **文档解析**：pdf-extract、docx-rs、calamine等Rust库
- **向量化**：sentence-transformers模型生成文档向量
- **存储**：ChromaDB向量数据库 + SQLite元数据存储
- **搜索**：余弦相似度计算 + 重排序算法

**数据库设计**
```sql
-- 知识库表
CREATE TABLE knowledge_bases (
    id TEXT PRIMARY KEY,
    name TEXT NOT NULL,
    description TEXT,
    embedding_model TEXT DEFAULT 'sentence-transformers/all-MiniLM-L6-v2',
    chunk_size INTEGER DEFAULT 512,
    chunk_overlap INTEGER DEFAULT 50,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
);

-- 文档表
CREATE TABLE documents (
    id TEXT PRIMARY KEY,
    kb_id TEXT REFERENCES knowledge_bases(id),
    name TEXT NOT NULL,
    type TEXT NOT NULL, -- 'pdf', 'docx', 'txt', 'md'
    size INTEGER,
    path TEXT NOT NULL,
    status TEXT DEFAULT 'processing', -- 'processing', 'completed', 'failed'
    chunks_count INTEGER DEFAULT 0,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
);

-- 文档块表
CREATE TABLE document_chunks (
    id TEXT PRIMARY KEY,
    document_id TEXT REFERENCES documents(id),
    content TEXT NOT NULL,
    metadata JSON,
    page_number INTEGER,
    chunk_index INTEGER,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP
);
```

**API接口**
```typescript
// 知识库管理
GET /api/knowledge
POST /api/knowledge
PUT /api/knowledge/{id}
DELETE /api/knowledge/{id}

// 文档管理
POST /api/knowledge/{id}/upload
GET /api/knowledge/{id}/documents
DELETE /api/knowledge/{kb_id}/documents/{doc_id}

// 语义搜索
POST /api/knowledge/{id}/search
Request: {
  query: string;
  limit?: number;
  threshold?: number;
  filters?: object;
}
```

### 4.3 模型管理模块 (Model Management)

**功能描述**
提供本地模型下载、加载、量化、部署等功能，支持HuggingFace生态和多种推理引擎。

**核心特性**
- **HuggingFace集成**：支持从HF Hub下载模型，包含镜像站切换
- **断点续传**：支持大文件分片下载和自动恢复
- **模型量化**：集成GPTQ、AWQ等量化工具，减少内存占用
- **GPU加速**：支持CUDA、Metal等GPU加速框架
- **一键部署**：自动化模型部署和服务管理
- **性能监控**：实时监控模型推理性能和资源使用

**技术实现**
- **下载引擎**：多线程分片下载，支持断点续传
- **模型加载**：Candle-core和llama.cpp双引擎支持
- **量化工具**：集成主流量化算法
- **监控系统**：实时性能指标收集和分析

**数据库设计**
```sql
-- 模型表
CREATE TABLE models (
    id TEXT PRIMARY KEY,
    name TEXT NOT NULL,
    display_name TEXT NOT NULL,
    description TEXT,
    author TEXT,
    version TEXT,
    size INTEGER, -- 文件大小(字节)
    quantization TEXT, -- 'none', 'q4_0', 'q4_1', 'q8_0'
    architecture TEXT, -- 'llama', 'mistral', 'qwen'
    context_length INTEGER DEFAULT 2048,
    status TEXT DEFAULT 'available', -- 'available', 'downloading', 'loaded', 'error'
    local_path TEXT,
    download_url TEXT,
    huggingface_id TEXT,
    config JSON, -- 模型配置参数
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
);

-- 下载任务表
CREATE TABLE download_tasks (
    id TEXT PRIMARY KEY,
    model_id TEXT REFERENCES models(id),
    url TEXT NOT NULL,
    total_size INTEGER,
    downloaded_size INTEGER DEFAULT 0,
    status TEXT DEFAULT 'pending', -- 'pending', 'downloading', 'completed', 'failed', 'paused'
    error_message TEXT,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
);
```

**API接口**
```typescript
// 模型管理
GET /api/models
GET /api/models/search
POST /api/models/download
POST /api/models/upload
DELETE /api/models/{id}

// 模型操作
POST /api/models/{id}/load
POST /api/models/{id}/unload
POST /api/models/{id}/quantize

// 下载管理
GET /api/models/downloads
POST /api/models/downloads/{id}/pause
POST /api/models/downloads/{id}/resume
```

### 4.4 多模态处理模块 (Multimodal)

**功能描述**
提供OCR识别、语音处理、图像分析等多模态功能，支持多种媒体格式处理和批量任务管理。

**核心特性**
- **OCR识别**：集成Tesseract或PaddleOCR进行文字识别
- **语音处理**：支持语音转文字(ASR)和文字转语音(TTS)
- **图像分析**：集成YOLO、CLIP等模型进行图像理解
- **视频处理**：支持视频分析和字幕生成
- **格式转换**：支持多种音视频格式转换
- **批量处理**：支持批量文件处理和任务队列

**技术实现**
- **OCR引擎**：Tesseract + 自定义优化算法
- **语音引擎**：whisper-rs + rodio音频处理
- **图像处理**：image + imageproc库
- **任务队列**：异步任务调度和进度管理

**数据库设计**
```sql
-- 多模态任务表
CREATE TABLE multimodal_tasks (
    id TEXT PRIMARY KEY,
    task_type TEXT NOT NULL, -- 'ocr', 'tts', 'asr', 'image_analysis', 'video_analysis'
    input_file_path TEXT NOT NULL,
    output_file_path TEXT,
    parameters JSON, -- 任务参数
    status TEXT DEFAULT 'pending', -- 'pending', 'processing', 'completed', 'failed'
    progress REAL DEFAULT 0.0,
    result JSON, -- 处理结果
    error_message TEXT,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
);

-- 处理历史表
CREATE TABLE processing_history (
    id TEXT PRIMARY KEY,
    task_id TEXT REFERENCES multimodal_tasks(id),
    file_name TEXT NOT NULL,
    file_size INTEGER,
    processing_time REAL, -- 处理耗时(秒)
    model_used TEXT,
    quality_score REAL, -- 处理质量评分
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP
);
```

**API接口**
```typescript
// 多模态处理
POST /api/multimodal/ocr
POST /api/multimodal/tts
POST /api/multimodal/asr
POST /api/multimodal/image/analyze
POST /api/multimodal/video/analyze
POST /api/multimodal/convert

// 任务管理
GET /api/multimodal/tasks
GET /api/multimodal/tasks/{id}
DELETE /api/multimodal/tasks/{id}
GET /api/multimodal/history
```

### 4.5 远程配置模块 (Remote Configuration)

**功能描述**
提供API密钥管理、远程模型配置、代理设置等功能，支持多种AI服务商和配置同步。

**核心特性**
- **API密钥管理**：支持OpenAI、Anthropic、Google等主流AI服务商
- **代理配置**：支持HTTP/HTTPS/SOCKS5代理设置
- **配置同步**：支持配置云端备份和多设备同步
- **安全存储**：敏感信息加密存储，支持主密码保护
- **配置模板**：预设常用配置模板，快速配置
- **配置验证**：自动验证API密钥有效性和网络连通性

**技术实现**
- **加密存储**：AES-GCM加密敏感配置信息
- **代理支持**：reqwest + proxy配置
- **配置验证**：异步连通性测试
- **模板系统**：预定义配置模板

**数据库设计**
```sql
-- 远程配置表
CREATE TABLE remote_configs (
    id TEXT PRIMARY KEY,
    name TEXT NOT NULL,
    provider TEXT NOT NULL, -- 'openai', 'anthropic', 'google', 'custom'
    api_key TEXT NOT NULL, -- 加密存储
    base_url TEXT,
    model_name TEXT,
    max_tokens INTEGER DEFAULT 2048,
    temperature REAL DEFAULT 0.7,
    proxy_type TEXT, -- 'none', 'http', 'https', 'socks5'
    proxy_host TEXT,
    proxy_port INTEGER,
    proxy_username TEXT,
    proxy_password TEXT, -- 加密存储
    is_active BOOLEAN DEFAULT FALSE,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
);

-- 配置模板表
CREATE TABLE config_templates (
    id TEXT PRIMARY KEY,
    name TEXT NOT NULL,
    description TEXT,
    provider TEXT NOT NULL,
    config JSON NOT NULL,
    is_builtin BOOLEAN DEFAULT FALSE,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP
);
```

**API接口**
```typescript
// 配置管理
GET /api/remote/configs
POST /api/remote/configs
PUT /api/remote/configs/{id}
DELETE /api/remote/configs/{id}
POST /api/remote/configs/{id}/test
POST /api/remote/configs/{id}/activate

// 模板管理
GET /api/remote/templates
POST /api/remote/templates

// 配置同步
POST /api/remote/sync/backup
POST /api/remote/sync/restore
```

### 4.6 局域网共享模块 (Network Sharing)

**功能描述**
提供局域网设备发现、P2P通信、资源共享等功能，实现设备间的协同工作。

**核心特性**
- **mDNS发现**：基于mDNS协议的局域网设备自动发现
- **P2P通信**：WebRTC或自定义协议的点对点通信
- **文件传输**：支持大文件分片传输和断点续传
- **权限管理**：细粒度的访问控制和安全认证
- **资源共享**：模型、知识库、配置的跨设备共享
- **分布式推理**：多设备协同推理，提升性能

**技术实现**
- **设备发现**：mdns库实现服务发现
- **P2P通信**：自定义协议 + TLS加密
- **文件传输**：分片传输 + 校验和验证
- **权限控制**：基于公钥的身份认证

**数据库设计**
```sql
-- 网络节点表
CREATE TABLE network_nodes (
    id TEXT PRIMARY KEY,
    name TEXT NOT NULL,
    device_type TEXT, -- 'desktop', 'laptop', 'server'
    ip_address TEXT NOT NULL,
    port INTEGER NOT NULL,
    public_key TEXT, -- 用于加密通信
    capabilities JSON, -- 设备能力描述
    status TEXT DEFAULT 'offline', -- 'online', 'offline', 'busy'
    last_seen DATETIME,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP
);

-- 共享资源表
CREATE TABLE shared_resources (
    id TEXT PRIMARY KEY,
    node_id TEXT REFERENCES network_nodes(id),
    resource_type TEXT NOT NULL, -- 'model', 'knowledge_base', 'config'
    resource_id TEXT NOT NULL,
    resource_name TEXT NOT NULL,
    permissions JSON, -- 权限配置
    is_public BOOLEAN DEFAULT FALSE,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP
);

-- 传输任务表
CREATE TABLE transfer_tasks (
    id TEXT PRIMARY KEY,
    source_node_id TEXT REFERENCES network_nodes(id),
    target_node_id TEXT REFERENCES network_nodes(id),
    resource_type TEXT NOT NULL,
    resource_id TEXT NOT NULL,
    total_size INTEGER,
    transferred_size INTEGER DEFAULT 0,
    status TEXT DEFAULT 'pending', -- 'pending', 'transferring', 'completed', 'failed'
    error_message TEXT,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
);
```

**API接口**
```typescript
// 设备管理
GET /api/network/discover
POST /api/network/connect
POST /api/network/disconnect
GET /api/network/nodes

// 资源共享
POST /api/network/share
GET /api/network/resources

// 传输管理
POST /api/network/transfer
GET /api/network/transfers
POST /api/network/transfers/{id}/pause
POST /api/network/transfers/{id}/resume
```

### 4.7 插件系统模块 (Plugin System)

**功能描述**
支持第三方插件扩展、插件市场、云端API集成等功能，提供安全的插件运行环境。

**核心特性**
- **WASM插件**：基于WebAssembly的安全插件运行环境
- **插件市场**：在线插件商店，支持搜索、安装、更新
- **API集成**：支持自定义API接口和JavaScript脚本
- **沙箱隔离**：插件运行在隔离环境中，确保系统安全
- **权限管理**：细粒度的插件权限控制
- **热插拔**：支持插件的动态加载和卸载

**技术实现**
- **WASM运行时**：wasmtime + 安全沙箱
- **权限系统**：基于能力的权限模型
- **插件API**：标准化插件接口定义
- **市场服务**：插件发现和分发服务

**数据库设计**
```sql
-- 插件表
CREATE TABLE plugins (
    id TEXT PRIMARY KEY,
    name TEXT NOT NULL,
    display_name TEXT NOT NULL,
    description TEXT,
    version TEXT NOT NULL,
    author TEXT,
    category TEXT, -- 'search', 'tool', 'api', 'ui'
    plugin_type TEXT NOT NULL, -- 'wasm', 'javascript', 'api'
    file_path TEXT,
    config_schema JSON,
    permissions JSON,
    status TEXT DEFAULT 'installed', -- 'installed', 'enabled', 'disabled', 'error'
    install_source TEXT, -- 'market', 'local', 'url'
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
);

-- 插件配置表
CREATE TABLE plugin_configs (
    id TEXT PRIMARY KEY,
    plugin_id TEXT REFERENCES plugins(id),
    config_key TEXT NOT NULL,
    config_value TEXT NOT NULL,
    is_encrypted BOOLEAN DEFAULT FALSE,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(plugin_id, config_key)
);

-- 插件市场表
CREATE TABLE plugin_market (
    id TEXT PRIMARY KEY,
    plugin_id TEXT NOT NULL,
    name TEXT NOT NULL,
    description TEXT,
    version TEXT NOT NULL,
    author TEXT,
    category TEXT,
    download_url TEXT NOT NULL,
    icon_url TEXT,
    screenshots JSON,
    rating REAL DEFAULT 0.0,
    download_count INTEGER DEFAULT 0,
    file_size INTEGER,
    checksum TEXT,
    is_verified BOOLEAN DEFAULT FALSE,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
);

-- 插件执行日志表
CREATE TABLE plugin_logs (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    plugin_id TEXT REFERENCES plugins(id),
    level TEXT NOT NULL,
    message TEXT NOT NULL,
    execution_time REAL,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP
);
```

**API接口**
```typescript
// 插件管理
GET /api/plugins
POST /api/plugins/install
DELETE /api/plugins/{id}
POST /api/plugins/{id}/enable
POST /api/plugins/{id}/disable

// 插件配置
GET /api/plugins/{id}/config
PUT /api/plugins/{id}/config

// 插件执行
POST /api/plugins/{id}/execute

// 插件市场
GET /api/plugins/market
GET /api/plugins/market/search
POST /api/plugins/market/{id}/install
```

### 4.8 系统管理模块 (System Management)

**功能描述**
提供系统监控、配置管理、日志管理等系统功能，确保应用稳定运行。

**核心特性**
- **性能监控**：实时监控CPU、内存、GPU使用情况
- **配置管理**：统一的配置文件管理和热更新
- **日志系统**：结构化日志记录和查询
- **自动更新**：应用自动更新和版本管理
- **数据备份**：自动数据备份和恢复机制
- **系统诊断**：健康检查和故障诊断

**技术实现**
- **监控系统**：tracing + 自定义指标收集
- **配置热更新**：文件监听 + 动态重载
- **备份系统**：增量备份 + 压缩存储
- **更新机制**：差分更新 + 签名验证

**数据库设计**
```sql
-- 系统配置表
CREATE TABLE system_configs (
    key TEXT PRIMARY KEY,
    value TEXT NOT NULL,
    type TEXT DEFAULT 'string', -- 'string', 'number', 'boolean', 'json'
    description TEXT,
    is_user_configurable BOOLEAN DEFAULT TRUE,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
);

-- 系统日志表
CREATE TABLE system_logs (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    level TEXT NOT NULL, -- 'debug', 'info', 'warn', 'error'
    module TEXT NOT NULL,
    message TEXT NOT NULL,
    metadata JSON,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP
);

-- 性能指标表
CREATE TABLE performance_metrics (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    metric_name TEXT NOT NULL,
    metric_value REAL NOT NULL,
    unit TEXT,
    tags JSON,
    recorded_at DATETIME DEFAULT CURRENT_TIMESTAMP
);

-- 备份记录表
CREATE TABLE backup_records (
    id TEXT PRIMARY KEY,
    backup_type TEXT NOT NULL, -- 'full', 'incremental'
    file_path TEXT NOT NULL,
    file_size INTEGER,
    checksum TEXT,
    status TEXT DEFAULT 'completed', -- 'completed', 'failed'
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP
);
```

**API接口**
```typescript
// 系统信息
GET /api/system/info
GET /api/system/performance
GET /api/system/health

// 配置管理
GET /api/system/configs
PUT /api/system/configs

// 日志管理
GET /api/system/logs

// 备份管理
POST /api/system/backup
GET /api/system/backups
POST /api/system/restore

// 更新管理
POST /api/system/update/check
POST /api/system/update/install
```

## 9. 详细开发功能点代码实现

### 9.1 前端核心组件代码

#### 9.1.1 主题切换组件 (ThemeToggle.vue)

```vue
<template>
  <div class="theme-toggle">
    <n-button
      :type="isDark ? 'primary' : 'default'"
      circle
      @click="toggleTheme"
      :title="$t('common.toggleTheme')"
    >
      <template #icon>
        <n-icon>
          <SunIcon v-if="isDark" />
          <MoonIcon v-else />
        </n-icon>
      </template>
    </n-button>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { NButton, NIcon } from 'naive-ui'
import { SunIcon, MoonIcon } from '@/components/icons'
import { useTheme } from '@/composables/useTheme'
import { useI18n } from 'vue-i18n'

const { t } = useI18n()
const { theme, toggleTheme } = useTheme()

const isDark = computed(() => theme.value === 'dark')
</script>

<style scoped>
.theme-toggle {
  display: flex;
  align-items: center;
  justify-content: center;
}
</style>
```

#### 9.1.2 聊天容器组件 (ChatContainer.vue)

```vue
<template>
  <div class="chat-container">
    <div class="chat-header">
      <div class="session-info">
        <h3 class="session-title" @click="editTitle">
          {{ currentSession?.title || $t('chat.newSession') }}
        </h3>
        <div class="session-meta">
          <n-tag size="small" type="info">
            {{ currentSession?.model_id || $t('chat.noModel') }}
          </n-tag>
          <span class="message-count">
            {{ messageCount }} {{ $t('chat.messages') }}
          </span>
        </div>
      </div>
      <div class="chat-actions">
        <n-button-group>
          <n-button @click="showModelSelector = true">
            <template #icon>
              <n-icon><RobotIcon /></n-icon>
            </template>
            {{ $t('chat.selectModel') }}
          </n-button>
          <n-button @click="showSettings = true">
            <template #icon>
              <n-icon><SettingsIcon /></n-icon>
            </template>
          </n-button>
          <n-button @click="clearSession" type="warning">
            <template #icon>
              <n-icon><ClearIcon /></n-icon>
            </template>
          </n-button>
          <n-button @click="exportSession">
            <template #icon>
              <n-icon><ExportIcon /></n-icon>
            </template>
          </n-button>
        </n-button-group>
      </div>
    </div>

    <div class="chat-content">
      <MessageList
        :messages="messages"
        :loading="isLoading"
        @regenerate="regenerateMessage"
        @copy="copyMessage"
        @delete="deleteMessage"
      />
    </div>

    <div class="chat-input">
      <ChatInput
        v-model="inputMessage"
        :disabled="isLoading"
        :attachments="attachments"
        @send="sendMessage"
        @attach="handleAttachment"
        @voice="handleVoiceInput"
      />
    </div>

    <!-- 模型选择器 -->
    <ModelSelector
      v-model:show="showModelSelector"
      @select="selectModel"
    />

    <!-- 设置面板 -->
    <ChatSettings
      v-model:show="showSettings"
      :session="currentSession"
      @update="updateSessionSettings"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { NButton, NButtonGroup, NIcon, NTag } from 'naive-ui'
import { useChat } from '@/composables/useChat'
import { useI18n } from 'vue-i18n'
import MessageList from './MessageList.vue'
import ChatInput from './ChatInput.vue'
import ModelSelector from './ModelSelector.vue'
import ChatSettings from './ChatSettings.vue'

const { t } = useI18n()
const {
  currentSession,
  messages,
  isLoading,
  messageCount,
  sendMessage: send,
  clearSession: clear,
  exportSession: exportSess,
  regenerateMessage: regenerate,
  deleteMessage: deleteMsg,
  updateSessionSettings: updateSettings
} = useChat()

const inputMessage = ref('')
const attachments = ref([])
const showModelSelector = ref(false)
const showSettings = ref(false)

const sendMessage = async () => {
  if (!inputMessage.value.trim()) return

  await send({
    content: inputMessage.value,
    attachments: attachments.value
  })

  inputMessage.value = ''
  attachments.value = []
}

const selectModel = (modelId: string) => {
  updateSettings({ model_id: modelId })
  showModelSelector.value = false
}

const handleAttachment = (file: File) => {
  attachments.value.push(file)
}

const handleVoiceInput = (audioBlob: Blob) => {
  // 处理语音输入
  console.log('Voice input:', audioBlob)
}

const editTitle = () => {
  // 编辑会话标题
}

const clearSession = () => {
  clear()
}

const exportSession = () => {
  exportSess()
}

const regenerateMessage = (messageId: string) => {
  regenerate(messageId)
}

const copyMessage = (content: string) => {
  navigator.clipboard.writeText(content)
}

const deleteMessage = (messageId: string) => {
  deleteMsg(messageId)
}

onMounted(() => {
  // 初始化聊天容器
})
</script>

<style scoped>
.chat-container {
  display: flex;
  flex-direction: column;
  height: 100%;
  background: var(--bg-color);
}

.chat-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 24px;
  border-bottom: 1px solid var(--border-color);
  background: var(--header-bg);
}

.session-info {
  flex: 1;
}

.session-title {
  margin: 0 0 8px 0;
  font-size: 18px;
  font-weight: 600;
  cursor: pointer;
  color: var(--text-color);
}

.session-meta {
  display: flex;
  align-items: center;
  gap: 12px;
  font-size: 14px;
  color: var(--text-color-secondary);
}

.chat-content {
  flex: 1;
  overflow: hidden;
}

.chat-input {
  padding: 16px 24px;
  border-top: 1px solid var(--border-color);
  background: var(--input-bg);
}
</style>
```

### 9.2 后端Rust核心实现

#### 9.2.1 聊天模块实现 (src/chat/mod.rs)

```rust
use serde::{Deserialize, Serialize};
use std::collections::HashMap;
use tokio::sync::RwLock;
use uuid::Uuid;

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ChatSession {
    pub id: String,
    pub title: String,
    pub model_id: Option<String>,
    pub system_prompt: Option<String>,
    pub temperature: f32,
    pub max_tokens: u32,
    pub created_at: chrono::DateTime<chrono::Utc>,
    pub updated_at: chrono::DateTime<chrono::Utc>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ChatMessage {
    pub id: String,
    pub session_id: String,
    pub role: MessageRole,
    pub content: String,
    pub attachments: Option<Vec<Attachment>>,
    pub tokens_used: Option<u32>,
    pub response_time: Option<f64>,
    pub created_at: chrono::DateTime<chrono::Utc>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum MessageRole {
    User,
    Assistant,
    System,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct Attachment {
    pub attachment_type: String,
    pub url: String,
    pub name: String,
    pub size: u64,
}

pub struct ChatManager {
    sessions: RwLock<HashMap<String, ChatSession>>,
    messages: RwLock<HashMap<String, Vec<ChatMessage>>>,
}

impl ChatManager {
    pub fn new() -> Self {
        Self {
            sessions: RwLock::new(HashMap::new()),
            messages: RwLock::new(HashMap::new()),
        }
    }

    pub async fn create_session(&self, title: Option<String>) -> Result<ChatSession, ChatError> {
        let session = ChatSession {
            id: Uuid::new_v4().to_string(),
            title: title.unwrap_or_else(|| "新对话".to_string()),
            model_id: None,
            system_prompt: None,
            temperature: 0.7,
            max_tokens: 2048,
            created_at: chrono::Utc::now(),
            updated_at: chrono::Utc::now(),
        };

        let mut sessions = self.sessions.write().await;
        sessions.insert(session.id.clone(), session.clone());

        Ok(session)
    }

    pub async fn get_session(&self, session_id: &str) -> Option<ChatSession> {
        let sessions = self.sessions.read().await;
        sessions.get(session_id).cloned()
    }

    pub async fn update_session(&self, session_id: &str, updates: SessionUpdate) -> Result<(), ChatError> {
        let mut sessions = self.sessions.write().await;
        if let Some(session) = sessions.get_mut(session_id) {
            if let Some(title) = updates.title {
                session.title = title;
            }
            if let Some(model_id) = updates.model_id {
                session.model_id = Some(model_id);
            }
            if let Some(system_prompt) = updates.system_prompt {
                session.system_prompt = Some(system_prompt);
            }
            if let Some(temperature) = updates.temperature {
                session.temperature = temperature;
            }
            if let Some(max_tokens) = updates.max_tokens {
                session.max_tokens = max_tokens;
            }
            session.updated_at = chrono::Utc::now();
            Ok(())
        } else {
            Err(ChatError::SessionNotFound)
        }
    }

    pub async fn add_message(&self, message: ChatMessage) -> Result<(), ChatError> {
        let mut messages = self.messages.write().await;
        messages
            .entry(message.session_id.clone())
            .or_insert_with(Vec::new)
            .push(message);
        Ok(())
    }

    pub async fn get_messages(&self, session_id: &str) -> Vec<ChatMessage> {
        let messages = self.messages.read().await;
        messages.get(session_id).cloned().unwrap_or_default()
    }

    pub async fn delete_session(&self, session_id: &str) -> Result<(), ChatError> {
        let mut sessions = self.sessions.write().await;
        let mut messages = self.messages.write().await;

        sessions.remove(session_id);
        messages.remove(session_id);

        Ok(())
    }
}

#[derive(Debug, Serialize, Deserialize)]
pub struct SessionUpdate {
    pub title: Option<String>,
    pub model_id: Option<String>,
    pub system_prompt: Option<String>,
    pub temperature: Option<f32>,
    pub max_tokens: Option<u32>,
}

#[derive(Debug, thiserror::Error)]
pub enum ChatError {
    #[error("Session not found")]
    SessionNotFound,
    #[error("Database error: {0}")]
    DatabaseError(String),
    #[error("Serialization error: {0}")]
    SerializationError(String),
}
```

#### 9.2.2 模型管理实现 (src/ai/model.rs)

```rust
use serde::{Deserialize, Serialize};
use std::path::PathBuf;
use tokio::fs;
use tokio::sync::RwLock;
use std::collections::HashMap;

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct Model {
    pub id: String,
    pub name: String,
    pub display_name: String,
    pub description: Option<String>,
    pub author: Option<String>,
    pub version: Option<String>,
    pub size: Option<u64>,
    pub quantization: Quantization,
    pub architecture: Architecture,
    pub context_length: u32,
    pub status: ModelStatus,
    pub local_path: Option<PathBuf>,
    pub download_url: Option<String>,
    pub huggingface_id: Option<String>,
    pub config: Option<serde_json::Value>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum Quantization {
    None,
    Q4_0,
    Q4_1,
    Q8_0,
    Q8_1,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum Architecture {
    Llama,
    Mistral,
    Qwen,
    ChatGLM,
    Baichuan,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum ModelStatus {
    Available,
    Downloading,
    Loaded,
    Error(String),
}

pub struct ModelManager {
    models: RwLock<HashMap<String, Model>>,
    loaded_model: RwLock<Option<String>>,
    models_dir: PathBuf,
}

impl ModelManager {
    pub fn new(models_dir: PathBuf) -> Self {
        Self {
            models: RwLock::new(HashMap::new()),
            loaded_model: RwLock::new(None),
            models_dir,
        }
    }

    pub async fn initialize(&self) -> Result<(), ModelError> {
        // 创建模型目录
        fs::create_dir_all(&self.models_dir).await?;

        // 扫描本地模型
        self.scan_local_models().await?;

        Ok(())
    }

    async fn scan_local_models(&self) -> Result<(), ModelError> {
        let mut dir = fs::read_dir(&self.models_dir).await?;
        let mut models = self.models.write().await;

        while let Some(entry) = dir.next_entry().await? {
            if entry.file_type().await?.is_dir() {
                let model_path = entry.path();
                if let Some(model) = self.load_model_config(&model_path).await? {
                    models.insert(model.id.clone(), model);
                }
            }
        }

        Ok(())
    }

    async fn load_model_config(&self, model_path: &PathBuf) -> Result<Option<Model>, ModelError> {
        let config_path = model_path.join("config.json");
        if !config_path.exists() {
            return Ok(None);
        }

        let config_content = fs::read_to_string(config_path).await?;
        let model: Model = serde_json::from_str(&config_content)?;

        Ok(Some(model))
    }

    pub async fn add_model(&self, model: Model) -> Result<(), ModelError> {
        let mut models = self.models.write().await;
        models.insert(model.id.clone(), model);
        Ok(())
    }

    pub async fn get_model(&self, model_id: &str) -> Option<Model> {
        let models = self.models.read().await;
        models.get(model_id).cloned()
    }

    pub async fn list_models(&self) -> Vec<Model> {
        let models = self.models.read().await;
        models.values().cloned().collect()
    }

    pub async fn load_model(&self, model_id: &str) -> Result<(), ModelError> {
        let models = self.models.read().await;
        let model = models.get(model_id).ok_or(ModelError::ModelNotFound)?;

        // 检查模型文件是否存在
        if let Some(local_path) = &model.local_path {
            if !local_path.exists() {
                return Err(ModelError::ModelFileNotFound);
            }
        } else {
            return Err(ModelError::ModelNotDownloaded);
        }

        // 卸载当前模型
        self.unload_current_model().await?;

        // 加载新模型
        // 这里应该调用实际的模型加载逻辑

        let mut loaded_model = self.loaded_model.write().await;
        *loaded_model = Some(model_id.to_string());

        Ok(())
    }

    pub async fn unload_current_model(&self) -> Result<(), ModelError> {
        let mut loaded_model = self.loaded_model.write().await;
        if loaded_model.is_some() {
            // 执行模型卸载逻辑
            *loaded_model = None;
        }
        Ok(())
    }

    pub async fn get_loaded_model(&self) -> Option<String> {
        let loaded_model = self.loaded_model.read().await;
        loaded_model.clone()
    }

    pub async fn delete_model(&self, model_id: &str) -> Result<(), ModelError> {
        let mut models = self.models.write().await;

        if let Some(model) = models.get(model_id) {
            // 如果模型正在使用，先卸载
            let loaded_model = self.loaded_model.read().await;
            if loaded_model.as_ref() == Some(model_id) {
                drop(loaded_model);
                self.unload_current_model().await?;
            }

            // 删除模型文件
            if let Some(local_path) = &model.local_path {
                if local_path.exists() {
                    fs::remove_dir_all(local_path).await?;
                }
            }

            models.remove(model_id);
        }

        Ok(())
    }
}

#[derive(Debug, thiserror::Error)]
pub enum ModelError {
    #[error("Model not found")]
    ModelNotFound,
    #[error("Model file not found")]
    ModelFileNotFound,
    #[error("Model not downloaded")]
    ModelNotDownloaded,
    #[error("IO error: {0}")]
    IoError(#[from] std::io::Error),
    #[error("JSON error: {0}")]
    JsonError(#[from] serde_json::Error),
    #[error("Model loading error: {0}")]
    LoadingError(String),
}
```

### 9.3 配置文件示例

#### 9.3.1 前端配置 (package.json)

```json
{
  "name": "ai-studio",
  "version": "1.0.0",
  "description": "AI Studio - 中文AI助手桌面应用",
  "type": "module",
  "scripts": {
    "dev": "vite",
    "build": "vue-tsc && vite build",
    "preview": "vite preview",
    "tauri": "tauri",
    "tauri:dev": "tauri dev",
    "tauri:build": "tauri build",
    "lint": "eslint . --ext .vue,.js,.jsx,.cjs,.mjs,.ts,.tsx,.cts,.mts --fix --ignore-path .gitignore",
    "format": "prettier --write src/",
    "test": "vitest",
    "test:ui": "vitest --ui"
  },
  "dependencies": {
    "@tauri-apps/api": "^2.0.0",
    "@tauri-apps/plugin-shell": "^2.0.0",
    "@vueuse/core": "^10.5.0",
    "@vueuse/markdown": "^10.5.0",
    "echarts": "^5.4.3",
    "naive-ui": "^2.35.0",
    "pinia": "^2.1.7",
    "prismjs": "^1.29.0",
    "vue": "^3.3.8",
    "vue-i18n": "^9.8.0",
    "vue-router": "^4.2.5"
  },
  "devDependencies": {
    "@iconify/vue": "^4.1.1",
    "@tauri-apps/cli": "^2.0.0",
    "@types/node": "^20.9.0",
    "@types/prismjs": "^1.26.3",
    "@typescript-eslint/eslint-plugin": "^6.12.0",
    "@typescript-eslint/parser": "^6.12.0",
    "@vitejs/plugin-vue": "^4.5.0",
    "@vue/eslint-config-prettier": "^8.0.0",
    "@vue/eslint-config-typescript": "^12.0.0",
    "autoprefixer": "^10.4.16",
    "eslint": "^8.54.0",
    "eslint-plugin-vue": "^9.18.1",
    "postcss": "^8.4.31",
    "prettier": "^3.1.0",
    "sass": "^1.69.5",
    "tailwindcss": "^3.3.5",
    "typescript": "^5.2.2",
    "unplugin-auto-import": "^0.16.7",
    "unplugin-vue-components": "^0.25.2",
    "vite": "^5.0.0",
    "vitest": "^0.34.6",
    "vue-tsc": "^1.8.22"
  }
}
```

#### 9.3.2 后端配置 (Cargo.toml)

```toml
[package]
name = "ai-studio"
version = "1.0.0"
description = "AI Studio - 中文AI助手桌面应用"
authors = ["AI Studio Team"]
license = "MIT"
repository = "https://github.com/ai-studio/ai-studio"
edition = "2021"

[build-dependencies]
tauri-build = { version = "2.0", features = [] }

[dependencies]
# Tauri 核心
tauri = { version = "2.0", features = ["shell-open"] }
tauri-plugin-shell = "2.0"

# 异步运行时
tokio = { version = "1.35", features = ["full"] }

# 序列化
serde = { version = "1.0", features = ["derive"] }
serde_json = "1.0"

# 数据库
sqlx = { version = "0.7", features = ["runtime-tokio-rustls", "sqlite", "chrono", "uuid"] }

# HTTP 客户端
reqwest = { version = "0.11", features = ["json", "stream"] }

# 错误处理
thiserror = "1.0"
anyhow = "1.0"

# 日志
tracing = "0.1"
tracing-subscriber = { version = "0.3", features = ["env-filter"] }

# 加密
aes-gcm = "0.10"
ring = "0.17"

# 文档处理
pdf-extract = "0.7"
docx-rs = "0.4"
calamine = "0.22"

# 图像处理
image = "0.24"
imageproc = "0.23"

# 音频处理
rodio = "0.17"

# 网络发现
mdns = "3.0"
tokio-tungstenite = "0.20"

# 工具库
uuid = { version = "1.6", features = ["v4", "serde"] }
chrono = { version = "0.4", features = ["serde"] }
url = "2.4"
base64 = "0.21"

# AI 推理
candle-core = "0.3"
candle-nn = "0.3"
candle-transformers = "0.3"

[features]
default = ["custom-protocol"]
custom-protocol = ["tauri/custom-protocol"]

[profile.release]
panic = "abort"
codegen-units = 1
lto = true
opt-level = "s"
strip = true
```

#### 9.3.3 Tauri 配置 (tauri.conf.json)

```json
{
  "$schema": "https://schema.tauri.app/config/2.0.0",
  "productName": "AI Studio",
  "version": "1.0.0",
  "identifier": "com.aistudio.app",
  "build": {
    "beforeDevCommand": "npm run dev",
    "beforeBuildCommand": "npm run build",
    "devUrl": "http://localhost:1420",
    "frontendDist": "../dist"
  },
  "app": {
    "windows": [
      {
        "title": "AI Studio",
        "width": 1200,
        "height": 800,
        "minWidth": 800,
        "minHeight": 600,
        "resizable": true,
        "fullscreen": false,
        "decorations": true,
        "transparent": false,
        "alwaysOnTop": false,
        "center": true
      }
    ],
    "security": {
      "csp": null
    }
  },
  "bundle": {
    "active": true,
    "targets": "all",
    "icon": [
      "icons/32x32.png",
      "icons/128x128.png",
      "icons/<EMAIL>",
      "icons/icon.icns",
      "icons/icon.ico"
    ],
    "resources": [],
    "externalBin": [],
    "copyright": "",
    "category": "DeveloperTool",
    "shortDescription": "AI Studio - 中文AI助手桌面应用",
    "longDescription": "AI Studio 是一个基于 Vue3 + TypeScript + Vite + Tauri 2.x 技术栈的中文AI助手桌面应用，专为 Windows/macOS 平台设计。",
    "deb": {
      "depends": []
    },
    "macOS": {
      "frameworks": [],
      "minimumSystemVersion": "10.13",
      "exceptionDomain": ""
    },
    "windows": {
      "certificateThumbprint": null,
      "digestAlgorithm": "sha256",
      "timestampUrl": ""
    }
  },
  "plugins": {
    "shell": {
      "open": true
    }
  }
}
```

#### 9.3.4 Vite 配置 (vite.config.ts)

```typescript
import { defineConfig } from 'vite'
import vue from '@vitejs/plugin-vue'
import { resolve } from 'path'
import AutoImport from 'unplugin-auto-import/vite'
import Components from 'unplugin-vue-components/vite'
import { NaiveUiResolver } from 'unplugin-vue-components/resolvers'

const host = process.env.TAURI_DEV_HOST

export default defineConfig(async () => ({
  plugins: [
    vue(),
    AutoImport({
      imports: [
        'vue',
        'vue-router',
        'vue-i18n',
        '@vueuse/core',
        {
          'naive-ui': [
            'useDialog',
            'useMessage',
            'useNotification',
            'useLoadingBar'
          ]
        }
      ],
      dts: true,
      dirs: [
        'src/composables',
        'src/stores',
        'src/utils'
      ]
    }),
    Components({
      resolvers: [NaiveUiResolver()],
      dts: true
    })
  ],
  resolve: {
    alias: {
      '@': resolve(__dirname, 'src')
    }
  },
  css: {
    preprocessorOptions: {
      scss: {
        additionalData: `@import "@/assets/styles/variables.scss";`
      }
    }
  },
  clearScreen: false,
  server: {
    port: 1420,
    strictPort: true,
    host: host || false,
    hmr: host
      ? {
          protocol: 'ws',
          host,
          port: 1421
        }
      : undefined,
    watch: {
      ignored: ['**/src-tauri/**']
    }
  },
  build: {
    target: process.env.TAURI_PLATFORM == 'windows' ? 'chrome105' : 'safari13',
    minify: !process.env.TAURI_DEBUG ? 'esbuild' : false,
    sourcemap: !!process.env.TAURI_DEBUG,
    rollupOptions: {
      output: {
        manualChunks: {
          'naive-ui': ['naive-ui'],
          'vue-vendor': ['vue', 'vue-router', 'vue-i18n'],
          'utils': ['@vueuse/core', 'echarts']
        }
      }
    }
  }
}))
```

## 7.2 详细数据结构定义

### 7.2.1 聊天相关数据结构

```typescript
// 扩展的聊天会话结构
interface ChatSession {
  id: string;
  title: string;
  model_id?: string;
  system_prompt?: string;
  temperature: number;
  max_tokens: number;
  created_at: string;
  updated_at: string;
  last_message_at?: string;
  message_count: number;

  // 统计信息
  statistics: {
    total_tokens: number;       // 总token消耗
    total_cost?: number;        // 总成本(付费模型)
    avg_response_time: number;  // 平均响应时间(ms)
    rag_queries: number;        // RAG查询次数
  };

  // 会话管理
  tags: string[];              // 会话标签
  is_archived: boolean;        // 是否归档
  is_pinned: boolean;          // 是否置顶
  knowledge_base_ids: string[]; // 关联的知识库
  context_window: number;      // 上下文窗口大小
}

// 扩展的消息结构
interface ChatMessage {
  id: string;
  session_id: string;
  role: 'user' | 'assistant' | 'system';
  content: string;
  attachments?: Attachment[];
  tokens_used?: number;
  response_time?: number;
  created_at: string;

  // 消息元数据
  metadata: {
    model_used?: string;        // 使用的模型
    temperature?: number;       // 生成温度
    finish_reason?: string;     // 完成原因
    rag_sources?: RAGSource[];  // RAG来源
    confidence?: number;        // 置信度
  };

  // 消息状态
  status: 'pending' | 'completed' | 'failed' | 'cancelled';
  error_message?: string;
  is_edited: boolean;
  edit_history?: MessageEdit[];
}

interface Attachment {
  id: string;
  type: 'image' | 'audio' | 'video' | 'document';
  name: string;
  url: string;
  size: number;
  mime_type: string;
  metadata?: any;
}

interface RAGSource {
  document_id: string;
  document_name: string;
  chunk_id: string;
  content: string;
  score: number;
  page_number?: number;
}

interface MessageEdit {
  timestamp: string;
  old_content: string;
  new_content: string;
  reason?: string;
}
```

### 7.2.2 模型相关数据结构

```typescript
// 详细的模型信息结构
interface ModelInfo {
  id: string;
  name: string;
  display_name: string;
  description: string;
  provider: 'local' | 'openai' | 'anthropic' | 'google' | 'custom';
  type: 'chat' | 'completion' | 'embedding' | 'multimodal';

  // 本地模型信息
  local_info?: {
    file_path: string;
    file_size: number;
    quantization: 'none' | 'q4_0' | 'q4_1' | 'q8_0' | 'q8_1';
    architecture: 'llama' | 'mistral' | 'qwen' | 'chatglm' | 'baichuan';
    vocabulary: number;
    checksum: string;
  };

  // 性能要求
  requirements: {
    min_memory: number;         // 最小内存要求(MB)
    recommended_memory: number; // 推荐内存(MB)
    min_vram?: number;          // 最小显存要求(MB)
    cpu_threads: number;        // 推荐CPU线程数
    supported_os: string[];     // 支持的操作系统
  };

  // 模型能力
  capabilities: {
    max_context_length: number;
    supported_languages: string[];
    supports_chat_mode: boolean;
    supports_completion: boolean;
    supports_rag: boolean;
    supports_images: boolean;
    supports_audio: boolean;
    supports_video: boolean;
    supports_function_calling: boolean;
  };

  // 运行状态
  status: 'available' | 'downloading' | 'loading' | 'loaded' | 'error' | 'unloaded';
  is_local: boolean;
  is_loaded: boolean;
  loaded_at?: string;

  // 下载信息
  download_info?: {
    url: string;
    progress: number;          // 0-100
    speed: number;             // KB/s
    eta: number;               // 预计剩余时间(秒)
    downloaded_size: number;   // 已下载大小(字节)
    total_size: number;        // 总大小(字节)
  };

  // 性能指标
  performance?: {
    tokens_per_second: number;
    memory_usage: number;      // MB
    vram_usage?: number;       // MB
    cpu_usage: number;         // 百分比
    temperature: number;       // 运行温度
    power_consumption?: number; // 功耗(W)
  };

  // 配置信息
  config: ModelConfig;

  // 元数据
  metadata: {
    author: string;
    license: string;
    version: string;
    release_date: string;
    homepage?: string;
    repository?: string;
    paper_url?: string;
    tags: string[];
  };
}

interface ModelConfig {
  temperature: number;
  max_tokens: number;
  top_p: number;
  top_k: number;
  repeat_penalty: number;
  context_length: number;
  batch_size: number;
  threads: number;
  gpu_layers?: number;
}
```

### 7.2.3 知识库相关数据结构

```typescript
// 详细的知识库结构
interface KnowledgeBase {
  id: string;
  name: string;
  description: string;
  embedding_model: string;
  created_at: string;
  updated_at: string;
  last_indexed_at?: string;

  // 统计信息
  statistics: {
    document_count: number;
    vector_count: number;
    total_size: number;        // 总大小(字节)
    avg_chunk_size: number;    // 平均块大小
    indexing_progress: number; // 索引进度(0-100)
    search_count: number;      // 搜索次数
    last_search_at?: string;
  };

  // 知识库设置
  settings: KnowledgeBaseSettings;
  status: 'active' | 'indexing' | 'error' | 'archived';

  // 共享功能
  is_shared: boolean;
  share_settings?: {
    access_level: 'public' | 'private' | 'password';
    password?: string;
    allow_download: boolean;
    allow_search: boolean;
  };

  // 权限管理
  permissions: {
    owner: string;
    readers: string[];
    writers: string[];
    admins: string[];
  };

  // 元数据
  metadata: {
    tags: string[];
    category: string;
    language: string;
    source: string;
    version: string;
  };
}

interface KnowledgeBaseSettings {
  // 文本分块设置
  chunk_size: number;
  chunk_overlap: number;
  chunk_strategy: 'fixed' | 'semantic' | 'sentence';

  // 搜索设置
  similarity_threshold: number;
  max_results: number;
  enable_reranking: boolean;
  reranking_model?: string;

  // 处理设置
  enable_summary: boolean;
  summary_model?: string;
  enable_keywords: boolean;
  enable_ocr: boolean;

  // 索引设置
  indexing_batch_size: number;
  enable_incremental_index: boolean;
  auto_reindex: boolean;
  reindex_interval: number;

  // 存储设置
  compression_enabled: boolean;
  encryption_enabled: boolean;
  backup_enabled: boolean;
  retention_days: number;
}

// 文档结构
interface Document {
  id: string;
  kb_id: string;
  name: string;
  type: 'pdf' | 'docx' | 'txt' | 'md' | 'html' | 'csv' | 'xlsx';
  size: number;
  path: string;
  status: 'processing' | 'completed' | 'failed' | 'pending';
  chunks_count: number;
  created_at: string;
  updated_at: string;

  // 文档元数据
  metadata: {
    author?: string;
    title?: string;
    subject?: string;
    keywords?: string[];
    language?: string;
    page_count?: number;
    word_count?: number;
    character_count?: number;
  };

  // 处理结果
  processing_result?: {
    extracted_text: string;
    images_count: number;
    tables_count: number;
    links_count: number;
    processing_time: number;
    quality_score: number;
  };
}

// 文档块结构
interface DocumentChunk {
  id: string;
  document_id: string;
  content: string;
  metadata: {
    page_number?: number;
    chunk_index: number;
    start_char: number;
    end_char: number;
    section_title?: string;
    keywords?: string[];
  };
  embedding?: number[];
  created_at: string;
}
```

### 7.2.4 插件系统数据结构

```typescript
// 插件信息结构
interface Plugin {
  id: string;
  name: string;
  display_name: string;
  description: string;
  version: string;
  author: string;
  category: 'search' | 'tool' | 'api' | 'ui' | 'integration';
  plugin_type: 'wasm' | 'javascript' | 'api' | 'native';

  // 插件文件信息
  file_path?: string;
  file_size?: number;
  checksum?: string;

  // 插件能力
  capabilities: {
    // 联网功能
    network_search: boolean;      // 联网搜索
    network_qa: boolean;          // 联网问答
    network_memory: boolean;      // 联网记忆
    network_tools: boolean;       // 联网工具

    // 本地文件操作
    file_upload: boolean;         // 本地文件上传
    file_read: boolean;           // 本地文件读取
    file_memory: boolean;         // 本地文件记忆

    // API集成
    custom_api: boolean;          // 自定义API接口

    // 脚本执行
    javascript: boolean;          // JavaScript脚本

    // 系统集成
    system_access: boolean;       // 系统访问权限
    process_control: boolean;     // 进程控制
  };

  // 权限管理
  permissions: {
    network: boolean;            // 网络访问权限
    filesystem: boolean;         // 文件系统权限
    system: boolean;             // 系统权限
    api: string[];               // 允许访问的API列表
  };

  // 配置模式
  config_schema?: JSONSchema;

  // 插件状态
  status: 'installed' | 'enabled' | 'disabled' | 'error' | 'updating';
  install_source: 'market' | 'local' | 'url' | 'development';

  // 运行时信息
  runtime_info?: {
    memory_usage: number;
    cpu_usage: number;
    execution_count: number;
    last_execution: string;
    error_count: number;
    last_error?: string;
  };

  created_at: string;
  updated_at: string;
}

// 插件配置
interface PluginConfig {
  id: string;
  plugin_id: string;
  config_key: string;
  config_value: any;
  is_encrypted: boolean;
  updated_at: string;
}

// 插件市场信息
interface PluginMarketInfo {
  id: string;
  plugin_id: string;
  name: string;
  description: string;
  version: string;
  author: string;
  category: string;
  download_url: string;
  icon_url?: string;
  screenshots?: string[];
  rating: number;
  download_count: number;
  file_size: number;
  checksum: string;
  is_verified: boolean;
  is_featured: boolean;

  // 兼容性信息
  compatibility: {
    min_app_version: string;
    max_app_version?: string;
    supported_os: string[];
    required_permissions: string[];
  };

  // 更新信息
  changelog?: string;
  release_notes?: string;

  created_at: string;
  updated_at: string;
}

// 插件执行日志
interface PluginLog {
  id: number;
  plugin_id: string;
  level: 'debug' | 'info' | 'warn' | 'error';
  message: string;
  execution_time?: number;
  context?: any;
  created_at: string;
}
```

### 7.2.5 插件接口和示例

```typescript
// 插件基础接口
interface Plugin {
  id: string;
  name: string;
  version: string;
  description: string;

  activate(context: PluginContext): void;
  deactivate?(): void;
}

// 插件上下文接口
interface PluginContext {
  // 注册命令
  registerCommand(command: Command): void;

  // 添加菜单项
  addMenuItem(item: MenuItem): void;

  // 扩展UI组件
  extendComponent(componentName: string, extension: ComponentExtension): void;

  // 访问应用数据
  getData(key: string): any;
  setData(key: string, value: any): void;

  // 调用主应用API
  callApi(endpoint: string, payload: any): Promise<any>;

  // 事件系统
  on(event: string, handler: Function): void;
  emit(event: string, data: any): void;

  // 网络访问
  fetch(url: string, options?: RequestInit): Promise<Response>;

  // 文件系统访问
  readFile(path: string): Promise<string>;
  writeFile(path: string, content: string): Promise<void>;

  // 通知系统
  showNotification(message: string, type?: 'info' | 'success' | 'warning' | 'error'): void;
}

interface Command {
  id: string;
  name: string;
  description?: string;
  handler: Function;
  shortcut?: string;
}

interface MenuItem {
  id: string;
  label: string;
  icon?: string;
  action: Function;
  submenu?: MenuItem[];
}

interface ComponentExtension {
  template?: string;
  script?: string;
  style?: string;
  props?: any;
}
```

**联网搜索插件示例**
```typescript
class WebSearchPlugin implements Plugin {
  id = 'web-search';
  name = '联网搜索';
  version = '1.0.0';
  description = '提供联网搜索功能，支持多个搜索引擎';

  private context: PluginContext;

  activate(context: PluginContext) {
    this.context = context;

    // 注册搜索命令
    context.registerCommand({
      id: 'web-search',
      name: '联网搜索',
      description: '在网络上搜索信息',
      handler: this.performSearch.bind(this),
      shortcut: 'Ctrl+Shift+S'
    });

    // 添加菜单项
    context.addMenuItem({
      id: 'search-menu',
      label: '搜索',
      icon: 'search',
      action: () => this.showSearchDialog(),
      submenu: [
        {
          id: 'google-search',
          label: 'Google搜索',
          action: () => this.performSearch('', 'google')
        },
        {
          id: 'bing-search',
          label: 'Bing搜索',
          action: () => this.performSearch('', 'bing')
        }
      ]
    });

    // 扩展聊天输入组件
    context.extendComponent('ChatInput', {
      template: `
        <div class="search-actions">
          <button @click="performWebSearch" class="search-btn">
            <icon name="search" />
            联网搜索
          </button>
        </div>
      `,
      script: `
        methods: {
          performWebSearch() {
            this.$emit('web-search', this.inputValue);
          }
        }
      `,
      style: `
        .search-btn {
          padding: 8px 12px;
          background: #007bff;
          color: white;
          border: none;
          border-radius: 4px;
          cursor: pointer;
        }
      `
    });

    // 监听聊天事件
    context.on('chat:message:send', this.handleChatMessage.bind(this));
  }

  async performSearch(query: string, engine: string = 'google'): Promise<SearchResult[]> {
    try {
      const searchUrl = this.buildSearchUrl(query, engine);
      const response = await this.context.fetch(searchUrl);
      const results = await this.parseSearchResults(response, engine);

      // 通知用户搜索完成
      this.context.showNotification(`找到 ${results.length} 个搜索结果`, 'success');

      return results;
    } catch (error) {
      this.context.showNotification('搜索失败: ' + error.message, 'error');
      throw error;
    }
  }

  private buildSearchUrl(query: string, engine: string): string {
    const encodedQuery = encodeURIComponent(query);
    const urls = {
      google: `https://www.googleapis.com/customsearch/v1?key=${this.getApiKey('google')}&cx=${this.getSearchEngineId('google')}&q=${encodedQuery}`,
      bing: `https://api.bing.microsoft.com/v7.0/search?q=${encodedQuery}`,
      duckduckgo: `https://api.duckduckgo.com/?q=${encodedQuery}&format=json&no_html=1`
    };

    return urls[engine] || urls.google;
  }

  private async parseSearchResults(response: Response, engine: string): Promise<SearchResult[]> {
    const data = await response.json();

    switch (engine) {
      case 'google':
        return data.items?.map(item => ({
          title: item.title,
          url: item.link,
          snippet: item.snippet,
          source: 'Google'
        })) || [];

      case 'bing':
        return data.webPages?.value?.map(item => ({
          title: item.name,
          url: item.url,
          snippet: item.snippet,
          source: 'Bing'
        })) || [];

      default:
        return [];
    }
  }

  private getApiKey(service: string): string {
    return this.context.getData(`api_keys.${service}`) || '';
  }

  private getSearchEngineId(service: string): string {
    return this.context.getData(`search_engines.${service}.id`) || '';
  }

  private showSearchDialog(): void {
    // 显示搜索对话框的实现
  }

  private handleChatMessage(data: any): void {
    // 检查消息是否包含搜索意图
    if (this.detectSearchIntent(data.message)) {
      this.suggestWebSearch(data.message);
    }
  }

  private detectSearchIntent(message: string): boolean {
    const searchKeywords = ['搜索', '查找', '最新', '新闻', '当前', '现在'];
    return searchKeywords.some(keyword => message.includes(keyword));
  }

  private suggestWebSearch(message: string): void {
    this.context.showNotification('检测到搜索意图，是否进行联网搜索？', 'info');
  }

  deactivate(): void {
    // 清理资源
    this.context = null;
  }
}

interface SearchResult {
  title: string;
  url: string;
  snippet: string;
  source: string;
  timestamp?: string;
}
```

**文件处理插件示例**
```typescript
class FileProcessorPlugin implements Plugin {
  id = 'file-processor';
  name = '文件处理器';
  version = '1.0.0';
  description = '处理本地文件，支持多种格式的读取和分析';

  private context: PluginContext;

  activate(context: PluginContext) {
    this.context = context;

    // 注册文件处理命令
    context.registerCommand({
      id: 'process-file',
      name: '处理文件',
      description: '分析和处理本地文件',
      handler: this.processFile.bind(this),
      shortcut: 'Ctrl+O'
    });

    // 扩展文件上传组件
    context.extendComponent('FileUpload', {
      template: `
        <div class="file-processor">
          <input type="file" @change="handleFileSelect" multiple accept=".txt,.md,.pdf,.docx,.csv">
          <button @click="processSelectedFiles">智能分析</button>
        </div>
      `,
      script: `
        data() {
          return {
            selectedFiles: []
          };
        },
        methods: {
          handleFileSelect(event) {
            this.selectedFiles = Array.from(event.target.files);
          },
          async processSelectedFiles() {
            for (const file of this.selectedFiles) {
              await this.processFile(file);
            }
          }
        }
      `
    });
  }

  async processFile(file: File | string): Promise<ProcessingResult> {
    try {
      let content: string;
      let metadata: FileMetadata;

      if (typeof file === 'string') {
        // 处理文件路径
        content = await this.context.readFile(file);
        metadata = await this.extractMetadata(file);
      } else {
        // 处理File对象
        content = await this.readFileContent(file);
        metadata = this.extractFileMetadata(file);
      }

      const analysis = await this.analyzeContent(content, metadata);
      const summary = await this.generateSummary(content);
      const keywords = await this.extractKeywords(content);

      const result: ProcessingResult = {
        content,
        metadata,
        analysis,
        summary,
        keywords,
        processed_at: new Date().toISOString()
      };

      // 保存处理结果
      await this.saveProcessingResult(result);

      this.context.showNotification(`文件处理完成: ${metadata.name}`, 'success');

      return result;
    } catch (error) {
      this.context.showNotification(`文件处理失败: ${error.message}`, 'error');
      throw error;
    }
  }

  private async readFileContent(file: File): Promise<string> {
    return new Promise((resolve, reject) => {
      const reader = new FileReader();
      reader.onload = (e) => resolve(e.target?.result as string);
      reader.onerror = (e) => reject(new Error('文件读取失败'));
      reader.readAsText(file);
    });
  }

  private extractFileMetadata(file: File): FileMetadata {
    return {
      name: file.name,
      size: file.size,
      type: file.type,
      last_modified: new Date(file.lastModified).toISOString()
    };
  }

  private async extractMetadata(filePath: string): Promise<FileMetadata> {
    // 调用主应用API获取文件元数据
    return await this.context.callApi('file/metadata', { path: filePath });
  }

  private async analyzeContent(content: string, metadata: FileMetadata): Promise<ContentAnalysis> {
    const wordCount = content.split(/\s+/).length;
    const charCount = content.length;
    const lineCount = content.split('\n').length;
    const language = this.detectLanguage(content);
    const readingTime = Math.ceil(wordCount / 200); // 假设每分钟200词

    return {
      word_count: wordCount,
      character_count: charCount,
      line_count: lineCount,
      language,
      reading_time_minutes: readingTime,
      file_type: this.getFileType(metadata.name),
      complexity_score: this.calculateComplexity(content)
    };
  }

  private async generateSummary(content: string): Promise<string> {
    // 调用主应用的AI API生成摘要
    const response = await this.context.callApi('ai/summarize', {
      text: content,
      max_length: 200
    });

    return response.summary;
  }

  private async extractKeywords(content: string): Promise<string[]> {
    // 简单的关键词提取算法
    const words = content.toLowerCase()
      .replace(/[^\w\s]/g, '')
      .split(/\s+/)
      .filter(word => word.length > 3);

    const wordFreq = {};
    words.forEach(word => {
      wordFreq[word] = (wordFreq[word] || 0) + 1;
    });

    return Object.entries(wordFreq)
      .sort(([,a], [,b]) => b - a)
      .slice(0, 10)
      .map(([word]) => word);
  }

  private detectLanguage(content: string): string {
    // 简单的语言检测
    const chineseChars = content.match(/[\u4e00-\u9fff]/g);
    const englishChars = content.match(/[a-zA-Z]/g);

    if (chineseChars && chineseChars.length > (englishChars?.length || 0)) {
      return 'zh-CN';
    }
    return 'en-US';
  }

  private getFileType(filename: string): string {
    const ext = filename.split('.').pop()?.toLowerCase();
    const typeMap = {
      'txt': 'text',
      'md': 'markdown',
      'pdf': 'pdf',
      'docx': 'word',
      'csv': 'spreadsheet',
      'json': 'json',
      'xml': 'xml'
    };

    return typeMap[ext] || 'unknown';
  }

  private calculateComplexity(content: string): number {
    // 简单的复杂度计算
    const sentences = content.split(/[.!?]+/).length;
    const words = content.split(/\s+/).length;
    const avgWordsPerSentence = words / sentences;

    if (avgWordsPerSentence > 20) return 3; // 高复杂度
    if (avgWordsPerSentence > 15) return 2; // 中复杂度
    return 1; // 低复杂度
  }

  private async saveProcessingResult(result: ProcessingResult): Promise<void> {
    await this.context.callApi('file/save-processing-result', result);
  }

  deactivate(): void {
    this.context = null;
  }
}

interface FileMetadata {
  name: string;
  size: number;
  type: string;
  last_modified: string;
}

interface ContentAnalysis {
  word_count: number;
  character_count: number;
  line_count: number;
  language: string;
  reading_time_minutes: number;
  file_type: string;
  complexity_score: number;
}

interface ProcessingResult {
  content: string;
  metadata: FileMetadata;
  analysis: ContentAnalysis;
  summary: string;
  keywords: string[];
  processed_at: string;
}
```

## 8. 系统流程图设计

### 8.1 用户认证和会话管理流程

```
用户启动应用
    ↓
检查本地配置
    ↓
初始化主题和语言设置
    ↓
加载用户偏好设置
    ↓
显示主界面
    ↓
用户选择功能模块
    ↓
[聊天] → 创建/选择会话 → 发送消息 → AI处理 → 流式返回结果
[知识库] → 选择知识库 → 上传/搜索文档 → 处理结果
[模型管理] → 浏览/搜索模型 → 下载/加载模型 → 配置参数
[远程配置] → 配置API → 测试连接 → 保存设置
[网络共享] → 发现设备 → 建立连接 → 共享资源
[多模态] → 选择处理类型 → 上传文件 → 处理 → 返回结果
```

### 8.2 聊天模块核心流程

```
用户输入消息
    ↓
验证输入内容
    ↓
检查会话状态
    ↓
处理附件（如有）
    ↓
构建请求上下文
    ↓
选择推理引擎
    ↓
[本地模型] → 加载模型 → 本地推理 → 流式输出
[远程API] → 调用API → 处理响应 → 流式输出
    ↓
保存消息到数据库
    ↓
更新会话状态
    ↓
触发前端更新
```

### 8.3 知识库处理流程

```
用户上传文档
    ↓
文件格式检测
    ↓
[PDF] → PDF解析器 → 提取文本和图片
[Word] → DOCX解析器 → 提取文本和格式
[Excel] → 表格解析器 → 提取数据
[Markdown] → MD解析器 → 提取结构化内容
    ↓
文本预处理
    ↓
智能分块处理
    ↓
生成文本向量
    ↓
存储到向量数据库
    ↓
更新文档索引
    ↓
通知处理完成
```

### 8.4 模型下载和管理流程

```
用户搜索模型
    ↓
查询HuggingFace API
    ↓
显示搜索结果
    ↓
用户选择下载
    ↓
检查本地存储空间
    ↓
选择下载源（官方/镜像）
    ↓
创建下载任务
    ↓
分片下载文件
    ↓
验证文件完整性
    ↓
解压和安装模型
    ↓
更新模型数据库
    ↓
通知下载完成
```

## 5. 项目结构

### 5.1 前端目录结构 (src/)

```
src/
├── api/                          # API服务层 - 封装所有后台接口调用
│   ├── base.ts                   # 基础API配置和拦截器
│   ├── chat.ts                   # 聊天相关API接口
│   ├── knowledge.ts              # 知识库相关API接口
│   ├── model.ts                  # 模型管理相关API接口
│   ├── multimodal.ts             # 多模态处理相关API接口
│   ├── network.ts                # 网络共享相关API接口
│   ├── remote.ts                 # 远程配置相关API接口
│   ├── system.ts                 # 系统管理相关API接口
│   ├── plugin.ts                 # 插件系统相关API接口
│   └── index.ts                  # API模块统一导出
├── assets/                       # 静态资源文件
│   ├── images/                   # 图片资源
│   │   ├── icons/                # 图标文件
│   │   ├── logos/                # Logo文件
│   │   └── backgrounds/          # 背景图片
│   ├── fonts/                    # 字体文件
│   └── styles/                   # 全局样式文件
│       ├── variables.scss        # SCSS变量定义
│       ├── mixins.scss           # SCSS混入函数
│       ├── reset.scss            # 样式重置
│       ├── themes/               # 主题样式
│       │   ├── light.scss        # 浅色主题
│       │   └── dark.scss         # 深色主题
│       └── global.scss           # 全局样式
├── components/                   # 可复用组件 - 按功能模块组织
│   ├── common/                   # 通用组件
│   │   ├── AppHeader.vue         # 应用头部导航组件
│   │   ├── AppSidebar.vue        # 应用侧边栏组件
│   │   ├── AppFooter.vue         # 应用底部组件
│   │   ├── LoadingSpinner.vue    # 加载动画组件
│   │   ├── ErrorBoundary.vue     # 错误边界组件
│   │   ├── ConfirmDialog.vue     # 确认对话框组件
│   │   ├── FileUpload.vue        # 文件上传组件
│   │   ├── ProgressBar.vue       # 进度条组件
│   │   ├── SearchInput.vue       # 搜索输入组件
│   │   ├── ThemeToggle.vue       # 主题切换组件
│   │   ├── LanguageSwitch.vue    # 语言切换组件
│   │   └── UserDropdown.vue      # 用户下拉菜单组件
│   ├── chat/                     # 聊天模块组件
│   │   ├── ChatContainer.vue     # 聊天容器组件
│   │   ├── ChatSidebar.vue       # 聊天侧边栏组件
│   │   ├── ChatInput.vue         # 聊天输入组件
│   │   ├── ChatMessage.vue       # 聊天消息组件
│   │   ├── MessageList.vue       # 消息列表组件
│   │   ├── SessionList.vue       # 会话列表组件
│   │   ├── SessionItem.vue       # 会话项组件
│   │   ├── MarkdownRenderer.vue  # Markdown渲染组件
│   │   ├── CodeBlock.vue         # 代码块组件
│   │   ├── AttachmentPreview.vue # 附件预览组件
│   │   └── TypingIndicator.vue   # 输入状态指示器
│   ├── knowledge/                # 知识库模块组件
│   │   ├── KnowledgeContainer.vue # 知识库容器组件
│   │   ├── KnowledgeList.vue     # 知识库列表组件
│   │   ├── KnowledgeItem.vue     # 知识库项组件
│   │   ├── DocumentList.vue      # 文档列表组件
│   │   ├── DocumentItem.vue      # 文档项组件
│   │   ├── DocumentUpload.vue    # 文档上传组件
│   │   ├── DocumentPreview.vue   # 文档预览组件
│   │   ├── SearchResults.vue     # 搜索结果组件
│   │   ├── VectorSearch.vue      # 向量搜索组件
│   │   └── KnowledgeGraph.vue    # 知识图谱组件
│   ├── model/                    # 模型管理模块组件
│   │   ├── ModelContainer.vue    # 模型容器组件
│   │   ├── ModelList.vue         # 模型列表组件
│   │   ├── ModelItem.vue         # 模型项组件
│   │   ├── ModelSearch.vue       # 模型搜索组件
│   │   ├── ModelDownload.vue     # 模型下载组件
│   │   ├── ModelUpload.vue       # 模型上传组件
│   │   ├── ModelConfig.vue       # 模型配置组件
│   │   ├── ModelPerformance.vue  # 模型性能监控组件
│   │   ├── DownloadProgress.vue  # 下载进度组件
│   │   └── ModelQuantization.vue # 模型量化组件
│   ├── multimodal/               # 多模态模块组件
│   │   ├── MultimodalContainer.vue # 多模态容器组件
│   │   ├── OCRProcessor.vue      # OCR处理组件
│   │   ├── TTSProcessor.vue      # TTS处理组件
│   │   ├── ASRProcessor.vue      # ASR处理组件
│   │   ├── ImageAnalyzer.vue     # 图像分析组件
│   │   ├── VideoAnalyzer.vue     # 视频分析组件
│   │   ├── FormatConverter.vue   # 格式转换组件
│   │   ├── TaskQueue.vue         # 任务队列组件
│   │   ├── ProcessingHistory.vue # 处理历史组件
│   │   └── MediaPreview.vue      # 媒体预览组件
│   ├── remote/                   # 远程配置模块组件
│   │   ├── RemoteContainer.vue   # 远程配置容器组件
│   │   ├── ConfigList.vue        # 配置列表组件
│   │   ├── ConfigItem.vue        # 配置项组件
│   │   ├── ConfigForm.vue        # 配置表单组件
│   │   ├── ApiKeyManager.vue     # API密钥管理组件
│   │   ├── ProxySettings.vue     # 代理设置组件
│   │   ├── ConfigTemplates.vue   # 配置模板组件
│   │   ├── ConfigTest.vue        # 配置测试组件
│   │   └── ConfigSync.vue        # 配置同步组件
│   ├── network/                  # 网络共享模块组件
│   │   ├── NetworkContainer.vue  # 网络容器组件
│   │   ├── DeviceList.vue        # 设备列表组件
│   │   ├── DeviceItem.vue        # 设备项组件
│   │   ├── ResourceShare.vue     # 资源共享组件
│   │   ├── TransferList.vue      # 传输列表组件
│   │   ├── TransferItem.vue      # 传输项组件
│   │   ├── P2PConnection.vue     # P2P连接组件
│   │   ├── NetworkSettings.vue   # 网络设置组件
│   │   └── SecuritySettings.vue  # 安全设置组件
│   ├── plugins/                  # 插件系统组件
│   │   ├── PluginContainer.vue   # 插件容器组件
│   │   ├── PluginList.vue        # 插件列表组件
│   │   ├── PluginItem.vue        # 插件项组件
│   │   ├── PluginMarket.vue      # 插件市场组件
│   │   ├── PluginConfig.vue      # 插件配置组件
│   │   ├── PluginLogs.vue        # 插件日志组件
│   │   ├── PluginSandbox.vue     # 插件沙箱组件
│   │   └── PluginPermissions.vue # 插件权限组件
│   └── settings/                 # 设置模块组件
│       ├── SettingsContainer.vue # 设置容器组件
│       ├── GeneralSettings.vue   # 通用设置组件
│       ├── AppearanceSettings.vue # 外观设置组件
│       ├── LanguageSettings.vue  # 语言设置组件
│       ├── PerformanceSettings.vue # 性能设置组件
│       ├── SecuritySettings.vue  # 安全设置组件
│       ├── BackupSettings.vue    # 备份设置组件
│       ├── UpdateSettings.vue    # 更新设置组件
│       └── AboutDialog.vue       # 关于对话框组件
├── composables/                  # 组合式函数 - 可复用的业务逻辑
│   ├── useAuth.ts                # 用户认证相关逻辑
│   ├── useTheme.ts               # 主题切换相关逻辑
│   ├── useI18n.ts                # 国际化相关逻辑
│   ├── useChat.ts                # 聊天功能相关逻辑
│   ├── useKnowledge.ts           # 知识库功能相关逻辑
│   ├── useModel.ts               # 模型管理相关逻辑
│   ├── useNetwork.ts             # 网络共享相关逻辑
│   ├── useMultimodal.ts          # 多模态处理相关逻辑
│   ├── usePlugin.ts              # 插件系统相关逻辑
│   ├── useWebSocket.ts           # WebSocket连接相关逻辑
│   ├── useEventSource.ts         # SSE事件流相关逻辑
│   ├── useFileUpload.ts          # 文件上传相关逻辑
│   ├── useDownload.ts            # 文件下载相关逻辑
│   ├── useNotification.ts        # 通知相关逻辑
│   └── useStorage.ts             # 本地存储相关逻辑
├── stores/                       # 状态管理 (Pinia) - 全局状态和业务逻辑
│   ├── index.ts                  # Store统一导出
│   ├── app.ts                    # 应用全局状态
│   ├── auth.ts                   # 用户认证状态
│   ├── theme.ts                  # 主题状态管理
│   ├── i18n.ts                   # 国际化状态管理
│   ├── chat.ts                   # 聊天状态管理
│   ├── knowledge.ts              # 知识库状态管理
│   ├── model.ts                  # 模型管理状态
│   ├── remote.ts                 # 远程配置状态
│   ├── network.ts                # 网络共享状态
│   ├── multimodal.ts             # 多模态处理状态
│   ├── plugin.ts                 # 插件系统状态
│   └── system.ts                 # 系统状态管理
├── views/                        # 页面视图 - 主导航页面
│   ├── Chat/                     # 聊天页面
│   │   ├── index.vue             # 聊天主页面
│   │   ├── SessionView.vue       # 会话视图页面
│   │   └── SettingsView.vue      # 聊天设置页面
│   ├── Knowledge/                # 知识库页面
│   │   ├── index.vue             # 知识库主页面
│   │   ├── DocumentView.vue      # 文档视图页面
│   │   ├── SearchView.vue        # 搜索视图页面
│   │   └── AnalyticsView.vue     # 分析视图页面
│   ├── Model/                    # 模型管理页面
│   │   ├── index.vue             # 模型管理主页面
│   │   ├── LocalModels.vue       # 本地模型页面
│   │   ├── OnlineModels.vue      # 在线模型页面
│   │   ├── DownloadCenter.vue    # 下载中心页面
│   │   └── PerformanceView.vue   # 性能监控页面
│   ├── Multimodal/               # 多模态页面
│   │   ├── index.vue             # 多模态主页面
│   │   ├── OCRView.vue           # OCR处理页面
│   │   ├── AudioView.vue         # 音频处理页面
│   │   ├── ImageView.vue         # 图像处理页面
│   │   └── VideoView.vue         # 视频处理页面
│   ├── Remote/                   # 远程配置页面
│   │   ├── index.vue             # 远程配置主页面
│   │   ├── ApiKeys.vue           # API密钥管理页面
│   │   ├── ProxyConfig.vue       # 代理配置页面
│   │   └── CloudSync.vue         # 云端同步页面
│   ├── Network/                  # 网络共享页面
│   │   ├── index.vue             # 网络共享主页面
│   │   ├── DeviceManager.vue     # 设备管理页面
│   │   ├── ResourceShare.vue     # 资源共享页面
│   │   └── TransferCenter.vue    # 传输中心页面
│   ├── Plugins/                  # 插件页面
│   │   ├── index.vue             # 插件主页面
│   │   ├── Market.vue            # 插件市场页面
│   │   ├── Installed.vue         # 已安装插件页面
│   │   └── Developer.vue         # 开发者页面
│   └── Settings/                 # 设置页面
│       ├── index.vue             # 设置主页面
│       ├── General.vue           # 通用设置页面
│       ├── Appearance.vue        # 外观设置页面
│       ├── Performance.vue       # 性能设置页面
│       └── Security.vue          # 安全设置页面
├── router/                       # 路由管理
│   ├── index.ts                  # 路由配置主文件
│   ├── routes.ts                 # 路由定义
│   ├── guards.ts                 # 路由守卫
│   └── modules/                  # 模块化路由
│       ├── chat.ts               # 聊天模块路由
│       ├── knowledge.ts          # 知识库模块路由
│       ├── model.ts              # 模型管理模块路由
│       ├── multimodal.ts         # 多模态模块路由
│       ├── remote.ts             # 远程配置模块路由
│       ├── network.ts            # 网络共享模块路由
│       ├── plugins.ts            # 插件模块路由
│       └── settings.ts           # 设置模块路由
├── utils/                        # 工具函数 - 通用工具和辅助函数
│   ├── index.ts                  # 工具函数统一导出
│   ├── request.ts                # HTTP请求工具
│   ├── websocket.ts              # WebSocket工具
│   ├── eventSource.ts            # SSE事件流工具
│   ├── storage.ts                # 本地存储工具
│   ├── crypto.ts                 # 加密解密工具
│   ├── file.ts                   # 文件处理工具
│   ├── format.ts                 # 格式化工具
│   ├── validation.ts             # 数据验证工具
│   ├── date.ts                   # 日期时间工具
│   ├── color.ts                  # 颜色处理工具
│   ├── device.ts                 # 设备信息工具
│   ├── performance.ts            # 性能监控工具
│   └── constants.ts              # 常量定义
├── types/                        # TypeScript类型定义
│   ├── index.ts                  # 类型定义统一导出
│   ├── api.ts                    # API接口类型定义
│   ├── chat.ts                   # 聊天相关类型定义
│   ├── knowledge.ts              # 知识库相关类型定义
│   ├── model.ts                  # 模型相关类型定义
│   ├── multimodal.ts             # 多模态相关类型定义
│   ├── remote.ts                 # 远程配置相关类型定义
│   ├── network.ts                # 网络共享相关类型定义
│   ├── plugin.ts                 # 插件相关类型定义
│   ├── system.ts                 # 系统相关类型定义
│   ├── user.ts                   # 用户相关类型定义
│   └── common.ts                 # 通用类型定义
├── locales/                      # 国际化文件
│   ├── index.ts                  # 国际化配置主文件
│   ├── zh-CN/                    # 中文语言包
│   │   ├── common.json           # 通用翻译
│   │   ├── chat.json             # 聊天模块翻译
│   │   ├── knowledge.json        # 知识库模块翻译
│   │   ├── model.json            # 模型管理模块翻译
│   │   ├── multimodal.json       # 多模态模块翻译
│   │   ├── remote.json           # 远程配置模块翻译
│   │   ├── network.json          # 网络共享模块翻译
│   │   ├── plugins.json          # 插件模块翻译
│   │   └── settings.json         # 设置模块翻译
│   └── en-US/                    # 英文语言包
│       ├── common.json           # 通用翻译
│       ├── chat.json             # 聊天模块翻译
│       ├── knowledge.json        # 知识库模块翻译
│       ├── model.json            # 模型管理模块翻译
│       ├── multimodal.json       # 多模态模块翻译
│       ├── remote.json           # 远程配置模块翻译
│       ├── network.json          # 网络共享模块翻译
│       ├── plugins.json          # 插件模块翻译
│       └── settings.json         # 设置模块翻译
├── directives/                   # Vue指令
│   ├── index.ts                  # 指令统一导出
│   ├── loading.ts                # 加载指令
│   ├── permission.ts             # 权限指令
│   ├── copy.ts                   # 复制指令
│   └── resize.ts                 # 尺寸变化指令
├── App.vue                       # 根组件
├── main.ts                       # 应用入口文件
└── env.d.ts                      # 环境变量类型定义
```

### 5.2 后端目录结构 (src-tauri/)

```
src-tauri/
├── src/                          # Rust源代码目录
│   ├── main.rs                   # 应用入口文件
│   ├── lib.rs                    # 库文件，定义公共模块
│   ├── ai/                       # AI核心模块 - 推理引擎、模型管理
│   │   ├── mod.rs                # AI模块定义
│   │   ├── inference.rs          # 推理引擎核心逻辑
│   │   ├── model.rs              # 模型抽象和管理
│   │   ├── tokenizer.rs          # 分词器管理
│   │   ├── memory.rs             # 内存管理和优化
│   │   ├── gpu.rs                # GPU资源管理
│   │   ├── quantization.rs       # 模型量化处理
│   │   ├── performance.rs        # 性能监控和优化
│   │   ├── deployment.rs         # 模型部署管理
│   │   ├── downloader.rs         # 模型下载器
│   │   ├── local_upload.rs       # 本地模型上传
│   │   ├── huggingface.rs        # HuggingFace集成
│   │   └── thinking.rs           # 思维链处理
│   ├── chat/                     # 聊天功能 - 会话管理、消息处理
│   │   ├── mod.rs                # 聊天模块定义
│   │   ├── session.rs            # 会话管理
│   │   ├── message.rs            # 消息处理
│   │   ├── history.rs            # 聊天历史管理
│   │   ├── context.rs            # 上下文管理
│   │   ├── streaming.rs          # 流式响应处理
│   │   └── export.rs             # 会话导出功能
│   ├── knowledge/                # 知识库 - 文档解析、向量搜索
│   │   ├── mod.rs                # 知识库模块定义
│   │   ├── document.rs           # 文档处理和解析
│   │   ├── vector.rs             # 向量数据库操作
│   │   ├── embedding.rs          # 文本向量化
│   │   ├── search.rs             # 语义搜索引擎
│   │   ├── chunking.rs           # 文档分块处理
│   │   ├── indexing.rs           # 索引管理
│   │   ├── rag.rs                # RAG增强检索
│   │   └── graph.rs              # 知识图谱构建
│   ├── multimodal/               # 多模态 - OCR、TTS、音频处理
│   │   ├── mod.rs                # 多模态模块定义
│   │   ├── ocr.rs                # OCR文字识别
│   │   ├── tts.rs                # 文字转语音
│   │   ├── asr.rs                # 语音转文字
│   │   ├── image.rs              # 图像处理和分析
│   │   ├── video.rs              # 视频处理和分析
│   │   ├── audio.rs              # 音频处理
│   │   ├── converter.rs          # 格式转换器
│   │   └── pipeline.rs           # 处理流水线
│   ├── remote/                   # 远程配置 - API密钥、云端集成
│   │   ├── mod.rs                # 远程配置模块定义
│   │   ├── config.rs             # 配置管理
│   │   ├── api_client.rs         # API客户端
│   │   ├── providers.rs          # 服务提供商集成
│   │   ├── proxy.rs              # 代理设置管理
│   │   ├── sync.rs               # 配置同步
│   │   └── validation.rs         # 配置验证
│   ├── network/                  # 网络共享 - P2P通信、文件传输
│   │   ├── mod.rs                # 网络模块定义
│   │   ├── discovery.rs          # 设备发现(mDNS)
│   │   ├── p2p.rs                # P2P通信协议
│   │   ├── transfer.rs           # 文件传输管理
│   │   ├── security.rs           # 网络安全和加密
│   │   ├── protocol.rs           # 通信协议定义
│   │   ├── node.rs               # 网络节点管理
│   │   └── sync.rs               # 数据同步机制
│   ├── plugins/                  # 插件系统 - WASM插件、市场
│   │   ├── mod.rs                # 插件模块定义
│   │   ├── runtime.rs            # 插件运行时
│   │   ├── sandbox.rs            # 沙箱环境
│   │   ├── market.rs             # 插件市场
│   │   ├── installer.rs          # 插件安装器
│   │   ├── manager.rs            # 插件管理器
│   │   ├── permissions.rs        # 权限管理
│   │   ├── api.rs                # 插件API接口
│   │   └── loader.rs             # 插件加载器
│   ├── system/                   # 系统管理 - 监控、配置、日志
│   │   ├── mod.rs                # 系统模块定义
│   │   ├── monitor.rs            # 系统监控
│   │   ├── config.rs             # 系统配置管理
│   │   ├── logger.rs             # 日志系统
│   │   ├── backup.rs             # 数据备份
│   │   ├── update.rs             # 自动更新
│   │   ├── health.rs             # 健康检查
│   │   ├── metrics.rs            # 性能指标
│   │   └── diagnostics.rs        # 系统诊断
│   ├── db/                       # 数据库 - SQLite和向量数据库
│   │   ├── mod.rs                # 数据库模块定义
│   │   ├── connection.rs         # 数据库连接管理
│   │   ├── migrations.rs         # 数据库迁移
│   │   ├── schema.rs             # 数据库模式定义
│   │   ├── models/               # 数据模型
│   │   │   ├── mod.rs            # 模型模块定义
│   │   │   ├── chat.rs           # 聊天相关模型
│   │   │   ├── knowledge.rs      # 知识库相关模型
│   │   │   ├── model.rs          # 模型管理相关模型
│   │   │   ├── network.rs        # 网络相关模型
│   │   │   ├── system.rs         # 系统相关模型
│   │   │   └── plugin.rs         # 插件相关模型
│   │   ├── repositories/         # 数据访问层
│   │   │   ├── mod.rs            # 仓储模块定义
│   │   │   ├── chat.rs           # 聊天数据访问
│   │   │   ├── knowledge.rs      # 知识库数据访问
│   │   │   ├── model.rs          # 模型数据访问
│   │   │   ├── network.rs        # 网络数据访问
│   │   │   ├── system.rs         # 系统数据访问
│   │   │   └── plugin.rs         # 插件数据访问
│   │   └── vector/               # 向量数据库
│   │       ├── mod.rs            # 向量数据库模块定义
│   │       ├── chroma.rs         # ChromaDB集成
│   │       ├── client.rs         # 向量数据库客户端
│   │       └── operations.rs     # 向量操作
│   ├── commands/                 # Tauri命令 - 前后端接口层
│   │   ├── mod.rs                # 命令模块定义
│   │   ├── chat.rs               # 聊天相关命令
│   │   ├── knowledge.rs          # 知识库相关命令
│   │   ├── model.rs              # 模型管理相关命令
│   │   ├── remote.rs             # 远程配置相关命令
│   │   ├── network.rs            # 网络共享相关命令
│   │   ├── multimodal.rs         # 多模态相关命令
│   │   ├── system.rs             # 系统管理相关命令
│   │   └── plugin.rs             # 插件系统相关命令
│   ├── events/                   # 事件系统 - 应用内事件通信
│   │   ├── mod.rs                # 事件模块定义
│   │   ├── chat.rs               # 聊天事件
│   │   ├── model.rs              # 模型事件
│   │   ├── system.rs             # 系统事件
│   │   ├── network.rs            # 网络事件
│   │   └── plugin.rs             # 插件事件
│   ├── utils/                    # 工具模块 - 通用工具和辅助函数
│   │   ├── mod.rs                # 工具模块定义
│   │   ├── crypto.rs             # 加密解密工具
│   │   ├── file.rs               # 文件操作工具
│   │   ├── network.rs            # 网络工具
│   │   ├── compression.rs        # 压缩解压工具
│   │   ├── validation.rs         # 数据验证工具
│   │   ├── serialization.rs      # 序列化工具
│   │   ├── time.rs               # 时间处理工具
│   │   ├── path.rs               # 路径处理工具
│   │   └── constants.rs          # 常量定义
│   ├── error/                    # 错误处理 - 统一错误管理
│   │   ├── mod.rs                # 错误模块定义
│   │   ├── types.rs              # 错误类型定义
│   │   ├── handler.rs            # 错误处理器
│   │   └── recovery.rs           # 错误恢复机制
│   └── config/                   # 配置管理 - 应用配置
│       ├── mod.rs                # 配置模块定义
│       ├── app.rs                # 应用配置
│       ├── database.rs           # 数据库配置
│       ├── ai.rs                 # AI配置
│       ├── network.rs            # 网络配置
│       └── security.rs           # 安全配置
├── migrations/                   # 数据库迁移文件
│   ├── 001_initial.sql           # 初始数据库结构
│   ├── 002_create_knowledge_tables.sql # 知识库相关表
│   ├── 003_create_config_tables.sql    # 配置相关表
│   ├── 004_create_network_tables.sql   # 网络相关表
│   ├── 005_create_plugin_tables.sql    # 插件相关表
│   └── 006_create_indexes.sql          # 索引创建
├── capabilities/                 # Tauri权限配置
│   └── default.json              # 默认权限配置
├── icons/                        # 应用图标
│   ├── 32x32.png                 # 32x32图标
│   ├── 128x128.png               # 128x128图标
│   ├── icon.icns                 # macOS图标
│   └── icon.ico                  # Windows图标
├── Cargo.toml                    # Rust项目配置文件
├── tauri.conf.json               # Tauri配置文件
└── build.rs                      # 构建脚本
```

## 6. 界面设计

### 6.1 整体UI设计规范

**设计原则**
- **简洁明了**：界面布局清晰，功能分区明确
- **一致性**：统一的设计语言和交互模式
- **响应式**：适配不同屏幕尺寸和分辨率
- **可访问性**：支持键盘导航和屏幕阅读器

**色彩系统**
- **主色调**：蓝色系 (#1890ff)，体现科技感和专业性
- **辅助色**：灰色系，用于文本和边框
- **状态色**：成功(绿色)、警告(橙色)、错误(红色)、信息(蓝色)
- **主题支持**：浅色主题和深色主题

**字体系统**
- **主字体**：系统默认字体 (SF Pro Display / Microsoft YaHei)
- **代码字体**：等宽字体 (JetBrains Mono / Consolas)
- **字体大小**：12px(小)、14px(正常)、16px(大)、18px(标题)

### 6.2 主界面布局设计

**整体布局**
采用经典的桌面应用布局，包含顶部导航栏、主内容区域和状态栏：

```
┌─────────────────────────────────────────────────────────────┐
│  Logo    聊天  知识库  模型  多模态  远程  网络  插件  设置   │ 顶部导航
├─────────────────────────────────────────────────────────────┤
│                                                             │
│                     主内容区域                              │
│                                                             │
│                                                             │
├─────────────────────────────────────────────────────────────┤
│  状态信息                                        系统状态    │ 底部状态栏
└─────────────────────────────────────────────────────────────┘
```

**顶部导航栏**
- **左侧Logo区域**：显示AI Studio应用Logo和名称
- **中央导航区域**：8个主导航标签页
  - 聊天 (Chat) - 图标：💬
  - 知识库 (Knowledge) - 图标：📚
  - 模型管理 (Model) - 图标：🤖
  - 多模态 (Multimodal) - 图标：🎭
  - 远程配置 (Remote) - 图标：🌐
  - 网络共享 (Network) - 图标：🔗
  - 插件系统 (Plugins) - 图标：🧩
  - 设置 (Settings) - 图标：⚙️
- **右侧用户区域**：用户头像和下拉菜单
  - 主题切换 (Theme) - 图标：🌓
  - 语言切换 (Language) - 图标：🌍
  - 用户信息 (Profile) - 图标：👤

**主题设计**
- **浅色主题**：白色背景，深色文字，蓝色主色调
- **深色主题**：深灰色背景，浅色文字，蓝色主色调
- **主题切换**：平滑过渡动画，保持用户体验一致性

### 6.3 聊天模块界面设计

**布局结构**：左侧会话列表 + 右侧聊天区域

**左侧会话列表 (300px宽度)**
- **顶部操作栏**：
  - 新建会话按钮 - 点击创建新的聊天会话
  - 搜索框 - 搜索历史会话
  - 排序选项 - 按时间/名称排序
- **会话列表**：
  - 会话项显示：会话标题、最后消息预览、时间戳
  - 右键菜单：重命名、删除、导出、置顶
  - 拖拽排序：支持会话拖拽重新排序
- **底部统计**：显示总会话数、今日消息数

**右侧聊天区域**
- **顶部工具栏**：
  - 会话标题（可编辑）
  - 模型选择下拉框
  - 参数设置按钮（温度、最大token等）
  - 清空会话按钮
  - 导出会话按钮
- **消息显示区域**：
  - 用户消息：右对齐，蓝色气泡
  - AI回复：左对齐，灰色气泡
  - 系统消息：居中显示，小字体
  - Markdown渲染：支持代码高亮、表格、公式
  - 附件显示：图片预览、文件下载链接
- **输入区域**：
  - 多行文本输入框（支持Markdown）
  - 附件上传按钮（图片、文档、音频）
  - 发送按钮（Ctrl+Enter快捷键）
  - 语音输入按钮
  - 表情符号按钮

**交互逻辑**
- **消息发送**：点击发送或快捷键触发
- **流式显示**：AI回复逐字显示，带打字机效果
- **消息操作**：悬停显示复制、重新生成、删除按钮
- **会话管理**：支持会话重命名、删除、导出
- **快捷操作**：支持键盘快捷键操作

### 6.4 知识库模块界面设计

**布局结构**：左侧知识库列表 + 右侧文档管理区域

**左侧知识库列表 (280px宽度)**
- **顶部操作栏**：
  - 创建知识库按钮
  - 导入知识库按钮
  - 搜索框
- **知识库列表**：
  - 知识库项显示：名称、文档数量、大小、创建时间
  - 右键菜单：编辑、删除、导出、备份
  - 状态指示：处理中、已完成、错误状态

**右侧文档管理区域**
- **顶部工具栏**：
  - 知识库名称和描述
  - 上传文档按钮
  - 批量操作按钮
  - 搜索框
  - 视图切换（列表/网格）
- **文档列表**：
  - 文档项显示：文件名、类型、大小、状态、处理时间
  - 状态指示：处理中、已完成、失败
  - 进度条：显示处理进度
  - 操作按钮：预览、下载、删除、重新处理
- **搜索结果区域**：
  - 语义搜索结果列表
  - 相关度评分显示
  - 高亮匹配文本
  - 来源文档链接

**交互逻辑**
- **文档上传**：拖拽上传或点击选择文件
- **批量操作**：支持多选文档进行批量删除/处理
- **实时搜索**：输入关键词实时显示搜索结果
- **文档预览**：支持PDF、Word、图片等格式预览

### 6.5 模型管理模块界面设计

**布局结构**：顶部标签页 + 主内容区域

**标签页导航**
- 本地模型 - 显示已下载的模型
- 在线模型 - 浏览和搜索在线模型
- 下载中心 - 管理下载任务
- 性能监控 - 模型性能统计

**本地模型页面**
- **模型列表**：
  - 模型卡片显示：名称、版本、大小、状态
  - 状态指示：未加载、已加载、加载中、错误
  - 操作按钮：加载、卸载、配置、删除、量化
- **模型详情面板**：
  - 模型信息：架构、参数量、上下文长度
  - 配置选项：量化级别、GPU层数、内存限制
  - 性能数据：推理速度、内存使用、GPU利用率

**在线模型页面**
- **搜索和筛选**：
  - 搜索框：按名称、作者、标签搜索
  - 筛选器：模型类型、大小、许可证
  - 排序选项：下载量、评分、更新时间
- **模型列表**：
  - 模型卡片：名称、作者、描述、评分、大小
  - 下载按钮：一键下载模型
  - 详情链接：查看模型详细信息

**下载中心页面**
- **下载任务列表**：
  - 任务信息：模型名称、文件大小、下载进度
  - 状态显示：等待中、下载中、已完成、失败、暂停
  - 操作按钮：暂停、恢复、取消、重试
- **下载统计**：
  - 总下载量、当前速度、剩余时间
  - 网络状态和连接质量

### 6.6 多模态模块界面设计

**布局结构**：功能标签页 + 处理区域

**功能标签页**
- OCR识别 - 图片文字识别
- 语音处理 - TTS和ASR功能
- 图像分析 - 图像理解和描述
- 视频处理 - 视频分析和字幕

**处理区域**
- **文件上传区域**：拖拽上传或选择文件
- **参数设置**：处理参数和选项配置
- **处理结果**：显示处理结果和输出文件
- **任务队列**：批量处理任务管理

### 6.7 远程配置模块界面设计

**布局结构**：左侧配置列表 + 右侧配置详情

**左侧配置列表 (300px宽度)**
- **配置项显示**：服务商名称、状态、最后测试时间
- **状态指示**：已连接、未连接、测试中、错误
- **操作按钮**：新增、编辑、删除、测试连接

**右侧配置详情**
- **基本信息**：配置名称、服务商类型、描述
- **API配置**：API密钥、基础URL、模型名称
- **参数设置**：最大token、温度、top_p等
- **代理设置**：代理类型、地址、端口、认证
- **测试结果**：连接状态、响应时间、错误信息

### 6.8 网络共享模块界面设计

**布局结构**：设备发现 + 资源共享 + 传输管理

**设备发现区域**
- **在线设备列表**：设备名称、IP地址、状态、能力
- **连接操作**：连接、断开、信任设备
- **设备详情**：系统信息、共享资源、权限设置

**资源共享区域**
- **本地资源**：可共享的模型、知识库、配置
- **远程资源**：其他设备共享的资源
- **权限管理**：设置访问权限和安全策略

**传输管理区域**
- **传输任务**：文件名、大小、进度、速度、状态
- **传输控制**：暂停、恢复、取消传输
- **传输历史**：已完成的传输记录

### 6.9 插件系统界面设计

**布局结构**：标签页导航 + 插件管理区域

**标签页导航**
- 已安装 - 显示已安装的插件
- 插件市场 - 浏览和安装新插件
- 开发者 - 插件开发工具

**已安装页面**
- **插件列表**：插件名称、版本、状态、操作按钮
- **插件详情**：描述、权限、配置选项
- **插件控制**：启用、禁用、卸载、配置

**插件市场页面**
- **搜索和筛选**：按类别、评分、下载量筛选
- **插件卡片**：名称、描述、评分、截图
- **安装操作**：一键安装、查看详情

### 6.9 插件系统界面设计

**布局结构**：标签页导航 + 插件管理区域

**标签页导航**
- 已安装 - 显示已安装的插件
- 插件市场 - 浏览和安装新插件
- 开发者 - 插件开发工具

**已安装页面**
- **插件列表**：
  - 插件卡片显示：名称、版本、状态、描述
  - 状态指示：已启用、已禁用、错误、更新可用
  - 操作按钮：启用、禁用、配置、卸载、更新
- **插件详情面板**：
  - 基本信息：作者、版本、类别、安装时间
  - 权限列表：插件所需的系统权限
  - 配置选项：插件特定的配置参数
  - 执行日志：插件运行日志和错误信息

**插件市场页面**
- **搜索和筛选**：
  - 搜索框：按名称、作者、描述搜索
  - 分类筛选：工具、API、UI、搜索等类别
  - 排序选项：下载量、评分、更新时间、名称
  - 筛选器：免费/付费、验证状态、兼容性
- **插件卡片**：
  - 插件信息：名称、作者、评分、下载量
  - 预览图：插件截图或图标
  - 简介：功能描述和特性说明
  - 操作按钮：安装、查看详情、收藏
- **插件详情页**：
  - 详细描述：功能介绍、使用说明
  - 截图展示：界面预览和功能演示
  - 版本历史：更新日志和版本信息
  - 用户评价：评分、评论、反馈

**开发者页面**
- **插件开发工具**：
  - 创建新插件：模板选择和项目初始化
  - 代码编辑器：内置的插件代码编辑环境
  - 调试工具：插件调试和测试功能
  - 打包发布：插件打包和上传到市场
- **开发文档**：
  - API文档：插件开发API参考
  - 示例代码：常用功能的代码示例
  - 开发指南：插件开发最佳实践
  - 权限说明：插件权限系统介绍

**交互逻辑**
- **插件安装**：一键安装，自动处理依赖和权限
- **权限管理**：安装时显示权限请求，用户确认
- **配置管理**：图形化配置界面，实时预览效果
- **状态监控**：实时显示插件运行状态和性能
- **安全检查**：自动检测插件安全性和兼容性

### 6.10 设置模块界面设计

**布局结构**：左侧设置分类 + 右侧设置内容

**设置分类**
- 通用设置 - 基本应用设置
- 外观设置 - 主题和界面设置
- 性能设置 - 性能优化选项
- 安全设置 - 安全和隐私设置
- 备份设置 - 数据备份配置
- 更新设置 - 自动更新配置

**设置内容**
- **表单控件**：开关、滑块、下拉框、输入框
- **实时预览**：设置变更的实时效果预览
- **重置选项**：恢复默认设置功能

## 7. API接口设计

### 7.1 接口设计规范

**RESTful API设计原则**
- **统一资源标识符**：使用清晰的URL路径结构
- **HTTP方法语义**：GET(查询)、POST(创建)、PUT(更新)、DELETE(删除)
- **状态码规范**：200(成功)、201(创建)、400(请求错误)、401(未授权)、404(未找到)、500(服务器错误)
- **响应格式统一**：JSON格式，包含data、message、code字段

**接口响应格式**
```typescript
interface ApiResponse<T> {
  code: number;           // 状态码
  message: string;        // 响应消息
  data?: T;              // 响应数据
  timestamp: number;      // 时间戳
  request_id: string;     // 请求ID
  errors?: ApiError[];    // 错误详情
}

interface ApiError {
  field?: string;         // 错误字段
  code: string;          // 错误代码
  message: string;       // 错误消息
  details?: any;         // 错误详情
}
```

**分页响应格式**
```typescript
interface PaginatedResponse<T> {
  items: T[];            // 数据列表
  total: number;         // 总数量
  page: number;          // 当前页码
  limit: number;         // 每页数量
  has_more: boolean;     // 是否有更多数据
}
```

**错误处理规范**
```typescript
// 成功响应示例
{
  "code": 200,
  "message": "操作成功",
  "data": { ... },
  "timestamp": 1703123456789,
  "request_id": "req_123456"
}

// 参数验证错误示例
{
  "code": 400,
  "message": "请求参数错误",
  "timestamp": 1703123456789,
  "request_id": "req_123456",
  "errors": [
    {
      "field": "model_id",
      "code": "REQUIRED",
      "message": "模型ID不能为空"
    },
    {
      "field": "temperature",
      "code": "INVALID_RANGE",
      "message": "温度值必须在0.0-2.0之间",
      "details": { "min": 0.0, "max": 2.0, "current": 3.5 }
    }
  ]
}

// 业务逻辑错误示例
{
  "code": 422,
  "message": "模型加载失败",
  "timestamp": 1703123456789,
  "request_id": "req_123456",
  "errors": [
    {
      "code": "MODEL_LOAD_FAILED",
      "message": "模型文件损坏或不兼容",
      "details": {
        "model_id": "llama-7b",
        "error_type": "CORRUPTED_FILE",
        "file_path": "/models/llama-7b.bin"
      }
    }
  ]
}
```

**状态码详细定义**
- **2xx 成功状态码**
  - 200: 请求成功
  - 201: 资源创建成功
  - 204: 删除成功，无返回内容
- **4xx 客户端错误**
  - 400: 请求参数错误
  - 401: 未授权访问
  - 403: 权限不足
  - 404: 资源不存在
  - 409: 资源冲突
  - 422: 数据验证失败
  - 429: 请求频率限制
- **5xx 服务器错误**
  - 500: 服务器内部错误
  - 502: 外部服务错误
  - 503: 服务不可用
  - 504: 请求超时

### 7.2 聊天模块API接口

**发送消息接口**
```typescript
POST /api/chat/send
Request: {
  session_id: string;
  message: string;
  attachments?: Array<{
    type: 'image' | 'document' | 'audio';
    url: string;
    name: string;
    size: number;
  }>;
  model_config?: {
    model_id: string;
    temperature: number;
    max_tokens: number;
    top_p: number;
  };
}
Response: {
  message_id: string;
  status: 'success' | 'error';
  error?: string;
}
```

**SSE流式响应接口**
```typescript
GET /api/chat/stream/{message_id}
Response: Server-Sent Events
data: {
  type: 'token' | 'done' | 'error';
  content: string;
  metadata?: {
    tokens_used: number;
    response_time: number;
  };
}
```

**会话管理接口**
```typescript
// 获取会话列表
GET /api/chat/sessions
Query: {
  page?: number;
  limit?: number;
  search?: string;
  sort?: 'created_at' | 'updated_at' | 'title';
  order?: 'asc' | 'desc';
}

// 创建会话
POST /api/chat/sessions
Request: {
  title?: string;
  model_id?: string;
  system_prompt?: string;
  config?: {
    temperature: number;
    max_tokens: number;
    top_p: number;
  };
}

// 更新会话
PUT /api/chat/sessions/{id}
Request: {
  title?: string;
  system_prompt?: string;
  config?: object;
}

// 删除会话
DELETE /api/chat/sessions/{id}

// 获取会话消息
GET /api/chat/messages/{session_id}
Query: {
  page?: number;
  limit?: number;
  before?: string;
  after?: string;
}
```

### 7.3 知识库模块API接口

**知识库管理接口**
```typescript
// 创建知识库
POST /api/knowledge
Request: {
  name: string;
  description?: string;
  embedding_model?: string;
  chunk_size?: number;
  chunk_overlap?: number;
}

// 获取知识库列表
GET /api/knowledge
Query: {
  page?: number;
  limit?: number;
  search?: string;
}

// 更新知识库
PUT /api/knowledge/{id}
Request: {
  name?: string;
  description?: string;
  embedding_model?: string;
  chunk_size?: number;
  chunk_overlap?: number;
}

// 删除知识库
DELETE /api/knowledge/{id}
```

**文档管理接口**
```typescript
// 上传文档
POST /api/knowledge/{kb_id}/upload
Request: FormData {
  files: File[];
  auto_process?: boolean;
}
Response: {
  documents: Array<{
    id: string;
    name: string;
    type: string;
    size: number;
    status: 'processing' | 'completed' | 'failed';
  }>;
}

// 获取文档列表
GET /api/knowledge/{kb_id}/documents
Query: {
  page?: number;
  limit?: number;
  status?: string;
  type?: string;
}

// 删除文档
DELETE /api/knowledge/{kb_id}/documents/{doc_id}
```

**语义搜索接口**
```typescript
POST /api/knowledge/{kb_id}/search
Request: {
  query: string;
  limit?: number;
  threshold?: number;
  filters?: {
    document_types?: string[];
    date_range?: {
      start: string;
      end: string;
    };
  };
}
Response: {
  results: Array<{
    document_id: string;
    document_name: string;
    chunk_id: string;
    content: string;
    score: number;
    metadata: {
      page_number?: number;
      chunk_index: number;
    };
  }>;
  total: number;
  query_time: number;
}
```

### 7.4 模型管理模块API接口

**模型管理接口**
```typescript
// 获取模型列表
GET /api/models
Query: {
  type?: 'local' | 'online' | 'all';
  category?: string;
  search?: string;
  page?: number;
  limit?: number;
}

// 搜索在线模型
GET /api/models/search
Query: {
  query: string;
  category?: string;
  min_size?: number;
  max_size?: number;
  quantization?: string;
  sort?: 'downloads' | 'rating' | 'updated';
  page?: number;
  limit?: number;
}

// 下载模型
POST /api/models/download
Request: {
  model_id?: string;
  huggingface_id?: string;
  download_url?: string;
  quantization?: string;
  mirror?: 'huggingface' | 'hf-mirror';
}
Response: {
  task_id: string;
  model_id: string;
  status: 'pending' | 'downloading';
}

// 加载模型
POST /api/models/{model_id}/load
Request: {
  config?: {
    gpu_layers?: number;
    context_length?: number;
    batch_size?: number;
    threads?: number;
  };
}
Response: {
  status: 'success' | 'error';
  message?: string;
  memory_usage?: number;
  load_time?: number;
}

// 卸载模型
POST /api/models/{model_id}/unload

// 删除模型
DELETE /api/models/{id}

// 模型量化
POST /api/models/{model_id}/quantize
Request: {
  quantization_type: 'q4_0' | 'q4_1' | 'q8_0' | 'q8_1';
  output_path?: string;
}
Response: {
  task_id: string;
  status: 'pending' | 'processing';
  estimated_time?: number;
}
```

### 7.5 多模态处理模块API接口

**OCR识别接口**
```typescript
POST /api/multimodal/ocr
Request: FormData {
  image: File;
  language?: string;
  output_format?: 'text' | 'json' | 'pdf';
}
Response: {
  task_id: string;
  text: string;
  confidence: number;
  bounding_boxes?: Array<{
    text: string;
    x: number;
    y: number;
    width: number;
    height: number;
    confidence: number;
  }>;
  processing_time: number;
}
```

**语音处理接口**
```typescript
// 文字转语音
POST /api/multimodal/tts
Request: {
  text: string;
  voice?: string;
  speed?: number;
  pitch?: number;
  output_format?: 'wav' | 'mp3' | 'ogg';
}
Response: {
  task_id: string;
  audio_url: string;
  duration: number;
  file_size: number;
}

// 语音转文字
POST /api/multimodal/asr
Request: FormData {
  audio: File;
  language?: string;
  model?: string;
}
Response: {
  task_id: string;
  text: string;
  confidence: number;
  segments?: Array<{
    text: string;
    start: number;
    end: number;
    confidence: number;
  }>;
  processing_time: number;
}
```

**图像和视频处理接口**
```typescript
// 图像分析
POST /api/multimodal/image/analyze
Request: FormData {
  image: File;
  analysis_type: 'description' | 'objects' | 'text' | 'faces';
}

// 视频分析
POST /api/multimodal/video/analyze
Request: FormData {
  video: File;
  analysis_type: 'summary' | 'subtitles' | 'objects';
}

// 格式转换
POST /api/multimodal/convert
Request: FormData {
  file: File;
  target_format: string;
  quality?: number;
}
```

**任务管理接口**
```typescript
// 获取任务列表
GET /api/multimodal/tasks
Query: {
  status?: string;
  type?: string;
  page?: number;
  limit?: number;
}

// 获取任务详情
GET /api/multimodal/tasks/{id}

// 删除任务
DELETE /api/multimodal/tasks/{id}

// 获取处理历史
GET /api/multimodal/history
Query: {
  page?: number;
  limit?: number;
  date_range?: {
    start: string;
    end: string;
  };
}
```

### 7.6 远程配置模块API接口

**配置管理接口**
```typescript
// 获取配置列表
GET /api/remote/configs
Response: {
  configs: Array<{
    id: string;
    name: string;
    provider: string;
    model_name: string;
    is_active: boolean;
    last_test_at?: string;
    status: 'connected' | 'disconnected' | 'error';
    created_at: string;
  }>;
}

// 创建配置
POST /api/remote/configs
Request: {
  name: string;
  provider: 'openai' | 'anthropic' | 'google' | 'custom';
  api_key: string;
  base_url?: string;
  model_name: string;
  max_tokens?: number;
  temperature?: number;
  proxy?: {
    type: 'http' | 'https' | 'socks5';
    host: string;
    port: number;
    username?: string;
    password?: string;
  };
}

// 更新配置
PUT /api/remote/configs/{id}

// 删除配置
DELETE /api/remote/configs/{id}

// 测试配置
POST /api/remote/configs/{id}/test
Response: {
  status: 'success' | 'error';
  response_time?: number;
  error_message?: string;
  model_info?: {
    name: string;
    context_length: number;
    capabilities: string[];
  };
}

// 激活配置
POST /api/remote/configs/{id}/activate
```

### 7.7 网络共享模块API接口

**设备管理接口**
```typescript
// 设备发现
GET /api/network/discover
Response: {
  devices: Array<{
    id: string;
    name: string;
    device_type: string;
    ip_address: string;
    port: number;
    capabilities: string[];
    status: 'online' | 'offline' | 'busy';
    last_seen: string;
  }>;
}

// 连接设备
POST /api/network/connect
Request: {
  device_id: string;
  password?: string;
}
Response: {
  status: 'success' | 'error';
  connection_id?: string;
  error_message?: string;
}

// 断开连接
POST /api/network/disconnect
Request: {
  device_id: string;
}

// 获取节点列表
GET /api/network/nodes
```

**资源共享接口**
```typescript
// 共享资源
POST /api/network/share
Request: {
  resource_type: 'model' | 'knowledge_base' | 'config';
  resource_id: string;
  permissions: {
    read: boolean;
    write: boolean;
    download: boolean;
  };
  is_public: boolean;
  allowed_devices?: string[];
}
Response: {
  share_id: string;
  share_url: string;
  qr_code?: string;
}

// 获取共享资源列表
GET /api/network/resources
Query: {
  resource_type?: string;
  node_id?: string;
}
```

**传输管理接口**
```typescript
// 开始传输
POST /api/network/transfer
Request: {
  source_node_id: string;
  target_node_id: string;
  resource_type: string;
  resource_id: string;
}

// 获取传输任务列表
GET /api/network/transfers
Query: {
  status?: string;
  page?: number;
  limit?: number;
}

// 暂停传输
POST /api/network/transfers/{id}/pause

// 恢复传输
POST /api/network/transfers/{id}/resume

// 取消传输
DELETE /api/network/transfers/{id}
```

### 7.8 插件系统API接口

**插件管理接口**
```typescript
// 获取已安装插件列表
GET /api/plugins
Response: {
  plugins: Array<{
    id: string;
    name: string;
    display_name: string;
    version: string;
    author: string;
    category: string;
    status: 'enabled' | 'disabled' | 'error';
    permissions: string[];
    created_at: string;
  }>;
}

// 安装插件
POST /api/plugins/install
Request: {
  source: 'market' | 'local' | 'url';
  plugin_id?: string;
  file_path?: string;
  download_url?: string;
}
Response: {
  plugin_id: string;
  status: 'installing' | 'installed' | 'error';
  message?: string;
}

// 卸载插件
DELETE /api/plugins/{id}

// 启用插件
POST /api/plugins/{id}/enable

// 禁用插件
POST /api/plugins/{id}/disable

// 获取插件配置
GET /api/plugins/{id}/config

// 更新插件配置
PUT /api/plugins/{id}/config
Request: {
  config: Record<string, any>;
}

// 执行插件功能
POST /api/plugins/{id}/execute
Request: {
  function_name: string;
  parameters: Record<string, any>;
  context?: {
    user_id?: string;
    session_id?: string;
  };
}
Response: {
  result: any;
  execution_time: number;
  status: 'success' | 'error';
  error_message?: string;
}
```

**插件市场接口**
```typescript
// 获取插件市场列表
GET /api/plugins/market
Query: {
  category?: string;
  search?: string;
  sort?: 'downloads' | 'rating' | 'updated';
  page?: number;
  limit?: number;
}
Response: {
  plugins: Array<{
    id: string;
    name: string;
    description: string;
    version: string;
    author: string;
    category: string;
    rating: number;
    download_count: number;
    file_size: number;
    screenshots: string[];
    is_verified: boolean;
    updated_at: string;
  }>;
  total: number;
}

// 搜索插件
GET /api/plugins/market/search
Query: {
  query: string;
  category?: string;
}

// 获取插件详情
GET /api/plugins/market/{id}

// 从市场安装插件
POST /api/plugins/market/{id}/install

// 获取插件执行日志
GET /api/plugins/{id}/logs
Query: {
  level?: string;
  page?: number;
  limit?: number;
}
```

### 7.9 系统管理API接口

**系统信息接口**
```typescript
// 获取系统信息
GET /api/system/info
Response: {
  system: {
    os: string;
    arch: string;
    version: string;
    memory_total: number;
    memory_available: number;
    cpu_cores: number;
    gpu_info?: {
      name: string;
      memory: number;
      driver_version: string;
    };
  };
  application: {
    version: string;
    build_date: string;
    commit_hash: string;
  };
}

// 获取性能监控数据
GET /api/system/performance
Query: {
  duration?: number; // 时间范围(秒)
  interval?: number; // 采样间隔(秒)
}
Response: {
  metrics: Array<{
    timestamp: number;
    cpu_usage: number;
    memory_usage: number;
    gpu_usage?: number;
    disk_usage: number;
    network_io: {
      bytes_sent: number;
      bytes_received: number;
    };
  }>;
}

// 健康检查
GET /api/system/health
Response: {
  status: 'healthy' | 'warning' | 'error';
  checks: {
    database: 'ok' | 'error';
    vector_db: 'ok' | 'error';
    ai_engine: 'ok' | 'error';
    network: 'ok' | 'error';
  };
  uptime: number;
  last_check: string;
}
```

**配置管理接口**
```typescript
// 获取系统配置
GET /api/system/configs
Response: {
  configs: Record<string, {
    value: any;
    type: string;
    description: string;
    is_user_configurable: boolean;
  }>;
}

// 更新系统配置
PUT /api/system/configs
Request: {
  configs: Record<string, any>;
}
Response: {
  updated_configs: string[];
  restart_required: boolean;
}
```

**日志管理接口**
```typescript
// 查询系统日志
GET /api/system/logs
Query: {
  level?: 'debug' | 'info' | 'warn' | 'error';
  module?: string;
  start_time?: string;
  end_time?: string;
  search?: string;
  page?: number;
  limit?: number;
}
Response: {
  logs: Array<{
    id: number;
    level: string;
    module: string;
    message: string;
    metadata?: object;
    created_at: string;
  }>;
  total: number;
}
```

**备份管理接口**
```typescript
// 创建备份
POST /api/system/backup
Request: {
  backup_type: 'full' | 'incremental';
  include_models?: boolean;
  include_knowledge?: boolean;
  include_configs?: boolean;
}
Response: {
  backup_id: string;
  status: 'started' | 'error';
  estimated_size?: number;
}

// 获取备份列表
GET /api/system/backups
Query: {
  page?: number;
  limit?: number;
}
Response: {
  backups: Array<{
    id: string;
    backup_type: string;
    file_path: string;
    file_size: number;
    checksum: string;
    status: 'completed' | 'failed';
    created_at: string;
  }>;
  total: number;
}

// 恢复备份
POST /api/system/restore
Request: {
  backup_id: string;
  restore_options: {
    restore_models: boolean;
    restore_knowledge: boolean;
    restore_configs: boolean;
  };
}
Response: {
  status: 'started' | 'error';
  message?: string;
}
```

**更新管理接口**
```typescript
// 检查更新
POST /api/system/update/check
Response: {
  has_update: boolean;
  current_version: string;
  latest_version?: string;
  release_notes?: string;
  download_size?: number;
  release_date?: string;
}

// 安装更新
POST /api/system/update/install
Request: {
  version?: string;
  auto_restart?: boolean;
}
Response: {
  status: 'started' | 'error';
  message?: string;
  restart_required?: boolean;
}
```

## 8. 业务流程设计

### 8.1 应用启动和初始化流程

```
用户启动应用
    ↓
检查系统环境
    ↓
初始化数据库连接
    ↓
加载系统配置
    ↓
初始化AI推理引擎
    ↓
启动向量数据库服务
    ↓
加载用户偏好设置
    ↓
初始化主题和语言
    ↓
显示主界面
    ↓
检查模型状态
    ↓
启动后台服务
    ↓
应用就绪
```

### 8.2 聊天对话处理流程

```
用户输入消息
    ↓
验证输入内容
    ↓
检查会话状态
    ↓
处理附件（如有）
    ↓
构建消息上下文
    ↓
选择推理引擎
    ↓
[本地模型分支]          [远程API分支]
检查模型加载状态         验证API配置
    ↓                      ↓
加载模型（如需要）       构建API请求
    ↓                      ↓
本地推理处理             发送远程请求
    ↓                      ↓
流式输出响应             处理API响应
    ↓                      ↓
        合并到主流程
            ↓
    保存消息到数据库
            ↓
    更新会话状态
            ↓
    触发前端更新
            ↓
    处理完成
```

### 8.3 知识库文档处理流程

```
用户上传文档
    ↓
文件格式检测
    ↓
[PDF分支]     [Word分支]    [Excel分支]    [Markdown分支]
PDF解析器     DOCX解析器    表格解析器     MD解析器
    ↓             ↓            ↓             ↓
提取文本和图片  提取文本和格式  提取数据       提取结构化内容
    ↓             ↓            ↓             ↓
        合并到主流程
            ↓
    文本预处理和清洗
            ↓
    智能分块处理
            ↓
    生成文本向量
            ↓
    存储到向量数据库
            ↓
    更新文档索引
            ↓
    更新处理状态
            ↓
    通知前端完成
```

### 8.4 模型下载和管理流程

```
用户搜索模型
    ↓
查询HuggingFace API
    ↓
显示搜索结果
    ↓
用户选择下载
    ↓
检查本地存储空间
    ↓
选择下载源
    ↓
[官方源分支]        [镜像源分支]
连接HF官方服务器    连接镜像服务器
    ↓                  ↓
        合并到主流程
            ↓
    创建下载任务
            ↓
    分片下载文件
            ↓
    实时更新进度
            ↓
    验证文件完整性
            ↓
    解压和安装模型
            ↓
    更新模型数据库
            ↓
    通知下载完成
```

### 8.5 多模态处理流程

```
用户上传媒体文件
    ↓
文件类型检测
    ↓
[图片分支]    [音频分支]    [视频分支]
图片处理      音频处理      视频处理
    ↓            ↓            ↓
OCR识别       ASR转换       帧提取
图像分析      TTS合成       字幕生成
    ↓            ↓            ↓
        合并到主流程
            ↓
    创建处理任务
            ↓
    加入任务队列
            ↓
    异步处理执行
            ↓
    实时更新进度
            ↓
    生成处理结果
            ↓
    保存输出文件
            ↓
    更新任务状态
            ↓
    通知处理完成
```

### 8.6 网络设备发现和连接流程

```
启动设备发现
    ↓
广播mDNS查询
    ↓
接收设备响应
    ↓
解析设备信息
    ↓
验证设备能力
    ↓
显示可用设备
    ↓
用户选择连接
    ↓
发起连接请求
    ↓
设备身份验证
    ↓
建立安全通道
    ↓
交换公钥信息
    ↓
协商通信协议
    ↓
建立P2P连接
    ↓
同步资源列表
    ↓
连接建立完成
```

### 8.7 插件安装和执行流程

```
用户浏览插件市场
    ↓
搜索和筛选插件
    ↓
查看插件详情
    ↓
用户选择安装
    ↓
下载插件文件
    ↓
验证插件签名
    ↓
检查权限要求
    ↓
用户确认权限
    ↓
安装到沙箱环境
    ↓
初始化插件运行时
    ↓
注册插件API
    ↓
加载插件配置
    ↓
启用插件功能
    ↓
插件安装完成
    ↓
[执行插件功能]
用户调用插件
    ↓
验证执行权限
    ↓
准备执行环境
    ↓
调用插件函数
    ↓
监控执行过程
    ↓
收集执行结果
    ↓
清理执行环境
    ↓
返回执行结果
```

## 9. 部署与运维

### 9.1 部署方案

**开发环境部署**
```bash
# 1. 克隆项目
git clone https://github.com/your-org/ai-studio.git
cd ai-studio

# 2. 安装前端依赖
npm install

# 3. 安装Rust工具链
curl --proto '=https' --tlsv1.2 -sSf https://sh.rustup.rs | sh
rustup update

# 4. 安装Tauri CLI
cargo install tauri-cli

# 5. 启动开发服务器
npm run tauri dev
```

**生产环境构建**
```bash
# 1. 构建前端资源
npm run build

# 2. 构建Tauri应用
npm run tauri build

# 3. 生成安装包
# Windows: .msi 和 .exe 安装包
# macOS: .dmg 和 .app 应用包
# Linux: .deb 和 .AppImage 包
```

**系统要求**
- **操作系统**：Windows 10+ / macOS 10.15+ / Ubuntu 18.04+
- **内存**：最低 4GB，推荐 8GB+
- **存储**：最低 10GB 可用空间
- **GPU**：可选，支持CUDA 11.0+或Metal
- **网络**：可选，用于模型下载和远程API

### 9.2 配置管理

**应用配置文件**
```json
{
  "app": {
    "name": "AI Studio",
    "version": "1.0.0",
    "data_dir": "~/.ai-studio",
    "log_level": "info",
    "max_log_files": 10
  },
  "database": {
    "sqlite_path": "data/app.db",
    "connection_pool_size": 10,
    "backup_interval": 3600
  },
  "ai": {
    "default_model": "llama2-7b-chat",
    "max_context_length": 4096,
    "inference_timeout": 300,
    "gpu_enabled": true,
    "gpu_layers": -1
  },
  "vector_db": {
    "chroma_host": "localhost",
    "chroma_port": 8000,
    "collection_prefix": "ai_studio",
    "embedding_model": "sentence-transformers/all-MiniLM-L6-v2"
  },
  "network": {
    "mdns_enabled": true,
    "p2p_port": 8080,
    "max_connections": 10,
    "encryption_enabled": true
  },
  "security": {
    "api_key_encryption": true,
    "session_timeout": 86400,
    "max_file_size": 104857600,
    "allowed_file_types": ["pdf", "docx", "txt", "md", "jpg", "png"]
  }
}
```

**环境变量配置**
```bash
# 数据目录
AI_STUDIO_DATA_DIR=/path/to/data

# 日志级别
AI_STUDIO_LOG_LEVEL=info

# GPU配置
AI_STUDIO_GPU_ENABLED=true
CUDA_VISIBLE_DEVICES=0

# 网络配置
AI_STUDIO_P2P_PORT=8080
AI_STUDIO_MDNS_ENABLED=true

# 安全配置
AI_STUDIO_ENCRYPTION_KEY=your-encryption-key
AI_STUDIO_SESSION_SECRET=your-session-secret
```

### 9.3 监控与维护

**性能监控指标**
- **系统资源**：CPU使用率、内存使用率、磁盘使用率
- **AI推理**：推理速度、模型加载时间、GPU利用率
- **数据库**：查询响应时间、连接池状态、存储使用量
- **网络**：连接数量、传输速度、错误率
- **用户行为**：活跃用户数、会话数量、功能使用统计

**日志管理**
```rust
// 日志配置示例
use tracing::{info, warn, error};
use tracing_subscriber::{layer::SubscriberExt, util::SubscriberInitExt};

fn init_logging() {
    tracing_subscriber::registry()
        .with(tracing_subscriber::EnvFilter::new("ai_studio=info"))
        .with(tracing_subscriber::fmt::layer())
        .with(tracing_appender::rolling::daily("logs", "ai-studio.log"))
        .init();
}

// 使用示例
info!("Application started successfully");
warn!("Model loading took longer than expected: {}ms", duration);
error!("Failed to connect to vector database: {}", error);
```

**健康检查**
```typescript
// 健康检查端点
interface HealthCheck {
  status: 'healthy' | 'warning' | 'error';
  timestamp: string;
  checks: {
    database: CheckResult;
    vector_db: CheckResult;
    ai_engine: CheckResult;
    disk_space: CheckResult;
    memory_usage: CheckResult;
  };
  uptime: number;
}

interface CheckResult {
  status: 'ok' | 'warning' | 'error';
  message?: string;
  value?: number;
  threshold?: number;
}
```

**自动备份策略**
- **增量备份**：每小时备份变更数据
- **全量备份**：每日备份完整数据库
- **模型备份**：定期备份重要模型文件
- **配置备份**：备份用户配置和设置
- **备份保留**：保留最近30天的备份文件

### 9.4 故障排除

**常见问题及解决方案**

1. **模型加载失败**
   - 检查模型文件完整性
   - 验证内存是否充足
   - 确认GPU驱动版本
   - 查看错误日志详情

2. **向量数据库连接失败**
   - 检查ChromaDB服务状态
   - 验证网络连接
   - 确认端口是否被占用
   - 重启向量数据库服务

3. **文档处理失败**
   - 检查文件格式支持
   - 验证文件大小限制
   - 确认磁盘空间充足
   - 查看处理日志

4. **网络共享问题**
   - 检查防火墙设置
   - 验证mDNS服务
   - 确认网络权限
   - 重置网络配置

**诊断工具**
```bash
# 系统诊断命令
ai-studio --diagnose

# 检查模型状态
ai-studio --check-models

# 验证数据库
ai-studio --verify-database

# 网络连接测试
ai-studio --test-network

# 清理缓存
ai-studio --clean-cache
```

### 9.5 测试策略

**单元测试**
```typescript
// 前端单元测试示例 (Vitest)
import { describe, it, expect } from 'vitest'
import { mount } from '@vue/test-utils'
import ChatMessage from '@/components/chat/ChatMessage.vue'

describe('ChatMessage', () => {
  it('renders user message correctly', () => {
    const wrapper = mount(ChatMessage, {
      props: {
        message: {
          role: 'user',
          content: 'Hello, AI!',
          timestamp: new Date().toISOString()
        }
      }
    })

    expect(wrapper.text()).toContain('Hello, AI!')
    expect(wrapper.classes()).toContain('user-message')
  })

  it('renders markdown content', () => {
    const wrapper = mount(ChatMessage, {
      props: {
        message: {
          role: 'assistant',
          content: '```python\nprint("Hello")\n```',
          timestamp: new Date().toISOString()
        }
      }
    })

    expect(wrapper.find('code').exists()).toBe(true)
  })
})
```

```rust
// 后端单元测试示例 (Rust)
#[cfg(test)]
mod tests {
    use super::*;
    use tokio_test;

    #[tokio::test]
    async fn test_chat_session_creation() {
        let session = ChatSession::new("test-session", "llama2-7b-chat").await;
        assert!(session.is_ok());

        let session = session.unwrap();
        assert_eq!(session.model_id, "llama2-7b-chat");
        assert!(session.messages.is_empty());
    }

    #[tokio::test]
    async fn test_message_processing() {
        let mut session = ChatSession::new("test", "llama2-7b-chat").await.unwrap();
        let result = session.add_message("user", "Hello").await;

        assert!(result.is_ok());
        assert_eq!(session.messages.len(), 1);
        assert_eq!(session.messages[0].content, "Hello");
    }

    #[test]
    fn test_document_chunking() {
        let content = "This is a test document. It has multiple sentences.";
        let chunks = chunk_document(content, 20, 5);

        assert!(!chunks.is_empty());
        assert!(chunks[0].len() <= 25); // chunk_size + overlap
    }
}
```

**集成测试**
```typescript
// API集成测试
describe('Chat API Integration', () => {
  it('should create session and send message', async () => {
    // 创建会话
    const sessionResponse = await fetch('/api/chat/sessions', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        title: 'Test Session',
        model_id: 'test-model'
      })
    })

    const session = await sessionResponse.json()
    expect(session.data.id).toBeDefined()

    // 发送消息
    const messageResponse = await fetch('/api/chat/send', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        session_id: session.data.id,
        message: 'Hello, AI!'
      })
    })

    const result = await messageResponse.json()
    expect(result.status).toBe('success')
  })
})
```

**端到端测试**
```typescript
// E2E测试示例 (Playwright)
import { test, expect } from '@playwright/test'

test('complete chat workflow', async ({ page }) => {
  // 启动应用
  await page.goto('/')

  // 创建新会话
  await page.click('[data-testid="new-chat-button"]')
  await page.fill('[data-testid="session-title"]', 'E2E Test Session')
  await page.click('[data-testid="create-session"]')

  // 发送消息
  await page.fill('[data-testid="message-input"]', 'Hello, AI!')
  await page.click('[data-testid="send-button"]')

  // 验证响应
  await expect(page.locator('[data-testid="message-list"]')).toContainText('Hello, AI!')
  await expect(page.locator('[data-testid="ai-response"]')).toBeVisible()
})

test('knowledge base upload', async ({ page }) => {
  await page.goto('/knowledge')

  // 创建知识库
  await page.click('[data-testid="create-kb-button"]')
  await page.fill('[data-testid="kb-name"]', 'Test KB')
  await page.click('[data-testid="create-kb"]')

  // 上传文档
  await page.setInputFiles('[data-testid="file-upload"]', 'test-document.pdf')
  await expect(page.locator('[data-testid="upload-progress"]')).toBeVisible()
  await expect(page.locator('[data-testid="document-list"]')).toContainText('test-document.pdf')
})
```

**性能测试**
```typescript
// 负载测试示例
import { check } from 'k6'
import http from 'k6/http'

export let options = {
  stages: [
    { duration: '2m', target: 10 }, // 逐步增加到10个用户
    { duration: '5m', target: 10 }, // 保持10个用户5分钟
    { duration: '2m', target: 20 }, // 增加到20个用户
    { duration: '5m', target: 20 }, // 保持20个用户5分钟
    { duration: '2m', target: 0 },  // 逐步减少到0
  ],
}

export default function() {
  // 测试聊天API性能
  let response = http.post('http://localhost:3000/api/chat/send',
    JSON.stringify({
      session_id: 'test-session',
      message: 'Performance test message'
    }),
    { headers: { 'Content-Type': 'application/json' } }
  )

  check(response, {
    'status is 200': (r) => r.status === 200,
    'response time < 2000ms': (r) => r.timings.duration < 2000,
  })
}
```

### 9.6 性能优化

**前端性能优化**
- **代码分割**：按路由和功能模块进行代码分割
- **懒加载**：组件和资源的按需加载
- **缓存策略**：合理使用浏览器缓存和应用缓存
- **虚拟滚动**：大列表使用虚拟滚动技术
- **防抖节流**：用户输入和API调用的防抖处理

```typescript
// 代码分割示例
const ChatView = defineAsyncComponent(() => import('@/views/Chat/index.vue'))
const KnowledgeView = defineAsyncComponent(() => import('@/views/Knowledge/index.vue'))

// 虚拟滚动示例
import { VirtualList } from '@tanstack/vue-virtual'

// 防抖处理
import { debounce } from 'lodash-es'

const debouncedSearch = debounce(async (query: string) => {
  const results = await searchAPI(query)
  searchResults.value = results
}, 300)
```

**后端性能优化**
- **连接池**：数据库和HTTP连接池优化
- **缓存层**：Redis缓存热点数据
- **异步处理**：耗时操作异步化
- **批量操作**：数据库批量读写
- **索引优化**：数据库索引优化

```rust
// 连接池配置
use sqlx::sqlite::SqlitePoolOptions;

let pool = SqlitePoolOptions::new()
    .max_connections(20)
    .min_connections(5)
    .acquire_timeout(Duration::from_secs(30))
    .connect(&database_url)
    .await?;

// 异步任务处理
use tokio::task;

let handle = task::spawn(async move {
    process_document(document_id).await
});

// 批量插入优化
let mut tx = pool.begin().await?;
for chunk in document_chunks.chunks(100) {
    sqlx::query("INSERT INTO chunks (content, embedding) VALUES (?, ?)")
        .bind_all(chunk)
        .execute(&mut tx)
        .await?;
}
tx.commit().await?;
```

**AI推理优化**
- **模型量化**：使用4位或8位量化减少内存占用
- **批处理**：批量处理推理请求
- **缓存机制**：缓存常见查询结果
- **GPU优化**：合理分配GPU资源
- **模型预热**：预加载常用模型

```rust
// 模型量化配置
let model_config = ModelConfig {
    quantization: QuantizationType::Q4_0,
    gpu_layers: 32,
    context_size: 4096,
    batch_size: 8,
};

// 推理缓存
use moka::future::Cache;

let inference_cache = Cache::builder()
    .max_capacity(1000)
    .time_to_live(Duration::from_secs(3600))
    .build();

// 批处理推理
async fn batch_inference(requests: Vec<InferenceRequest>) -> Vec<InferenceResult> {
    let batch_size = 8;
    let mut results = Vec::new();

    for batch in requests.chunks(batch_size) {
        let batch_results = model.infer_batch(batch).await?;
        results.extend(batch_results);
    }

    results
}
```

## 10. 详细开发功能点代码实现

### 10.1 前端核心组件代码

#### 10.1.1 主题切换组件 (ThemeToggle.vue)
```vue
<template>
  <div class="theme-toggle">
    <n-button
      :type="isDark ? 'primary' : 'default'"
      circle
      @click="toggleTheme"
      :title="$t('common.toggleTheme')"
    >
      <template #icon>
        <n-icon>
          <SunIcon v-if="isDark" />
          <MoonIcon v-else />
        </n-icon>
      </template>
    </n-button>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { NButton, NIcon } from 'naive-ui'
import { SunIcon, MoonIcon } from '@/components/icons'
import { useThemeStore } from '@/stores/theme'

const themeStore = useThemeStore()

const isDark = computed(() => themeStore.isDark)

const toggleTheme = () => {
  themeStore.toggleTheme()
}
</script>

<style scoped>
.theme-toggle {
  display: inline-flex;
  align-items: center;
}
</style>
```

#### 10.1.2 语言切换组件 (LanguageSwitch.vue)
```vue
<template>
  <n-dropdown
    :options="languageOptions"
    @select="handleLanguageChange"
    trigger="click"
  >
    <n-button circle>
      <template #icon>
        <n-icon>
          <LanguageIcon />
        </n-icon>
      </template>
    </n-button>
  </n-dropdown>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { NDropdown, NButton, NIcon } from 'naive-ui'
import { LanguageIcon } from '@/components/icons'
import { useI18nStore } from '@/stores/i18n'

const i18nStore = useI18nStore()

const languageOptions = computed(() => [
  {
    label: '中文',
    key: 'zh-CN',
    icon: () => '🇨🇳'
  },
  {
    label: 'English',
    key: 'en-US',
    icon: () => '🇺🇸'
  }
])

const handleLanguageChange = (key: string) => {
  i18nStore.setLocale(key)
}
</script>
```

#### 10.1.3 聊天消息组件 (ChatMessage.vue)
```vue
<template>
  <div
    class="chat-message"
    :class="{
      'user-message': message.role === 'user',
      'assistant-message': message.role === 'assistant',
      'system-message': message.role === 'system'
    }"
  >
    <div class="message-avatar">
      <n-avatar
        :src="avatarSrc"
        :fallback-src="defaultAvatar"
        size="small"
      />
    </div>

    <div class="message-content">
      <div class="message-header">
        <span class="message-role">{{ roleText }}</span>
        <span class="message-time">{{ formatTime(message.created_at) }}</span>
      </div>

      <div class="message-body">
        <MarkdownRenderer
          v-if="message.role === 'assistant'"
          :content="message.content"
          :enable-copy="true"
        />
        <div v-else class="plain-text">
          {{ message.content }}
        </div>

        <!-- 附件显示 -->
        <div v-if="message.attachments?.length" class="message-attachments">
          <AttachmentPreview
            v-for="attachment in message.attachments"
            :key="attachment.id"
            :attachment="attachment"
          />
        </div>
      </div>

      <div class="message-actions">
        <n-button-group size="small">
          <n-button @click="copyMessage" ghost>
            <template #icon>
              <n-icon><CopyIcon /></n-icon>
            </template>
            {{ $t('chat.copy') }}
          </n-button>

          <n-button
            v-if="message.role === 'assistant'"
            @click="regenerateMessage"
            ghost
          >
            <template #icon>
              <n-icon><RefreshIcon /></n-icon>
            </template>
            {{ $t('chat.regenerate') }}
          </n-button>

          <n-button @click="deleteMessage" ghost type="error">
            <template #icon>
              <n-icon><DeleteIcon /></n-icon>
            </template>
            {{ $t('chat.delete') }}
          </n-button>
        </n-button-group>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { NAvatar, NButton, NButtonGroup, NIcon } from 'naive-ui'
import { CopyIcon, RefreshIcon, DeleteIcon } from '@/components/icons'
import MarkdownRenderer from '@/components/common/MarkdownRenderer.vue'
import AttachmentPreview from '@/components/common/AttachmentPreview.vue'
import { useChatStore } from '@/stores/chat'
import { useI18n } from 'vue-i18n'
import { formatDistanceToNow } from 'date-fns'
import { zhCN, enUS } from 'date-fns/locale'

interface Props {
  message: ChatMessage
}

const props = defineProps<Props>()
const emit = defineEmits<{
  regenerate: [messageId: string]
  delete: [messageId: string]
}>()

const { t, locale } = useI18n()
const chatStore = useChatStore()

const avatarSrc = computed(() => {
  if (props.message.role === 'user') {
    return chatStore.userAvatar
  } else if (props.message.role === 'assistant') {
    return '/icons/ai-avatar.png'
  }
  return '/icons/system-avatar.png'
})

const defaultAvatar = computed(() => {
  return props.message.role === 'user' ? '👤' : '🤖'
})

const roleText = computed(() => {
  return t(`chat.roles.${props.message.role}`)
})

const formatTime = (timestamp: string) => {
  const date = new Date(timestamp)
  const localeMap = {
    'zh-CN': zhCN,
    'en-US': enUS
  }

  return formatDistanceToNow(date, {
    addSuffix: true,
    locale: localeMap[locale.value as keyof typeof localeMap]
  })
}

const copyMessage = async () => {
  try {
    await navigator.clipboard.writeText(props.message.content)
    window.$message.success(t('chat.copySuccess'))
  } catch (error) {
    window.$message.error(t('chat.copyFailed'))
  }
}

const regenerateMessage = () => {
  emit('regenerate', props.message.id)
}

const deleteMessage = () => {
  emit('delete', props.message.id)
}
</script>

<style scoped>
.chat-message {
  display: flex;
  gap: 12px;
  padding: 16px;
  margin-bottom: 16px;
  border-radius: 8px;
  transition: background-color 0.2s;
}

.chat-message:hover {
  background-color: var(--n-color-hover);
}

.user-message {
  flex-direction: row-reverse;
}

.user-message .message-content {
  text-align: right;
}

.assistant-message .message-content {
  text-align: left;
}

.system-message {
  justify-content: center;
  opacity: 0.7;
}

.message-avatar {
  flex-shrink: 0;
}

.message-content {
  flex: 1;
  min-width: 0;
}

.message-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
  font-size: 12px;
  opacity: 0.7;
}

.message-body {
  margin-bottom: 8px;
}

.plain-text {
  white-space: pre-wrap;
  word-break: break-word;
}

.message-attachments {
  margin-top: 8px;
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.message-actions {
  opacity: 0;
  transition: opacity 0.2s;
}

.chat-message:hover .message-actions {
  opacity: 1;
}
</style>
```

### 10.2 状态管理 (Pinia Store)

#### 10.2.1 主题状态管理 (stores/theme.ts)
```typescript
import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { darkTheme, lightTheme } from 'naive-ui'

export const useThemeStore = defineStore('theme', () => {
  const isDark = ref(false)

  // 从本地存储加载主题设置
  const loadTheme = () => {
    const saved = localStorage.getItem('ai-studio-theme')
    if (saved) {
      isDark.value = saved === 'dark'
    } else {
      // 检测系统主题偏好
      isDark.value = window.matchMedia('(prefers-color-scheme: dark)').matches
    }
    applyTheme()
  }

  // 应用主题
  const applyTheme = () => {
    document.documentElement.setAttribute('data-theme', isDark.value ? 'dark' : 'light')
    localStorage.setItem('ai-studio-theme', isDark.value ? 'dark' : 'light')
  }

  // 切换主题
  const toggleTheme = () => {
    isDark.value = !isDark.value
    applyTheme()
  }

  // 设置主题
  const setTheme = (theme: 'light' | 'dark') => {
    isDark.value = theme === 'dark'
    applyTheme()
  }

  // Naive UI 主题配置
  const naiveTheme = computed(() => {
    return isDark.value ? darkTheme : lightTheme
  })

  // 主题变量
  const themeVars = computed(() => {
    if (isDark.value) {
      return {
        primaryColor: '#18a058',
        primaryColorHover: '#36ad6a',
        primaryColorPressed: '#0c7a43',
        primaryColorSuppl: '#36ad6a',
        infoColor: '#2080f0',
        successColor: '#18a058',
        warningColor: '#f0a020',
        errorColor: '#d03050',
        textColorBase: '#ffffff',
        textColor1: 'rgba(255, 255, 255, 0.9)',
        textColor2: 'rgba(255, 255, 255, 0.82)',
        textColor3: 'rgba(255, 255, 255, 0.52)',
        bodyColor: '#101014',
        cardColor: '#18181c',
        modalColor: '#1e1e20',
        popoverColor: '#1e1e20',
        tableHeaderColor: '#1e1e20',
        inputColor: 'rgba(255, 255, 255, 0.1)',
        codeColor: 'rgba(255, 255, 255, 0.12)',
        tabColor: 'rgba(255, 255, 255, 0.04)',
        actionColor: 'rgba(255, 255, 255, 0.06)',
        tableColorHover: 'rgba(255, 255, 255, 0.06)',
        tableColorStriped: 'rgba(255, 255, 255, 0.02)',
        hoverColor: 'rgba(255, 255, 255, 0.09)',
        dividerColor: 'rgba(255, 255, 255, 0.09)',
        borderColor: 'rgba(255, 255, 255, 0.24)',
        closeIconColor: 'rgba(255, 255, 255, 0.52)',
        closeIconColorHover: 'rgba(255, 255, 255, 0.82)',
        closeIconColorPressed: 'rgba(255, 255, 255, 0.9)',
        clearColor: 'rgba(255, 255, 255, 0.52)',
        clearColorHover: 'rgba(255, 255, 255, 0.82)',
        clearColorPressed: 'rgba(255, 255, 255, 0.9)',
        scrollbarColor: 'rgba(255, 255, 255, 0.2)',
        scrollbarColorHover: 'rgba(255, 255, 255, 0.3)',
        boxShadow1: '0 1px 2px -2px rgba(0, 0, 0, .24), 0 3px 6px 0 rgba(0, 0, 0, .18), 0 5px 12px 4px rgba(0, 0, 0, .12)',
        boxShadow2: '0 3px 6px -4px rgba(0, 0, 0, .24), 0 6px 16px 0 rgba(0, 0, 0, .18), 0 9px 28px 8px rgba(0, 0, 0, .12)',
        boxShadow3: '0 6px 16px -9px rgba(0, 0, 0, .24), 0 9px 28px 0 rgba(0, 0, 0, .18), 0 12px 48px 16px rgba(0, 0, 0, .12)'
      }
    } else {
      return {
        primaryColor: '#18a058',
        primaryColorHover: '#36ad6a',
        primaryColorPressed: '#0c7a43',
        primaryColorSuppl: '#36ad6a',
        infoColor: '#2080f0',
        successColor: '#18a058',
        warningColor: '#f0a020',
        errorColor: '#d03050',
        textColorBase: '#000000',
        textColor1: 'rgba(0, 0, 0, 0.9)',
        textColor2: 'rgba(0, 0, 0, 0.82)',
        textColor3: 'rgba(0, 0, 0, 0.52)',
        bodyColor: '#ffffff',
        cardColor: '#ffffff',
        modalColor: '#ffffff',
        popoverColor: '#ffffff',
        tableHeaderColor: '#fafafa',
        inputColor: '#ffffff',
        codeColor: 'rgba(0, 0, 0, 0.06)',
        tabColor: 'rgba(0, 0, 0, 0.04)',
        actionColor: 'rgba(0, 0, 0, 0.02)',
        tableColorHover: 'rgba(0, 0, 0, 0.02)',
        tableColorStriped: 'rgba(0, 0, 0, 0.01)',
        hoverColor: 'rgba(0, 0, 0, 0.03)',
        dividerColor: 'rgba(0, 0, 0, 0.06)',
        borderColor: 'rgba(0, 0, 0, 0.15)',
        closeIconColor: 'rgba(0, 0, 0, 0.45)',
        closeIconColorHover: 'rgba(0, 0, 0, 0.65)',
        closeIconColorPressed: 'rgba(0, 0, 0, 0.85)',
        clearColor: 'rgba(0, 0, 0, 0.45)',
        clearColorHover: 'rgba(0, 0, 0, 0.65)',
        clearColorPressed: 'rgba(0, 0, 0, 0.85)',
        scrollbarColor: 'rgba(0, 0, 0, 0.25)',
        scrollbarColorHover: 'rgba(0, 0, 0, 0.4)',
        boxShadow1: '0 1px 2px -2px rgba(0, 0, 0, .08), 0 3px 6px 0 rgba(0, 0, 0, .06), 0 5px 12px 4px rgba(0, 0, 0, .04)',
        boxShadow2: '0 3px 6px -4px rgba(0, 0, 0, .12), 0 6px 16px 0 rgba(0, 0, 0, .08), 0 9px 28px 8px rgba(0, 0, 0, .05)',
        boxShadow3: '0 6px 16px -9px rgba(0, 0, 0, .08), 0 9px 28px 0 rgba(0, 0, 0, .06), 0 12px 48px 16px rgba(0, 0, 0, .03)'
      }
    }
  })

  return {
    isDark,
    naiveTheme,
    themeVars,
    loadTheme,
    toggleTheme,
    setTheme
  }
})
```

#### 10.2.2 聊天状态管理 (stores/chat.ts)
```typescript
import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { chatAPI } from '@/api/chat'
import type { ChatSession, ChatMessage, ModelConfig } from '@/types/chat'

export const useChatStore = defineStore('chat', () => {
  const sessions = ref<ChatSession[]>([])
  const currentSessionId = ref<string | null>(null)
  const messages = ref<ChatMessage[]>([])
  const isLoading = ref(false)
  const isStreaming = ref(false)
  const userAvatar = ref('/avatars/default-user.png')

  // 计算属性
  const currentSession = computed(() => {
    return sessions.value.find(s => s.id === currentSessionId.value)
  })

  const sortedSessions = computed(() => {
    return [...sessions.value].sort((a, b) =>
      new Date(b.updated_at).getTime() - new Date(a.updated_at).getTime()
    )
  })

  // 加载会话列表
  const loadSessions = async () => {
    try {
      const response = await chatAPI.getSessions()
      sessions.value = response.data.items
    } catch (error) {
      console.error('Failed to load sessions:', error)
      throw error
    }
  }

  // 创建新会话
  const createSession = async (title?: string, modelId?: string) => {
    try {
      const response = await chatAPI.createSession({
        title: title || `新对话 ${new Date().toLocaleString()}`,
        model_id: modelId,
        system_prompt: '',
        config: {
          temperature: 0.7,
          max_tokens: 2048,
          top_p: 0.9
        }
      })

      const newSession = response.data
      sessions.value.unshift(newSession)
      currentSessionId.value = newSession.id
      messages.value = []

      return newSession
    } catch (error) {
      console.error('Failed to create session:', error)
      throw error
    }
  }

  // 切换会话
  const switchSession = async (sessionId: string) => {
    if (currentSessionId.value === sessionId) return

    currentSessionId.value = sessionId
    await loadMessages(sessionId)
  }

  // 加载消息
  const loadMessages = async (sessionId: string) => {
    try {
      isLoading.value = true
      const response = await chatAPI.getMessages(sessionId)
      messages.value = response.data.items
    } catch (error) {
      console.error('Failed to load messages:', error)
      throw error
    } finally {
      isLoading.value = false
    }
  }

  // 发送消息
  const sendMessage = async (
    content: string,
    attachments?: any[],
    modelConfig?: ModelConfig
  ) => {
    if (!currentSessionId.value) {
      throw new Error('No active session')
    }

    try {
      isLoading.value = true

      // 添加用户消息到本地状态
      const userMessage: ChatMessage = {
        id: `temp-${Date.now()}`,
        session_id: currentSessionId.value,
        role: 'user',
        content,
        attachments,
        created_at: new Date().toISOString()
      }
      messages.value.push(userMessage)

      // 发送到后端
      const response = await chatAPI.sendMessage({
        session_id: currentSessionId.value,
        message: content,
        attachments,
        model_config: modelConfig
      })

      // 更新用户消息ID
      const messageIndex = messages.value.findIndex(m => m.id === userMessage.id)
      if (messageIndex !== -1) {
        messages.value[messageIndex].id = response.data.message_id
      }

      // 开始接收流式响应
      await receiveStreamResponse(response.data.message_id)

    } catch (error) {
      console.error('Failed to send message:', error)
      throw error
    } finally {
      isLoading.value = false
    }
  }

  // 接收流式响应
  const receiveStreamResponse = async (messageId: string) => {
    return new Promise<void>((resolve, reject) => {
      isStreaming.value = true

      // 创建AI消息占位符
      const aiMessage: ChatMessage = {
        id: `ai-${messageId}`,
        session_id: currentSessionId.value!,
        role: 'assistant',
        content: '',
        created_at: new Date().toISOString()
      }
      messages.value.push(aiMessage)

      const eventSource = new EventSource(`/api/chat/stream/${messageId}`)

      eventSource.onmessage = (event) => {
        try {
          const data = JSON.parse(event.data)

          if (data.type === 'token') {
            // 更新消息内容
            const messageIndex = messages.value.findIndex(m => m.id === aiMessage.id)
            if (messageIndex !== -1) {
              messages.value[messageIndex].content += data.content
            }
          } else if (data.type === 'done') {
            // 响应完成
            const messageIndex = messages.value.findIndex(m => m.id === aiMessage.id)
            if (messageIndex !== -1) {
              messages.value[messageIndex].id = data.message_id || aiMessage.id
              messages.value[messageIndex].tokens_used = data.metadata?.tokens_used
              messages.value[messageIndex].response_time = data.metadata?.response_time
            }

            eventSource.close()
            isStreaming.value = false
            resolve()
          } else if (data.type === 'error') {
            // 处理错误
            console.error('Stream error:', data.content)
            eventSource.close()
            isStreaming.value = false
            reject(new Error(data.content))
          }
        } catch (error) {
          console.error('Failed to parse stream data:', error)
        }
      }

      eventSource.onerror = (error) => {
        console.error('EventSource error:', error)
        eventSource.close()
        isStreaming.value = false
        reject(error)
      }
    })
  }

  // 重新生成消息
  const regenerateMessage = async (messageId: string) => {
    const messageIndex = messages.value.findIndex(m => m.id === messageId)
    if (messageIndex === -1) return

    const message = messages.value[messageIndex]
    if (message.role !== 'assistant') return

    // 找到对应的用户消息
    const userMessageIndex = messageIndex - 1
    if (userMessageIndex < 0) return

    const userMessage = messages.value[userMessageIndex]

    // 删除AI消息
    messages.value.splice(messageIndex, 1)

    // 重新发送用户消息
    await sendMessage(userMessage.content, userMessage.attachments)
  }

  // 删除消息
  const deleteMessage = async (messageId: string) => {
    const messageIndex = messages.value.findIndex(m => m.id === messageId)
    if (messageIndex !== -1) {
      messages.value.splice(messageIndex, 1)
    }
  }

  // 更新会话
  const updateSession = async (sessionId: string, updates: Partial<ChatSession>) => {
    try {
      const response = await chatAPI.updateSession(sessionId, updates)
      const sessionIndex = sessions.value.findIndex(s => s.id === sessionId)
      if (sessionIndex !== -1) {
        sessions.value[sessionIndex] = { ...sessions.value[sessionIndex], ...response.data }
      }
    } catch (error) {
      console.error('Failed to update session:', error)
      throw error
    }
  }

  // 删除会话
  const deleteSession = async (sessionId: string) => {
    try {
      await chatAPI.deleteSession(sessionId)
      const sessionIndex = sessions.value.findIndex(s => s.id === sessionId)
      if (sessionIndex !== -1) {
        sessions.value.splice(sessionIndex, 1)
      }

      if (currentSessionId.value === sessionId) {
        currentSessionId.value = sessions.value[0]?.id || null
        if (currentSessionId.value) {
          await loadMessages(currentSessionId.value)
        } else {
          messages.value = []
        }
      }
    } catch (error) {
      console.error('Failed to delete session:', error)
      throw error
    }
  }

  // 清空当前会话消息
  const clearCurrentSession = async () => {
    if (!currentSessionId.value) return

    try {
      // 这里可以调用API清空会话消息
      messages.value = []
    } catch (error) {
      console.error('Failed to clear session:', error)
      throw error
    }
  }

  return {
    // 状态
    sessions,
    currentSessionId,
    messages,
    isLoading,
    isStreaming,
    userAvatar,

    // 计算属性
    currentSession,
    sortedSessions,

    // 方法
    loadSessions,
    createSession,
    switchSession,
    loadMessages,
    sendMessage,
    regenerateMessage,
    deleteMessage,
    updateSession,
    deleteSession,
    clearCurrentSession
  }
})
```

### 10.3 后端核心模块代码

#### 10.3.1 聊天服务模块 (src-tauri/src/chat/mod.rs)
```rust
use serde::{Deserialize, Serialize};
use std::collections::HashMap;
use tokio::sync::{mpsc, RwLock};
use uuid::Uuid;
use anyhow::Result;

pub mod session;
pub mod message;
pub mod streaming;

use session::ChatSession;
use message::ChatMessage;

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ChatConfig {
    pub model_id: String,
    pub temperature: f32,
    pub max_tokens: u32,
    pub top_p: f32,
    pub system_prompt: Option<String>,
}

impl Default for ChatConfig {
    fn default() -> Self {
        Self {
            model_id: "default".to_string(),
            temperature: 0.7,
            max_tokens: 2048,
            top_p: 0.9,
            system_prompt: None,
        }
    }
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SendMessageRequest {
    pub session_id: String,
    pub message: String,
    pub attachments: Option<Vec<MessageAttachment>>,
    pub model_config: Option<ChatConfig>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct MessageAttachment {
    pub id: String,
    pub r#type: String, // image, document, audio
    pub url: String,
    pub name: String,
    pub size: u64,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct StreamToken {
    pub r#type: String, // token, done, error
    pub content: String,
    pub metadata: Option<HashMap<String, serde_json::Value>>,
}

pub struct ChatService {
    sessions: RwLock<HashMap<String, ChatSession>>,
    active_streams: RwLock<HashMap<String, mpsc::Sender<StreamToken>>>,
}

impl ChatService {
    pub fn new() -> Self {
        Self {
            sessions: RwLock::new(HashMap::new()),
            active_streams: RwLock::new(HashMap::new()),
        }
    }

    pub async fn create_session(
        &self,
        title: Option<String>,
        config: Option<ChatConfig>,
    ) -> Result<ChatSession> {
        let session_id = Uuid::new_v4().to_string();
        let session = ChatSession::new(
            session_id.clone(),
            title.unwrap_or_else(|| format!("Chat {}", chrono::Utc::now().format("%Y-%m-%d %H:%M"))),
            config.unwrap_or_default(),
        );

        // 保存到数据库
        session.save_to_db().await?;

        // 缓存到内存
        self.sessions.write().await.insert(session_id, session.clone());

        Ok(session)
    }

    pub async fn get_session(&self, session_id: &str) -> Result<Option<ChatSession>> {
        // 先从内存缓存查找
        if let Some(session) = self.sessions.read().await.get(session_id) {
            return Ok(Some(session.clone()));
        }

        // 从数据库加载
        if let Some(session) = ChatSession::load_from_db(session_id).await? {
            self.sessions.write().await.insert(session_id.to_string(), session.clone());
            Ok(Some(session))
        } else {
            Ok(None)
        }
    }

    pub async fn send_message(
        &self,
        request: SendMessageRequest,
    ) -> Result<String> {
        let session = self.get_session(&request.session_id).await?
            .ok_or_else(|| anyhow::anyhow!("Session not found"))?;

        // 创建用户消息
        let user_message = ChatMessage::new(
            request.session_id.clone(),
            "user".to_string(),
            request.message,
            request.attachments,
        );

        // 保存用户消息
        user_message.save_to_db().await?;

        // 创建AI响应消息
        let ai_message_id = Uuid::new_v4().to_string();
        let ai_message = ChatMessage::new(
            request.session_id.clone(),
            "assistant".to_string(),
            String::new(),
            None,
        );

        // 开始流式推理
        self.start_inference(
            ai_message_id.clone(),
            session,
            user_message,
            request.model_config,
        ).await?;

        Ok(ai_message_id)
    }

    async fn start_inference(
        &self,
        message_id: String,
        session: ChatSession,
        user_message: ChatMessage,
        config: Option<ChatConfig>,
    ) -> Result<()> {
        let (tx, mut rx) = mpsc::channel::<StreamToken>(100);

        // 注册流
        self.active_streams.write().await.insert(message_id.clone(), tx);

        // 启动推理任务
        let inference_config = config.unwrap_or(session.config);
        tokio::spawn(async move {
            // 这里调用AI推理引擎
            // 示例：调用本地模型或远程API
            match perform_inference(&session, &user_message, &inference_config).await {
                Ok(response_stream) => {
                    // 处理流式响应
                    let mut content = String::new();

                    while let Some(token) = response_stream.next().await {
                        content.push_str(&token);

                        let stream_token = StreamToken {
                            r#type: "token".to_string(),
                            content: token,
                            metadata: None,
                        };

                        if tx.send(stream_token).await.is_err() {
                            break;
                        }
                    }

                    // 发送完成信号
                    let done_token = StreamToken {
                        r#type: "done".to_string(),
                        content: String::new(),
                        metadata: Some({
                            let mut meta = HashMap::new();
                            meta.insert("tokens_used".to_string(), serde_json::Value::Number(content.len().into()));
                            meta.insert("response_time".to_string(), serde_json::Value::Number(1000.into()));
                            meta
                        }),
                    };

                    let _ = tx.send(done_token).await;

                    // 保存完整的AI消息到数据库
                    let ai_message = ChatMessage::new(
                        session.id,
                        "assistant".to_string(),
                        content,
                        None,
                    );
                    let _ = ai_message.save_to_db().await;
                }
                Err(error) => {
                    let error_token = StreamToken {
                        r#type: "error".to_string(),
                        content: error.to_string(),
                        metadata: None,
                    };
                    let _ = tx.send(error_token).await;
                }
            }
        });

        Ok(())
    }

    pub async fn get_stream_receiver(&self, message_id: &str) -> Option<mpsc::Receiver<StreamToken>> {
        // 这里需要实现流接收器的获取逻辑
        // 由于Rust的所有权限制，这里需要特殊处理
        None
    }
}

// AI推理函数（示例）
async fn perform_inference(
    session: &ChatSession,
    user_message: &ChatMessage,
    config: &ChatConfig,
) -> Result<impl futures::Stream<Item = String>> {
    // 这里是AI推理的具体实现
    // 可以调用本地模型（如llama.cpp）或远程API

    use futures::stream;

    // 示例：返回一个模拟的流
    let tokens = vec![
        "Hello".to_string(),
        " there".to_string(),
        "! How".to_string(),
        " can".to_string(),
        " I".to_string(),
        " help".to_string(),
        " you".to_string(),
        " today".to_string(),
        "?".to_string(),
    ];

    Ok(stream::iter(tokens))
}
```

## 11. 完整数据库设计方案

### 11.1 数据库架构设计

AI Studio 采用混合数据库架构，结合关系型数据库和向量数据库的优势：

- **SQLite 主数据库**：存储结构化数据，如用户信息、会话记录、系统配置等
- **ChromaDB 向量数据库**：存储文档向量和实现语义搜索
- **本地文件存储**：存储模型文件、文档文件、媒体文件等

```
┌─────────────────────────────────────────────────────────────┐
│                    应用层 (Application)                     │
├─────────────────────────────────────────────────────────────┤
│                    数据访问层 (DAL)                         │
│  Repository Pattern + ORM + Connection Pool                │
├─────────────────────────────────────────────────────────────┤
│  SQLite 主数据库    │  ChromaDB 向量库  │  本地文件存储     │
│  - 用户数据         │  - 文档向量       │  - 模型文件       │
│  - 会话记录         │  - 语义搜索       │  - 文档文件       │
│  - 系统配置         │  - 知识图谱       │  - 媒体文件       │
│  - 元数据管理       │  - 相似度计算     │  - 缓存文件       │
└─────────────────────────────────────────────────────────────┘
```

### 11.2 SQLite 主数据库设计

#### 11.2.1 核心表结构

**用户和认证表**
```sql
-- 用户表
CREATE TABLE users (
    id TEXT PRIMARY KEY DEFAULT (lower(hex(randomblob(16)))),
    username TEXT UNIQUE NOT NULL,
    display_name TEXT NOT NULL,
    email TEXT UNIQUE,
    avatar_url TEXT,
    preferences JSON DEFAULT '{}',
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    last_login_at DATETIME,
    is_active BOOLEAN DEFAULT 1
);

-- 用户会话表（登录会话）
CREATE TABLE user_sessions (
    id TEXT PRIMARY KEY DEFAULT (lower(hex(randomblob(16)))),
    user_id TEXT NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    session_token TEXT UNIQUE NOT NULL,
    expires_at DATETIME NOT NULL,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    last_accessed_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    ip_address TEXT,
    user_agent TEXT
);

-- 用户设置表
CREATE TABLE user_settings (
    id TEXT PRIMARY KEY DEFAULT (lower(hex(randomblob(16)))),
    user_id TEXT NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    category TEXT NOT NULL, -- 'appearance', 'language', 'ai', 'privacy'
    key TEXT NOT NULL,
    value TEXT NOT NULL,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(user_id, category, key)
);
```

**聊天相关表**
```sql
-- 聊天会话表
CREATE TABLE chat_sessions (
    id TEXT PRIMARY KEY DEFAULT (lower(hex(randomblob(16)))),
    user_id TEXT NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    title TEXT NOT NULL,
    model_id TEXT,
    system_prompt TEXT,
    config JSON DEFAULT '{}', -- 温度、最大token等配置
    message_count INTEGER DEFAULT 0,
    total_tokens INTEGER DEFAULT 0,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    is_archived BOOLEAN DEFAULT 0,
    is_pinned BOOLEAN DEFAULT 0
);

-- 聊天消息表
CREATE TABLE chat_messages (
    id TEXT PRIMARY KEY DEFAULT (lower(hex(randomblob(16)))),
    session_id TEXT NOT NULL REFERENCES chat_sessions(id) ON DELETE CASCADE,
    parent_id TEXT REFERENCES chat_messages(id), -- 支持消息树结构
    role TEXT NOT NULL CHECK (role IN ('user', 'assistant', 'system')),
    content TEXT NOT NULL,
    content_type TEXT DEFAULT 'text', -- 'text', 'markdown', 'code'
    attachments JSON, -- 附件信息
    metadata JSON, -- 额外元数据
    tokens_used INTEGER,
    response_time REAL, -- 响应时间（秒）
    model_used TEXT, -- 使用的模型
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    is_deleted BOOLEAN DEFAULT 0
);

-- 消息反馈表
CREATE TABLE message_feedback (
    id TEXT PRIMARY KEY DEFAULT (lower(hex(randomblob(16)))),
    message_id TEXT NOT NULL REFERENCES chat_messages(id) ON DELETE CASCADE,
    user_id TEXT NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    feedback_type TEXT NOT NULL CHECK (feedback_type IN ('like', 'dislike', 'report')),
    comment TEXT,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(message_id, user_id, feedback_type)
);
```

**知识库相关表**
```sql
-- 知识库表
CREATE TABLE knowledge_bases (
    id TEXT PRIMARY KEY DEFAULT (lower(hex(randomblob(16)))),
    user_id TEXT NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    name TEXT NOT NULL,
    description TEXT,
    embedding_model TEXT DEFAULT 'sentence-transformers/all-MiniLM-L6-v2',
    chunk_size INTEGER DEFAULT 512,
    chunk_overlap INTEGER DEFAULT 50,
    document_count INTEGER DEFAULT 0,
    total_chunks INTEGER DEFAULT 0,
    total_size INTEGER DEFAULT 0, -- 总大小（字节）
    status TEXT DEFAULT 'active' CHECK (status IN ('active', 'processing', 'error', 'archived')),
    config JSON DEFAULT '{}',
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    last_indexed_at DATETIME
);

-- 文档表
CREATE TABLE documents (
    id TEXT PRIMARY KEY DEFAULT (lower(hex(randomblob(16)))),
    kb_id TEXT NOT NULL REFERENCES knowledge_bases(id) ON DELETE CASCADE,
    name TEXT NOT NULL,
    original_name TEXT NOT NULL,
    file_type TEXT NOT NULL, -- 'pdf', 'docx', 'txt', 'md', 'html'
    mime_type TEXT,
    file_size INTEGER NOT NULL,
    file_path TEXT NOT NULL, -- 本地文件路径
    file_hash TEXT NOT NULL, -- 文件哈希值，用于去重
    content_preview TEXT, -- 内容预览（前500字符）
    page_count INTEGER, -- 页数（适用于PDF等）
    word_count INTEGER, -- 字数
    language TEXT DEFAULT 'auto', -- 文档语言
    status TEXT DEFAULT 'pending' CHECK (status IN ('pending', 'processing', 'completed', 'failed', 'archived')),
    processing_progress REAL DEFAULT 0.0, -- 处理进度 0.0-1.0
    error_message TEXT, -- 错误信息
    chunks_count INTEGER DEFAULT 0,
    metadata JSON, -- 文档元数据
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    processed_at DATETIME,
    UNIQUE(kb_id, file_hash) -- 同一知识库内文件去重
);

-- 文档块表（用于存储分块信息）
CREATE TABLE document_chunks (
    id TEXT PRIMARY KEY DEFAULT (lower(hex(randomblob(16)))),
    document_id TEXT NOT NULL REFERENCES documents(id) ON DELETE CASCADE,
    kb_id TEXT NOT NULL REFERENCES knowledge_bases(id) ON DELETE CASCADE,
    chunk_index INTEGER NOT NULL, -- 块索引
    content TEXT NOT NULL, -- 块内容
    content_hash TEXT NOT NULL, -- 内容哈希
    token_count INTEGER, -- token数量
    page_number INTEGER, -- 页码（如果适用）
    section_title TEXT, -- 章节标题
    metadata JSON, -- 块元数据
    vector_id TEXT, -- 对应的向量ID（ChromaDB中的ID）
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(document_id, chunk_index)
);

-- 知识库搜索历史
CREATE TABLE kb_search_history (
    id TEXT PRIMARY KEY DEFAULT (lower(hex(randomblob(16)))),
    kb_id TEXT NOT NULL REFERENCES knowledge_bases(id) ON DELETE CASCADE,
    user_id TEXT NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    query TEXT NOT NULL,
    results_count INTEGER DEFAULT 0,
    search_time REAL, -- 搜索耗时
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP
);
```

**模型管理相关表**
```sql
-- 模型表
CREATE TABLE models (
    id TEXT PRIMARY KEY DEFAULT (lower(hex(randomblob(16)))),
    name TEXT NOT NULL, -- 模型标识符
    display_name TEXT NOT NULL, -- 显示名称
    description TEXT,
    author TEXT,
    version TEXT,
    model_type TEXT NOT NULL CHECK (model_type IN ('local', 'remote', 'api')),
    architecture TEXT, -- 'llama', 'mistral', 'qwen', 'gpt', 'claude'
    parameter_count TEXT, -- 参数量 '7B', '13B', '70B'
    quantization TEXT, -- 量化类型 'none', 'q4_0', 'q4_1', 'q8_0'
    context_length INTEGER DEFAULT 2048,
    file_size INTEGER, -- 文件大小（字节）
    local_path TEXT, -- 本地文件路径
    download_url TEXT, -- 下载URL
    huggingface_id TEXT, -- HuggingFace模型ID
    config JSON DEFAULT '{}', -- 模型配置
    capabilities JSON DEFAULT '[]', -- 模型能力 ['chat', 'completion', 'embedding']
    status TEXT DEFAULT 'available' CHECK (status IN ('available', 'downloading', 'loaded', 'error', 'archived')),
    load_time REAL, -- 加载时间
    memory_usage INTEGER, -- 内存使用量（MB）
    gpu_memory_usage INTEGER, -- GPU内存使用量（MB）
    performance_score REAL, -- 性能评分
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    last_used_at DATETIME,
    UNIQUE(name, version)
);

-- 模型下载任务表
CREATE TABLE model_downloads (
    id TEXT PRIMARY KEY DEFAULT (lower(hex(randomblob(16)))),
    model_id TEXT REFERENCES models(id) ON DELETE CASCADE,
    download_url TEXT NOT NULL,
    local_path TEXT NOT NULL,
    total_size INTEGER, -- 总大小
    downloaded_size INTEGER DEFAULT 0, -- 已下载大小
    progress REAL DEFAULT 0.0, -- 下载进度 0.0-1.0
    speed INTEGER DEFAULT 0, -- 下载速度（字节/秒）
    status TEXT DEFAULT 'pending' CHECK (status IN ('pending', 'downloading', 'paused', 'completed', 'failed', 'cancelled')),
    error_message TEXT,
    retry_count INTEGER DEFAULT 0,
    max_retries INTEGER DEFAULT 3,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    started_at DATETIME,
    completed_at DATETIME
);

-- 模型使用统计表
CREATE TABLE model_usage_stats (
    id TEXT PRIMARY KEY DEFAULT (lower(hex(randomblob(16)))),
    model_id TEXT NOT NULL REFERENCES models(id) ON DELETE CASCADE,
    user_id TEXT REFERENCES users(id) ON DELETE SET NULL,
    session_id TEXT REFERENCES chat_sessions(id) ON DELETE SET NULL,
    usage_type TEXT NOT NULL CHECK (usage_type IN ('chat', 'completion', 'embedding')),
    tokens_input INTEGER DEFAULT 0,
    tokens_output INTEGER DEFAULT 0,
    response_time REAL, -- 响应时间（秒）
    memory_peak INTEGER, -- 峰值内存使用（MB）
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP
);
```

**多模态处理相关表**
```sql
-- 多模态任务表
CREATE TABLE multimodal_tasks (
    id TEXT PRIMARY KEY DEFAULT (lower(hex(randomblob(16)))),
    user_id TEXT NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    task_type TEXT NOT NULL CHECK (task_type IN ('ocr', 'tts', 'asr', 'image_analysis', 'video_analysis', 'format_conversion')),
    input_file_path TEXT NOT NULL,
    input_file_type TEXT NOT NULL,
    input_file_size INTEGER NOT NULL,
    output_file_path TEXT,
    output_file_type TEXT,
    output_file_size INTEGER,
    parameters JSON DEFAULT '{}', -- 任务参数
    progress REAL DEFAULT 0.0,
    status TEXT DEFAULT 'pending' CHECK (status IN ('pending', 'processing', 'completed', 'failed', 'cancelled')),
    error_message TEXT,
    processing_time REAL, -- 处理时间（秒）
    result_data JSON, -- 处理结果数据
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    started_at DATETIME,
    completed_at DATETIME
);

-- OCR结果表
CREATE TABLE ocr_results (
    id TEXT PRIMARY KEY DEFAULT (lower(hex(randomblob(16)))),
    task_id TEXT NOT NULL REFERENCES multimodal_tasks(id) ON DELETE CASCADE,
    page_number INTEGER DEFAULT 1,
    text_content TEXT NOT NULL,
    confidence REAL, -- 识别置信度
    bounding_boxes JSON, -- 边界框信息
    language TEXT,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP
);
```

### 11.3 数据库索引设计

```sql
-- 性能优化索引
CREATE INDEX idx_chat_sessions_user_updated ON chat_sessions(user_id, updated_at DESC);
CREATE INDEX idx_chat_messages_session_created ON chat_messages(session_id, created_at);
CREATE INDEX idx_chat_messages_role ON chat_messages(role);
CREATE INDEX idx_documents_kb_status ON documents(kb_id, status);
CREATE INDEX idx_document_chunks_document ON document_chunks(document_id, chunk_index);
CREATE INDEX idx_models_type_status ON models(model_type, status);
CREATE INDEX idx_model_downloads_status ON model_downloads(status, created_at);
CREATE INDEX idx_multimodal_tasks_user_type ON multimodal_tasks(user_id, task_type, created_at DESC);

-- 全文搜索索引
CREATE VIRTUAL TABLE chat_messages_fts USING fts5(
    content,
    content=chat_messages,
    content_rowid=rowid
);

CREATE VIRTUAL TABLE documents_fts USING fts5(
    name,
    content_preview,
    content=documents,
    content_rowid=rowid
);

-- 触发器维护FTS索引
CREATE TRIGGER chat_messages_fts_insert AFTER INSERT ON chat_messages BEGIN
    INSERT INTO chat_messages_fts(rowid, content) VALUES (new.rowid, new.content);
END;

CREATE TRIGGER chat_messages_fts_delete AFTER DELETE ON chat_messages BEGIN
    INSERT INTO chat_messages_fts(chat_messages_fts, rowid, content) VALUES('delete', old.rowid, old.content);
END;

CREATE TRIGGER chat_messages_fts_update AFTER UPDATE ON chat_messages BEGIN
    INSERT INTO chat_messages_fts(chat_messages_fts, rowid, content) VALUES('delete', old.rowid, old.content);
    INSERT INTO chat_messages_fts(rowid, content) VALUES (new.rowid, new.content);
END;
```

### 11.4 ChromaDB 向量数据库设计

#### 11.4.1 向量数据库架构

ChromaDB 用于存储文档向量和实现高效的语义搜索：

```python
# ChromaDB 配置和初始化
import chromadb
from chromadb.config import Settings

# 数据库配置
CHROMA_CONFIG = {
    "chroma_db_impl": "duckdb+parquet",
    "persist_directory": "./data/chroma",
    "anonymized_telemetry": False,
    "allow_reset": True
}

# 初始化客户端
client = chromadb.Client(Settings(**CHROMA_CONFIG))

# Collection 命名规范
# ai_studio_kb_{knowledge_base_id}
# 例如: ai_studio_kb_abc123def456
```

#### 11.4.2 Collection 结构设计

```python
# 知识库 Collection 结构
class KnowledgeBaseCollection:
    def __init__(self, kb_id: str, embedding_model: str = "sentence-transformers/all-MiniLM-L6-v2"):
        self.collection_name = f"ai_studio_kb_{kb_id}"
        self.embedding_model = embedding_model
        self.collection = client.get_or_create_collection(
            name=self.collection_name,
            metadata={
                "kb_id": kb_id,
                "embedding_model": embedding_model,
                "created_at": datetime.utcnow().isoformat(),
                "version": "1.0"
            }
        )

    def add_document_chunks(self, chunks: List[DocumentChunk]):
        """添加文档块到向量数据库"""
        documents = []
        metadatas = []
        ids = []

        for chunk in chunks:
            documents.append(chunk.content)
            metadatas.append({
                "document_id": chunk.document_id,
                "chunk_index": chunk.chunk_index,
                "page_number": chunk.page_number,
                "section_title": chunk.section_title,
                "token_count": chunk.token_count,
                "created_at": chunk.created_at.isoformat(),
                "document_name": chunk.document_name,
                "document_type": chunk.document_type
            })
            ids.append(chunk.vector_id)

        self.collection.add(
            documents=documents,
            metadatas=metadatas,
            ids=ids
        )

    def search_similar(self, query: str, n_results: int = 10, filters: dict = None):
        """语义搜索"""
        where_clause = {}
        if filters:
            if "document_types" in filters:
                where_clause["document_type"] = {"$in": filters["document_types"]}
            if "date_range" in filters:
                where_clause["created_at"] = {
                    "$gte": filters["date_range"]["start"],
                    "$lte": filters["date_range"]["end"]
                }

        results = self.collection.query(
            query_texts=[query],
            n_results=n_results,
            where=where_clause if where_clause else None,
            include=["documents", "metadatas", "distances"]
        )

        return results

    def delete_document(self, document_id: str):
        """删除文档的所有向量"""
        self.collection.delete(
            where={"document_id": document_id}
        )

    def update_chunk(self, chunk_id: str, content: str, metadata: dict):
        """更新文档块"""
        self.collection.update(
            ids=[chunk_id],
            documents=[content],
            metadatas=[metadata]
        )

    def get_collection_stats(self):
        """获取Collection统计信息"""
        return {
            "total_chunks": self.collection.count(),
            "metadata": self.collection.metadata
        }
```

#### 11.4.3 向量搜索优化

```python
# 高级搜索功能
class AdvancedVectorSearch:
    def __init__(self, collection: chromadb.Collection):
        self.collection = collection

    def hybrid_search(self, query: str, keywords: List[str] = None,
                     semantic_weight: float = 0.7, keyword_weight: float = 0.3):
        """混合搜索：语义搜索 + 关键词搜索"""
        # 语义搜索
        semantic_results = self.collection.query(
            query_texts=[query],
            n_results=20,
            include=["documents", "metadatas", "distances"]
        )

        # 关键词过滤
        if keywords:
            filtered_results = []
            for i, doc in enumerate(semantic_results["documents"][0]):
                keyword_score = sum(1 for kw in keywords if kw.lower() in doc.lower()) / len(keywords)
                semantic_score = 1 - semantic_results["distances"][0][i]  # 转换为相似度

                combined_score = semantic_weight * semantic_score + keyword_weight * keyword_score

                filtered_results.append({
                    "document": doc,
                    "metadata": semantic_results["metadatas"][0][i],
                    "score": combined_score,
                    "semantic_score": semantic_score,
                    "keyword_score": keyword_score
                })

            # 按综合得分排序
            filtered_results.sort(key=lambda x: x["score"], reverse=True)
            return filtered_results[:10]

        return semantic_results

    def multi_query_search(self, queries: List[str], aggregation: str = "max"):
        """多查询搜索"""
        all_results = []

        for query in queries:
            results = self.collection.query(
                query_texts=[query],
                n_results=10,
                include=["documents", "metadatas", "distances"]
            )
            all_results.append(results)

        # 聚合结果
        if aggregation == "max":
            # 取最高相似度
            return self._aggregate_max_similarity(all_results)
        elif aggregation == "avg":
            # 取平均相似度
            return self._aggregate_avg_similarity(all_results)

        return all_results[0]  # 默认返回第一个查询的结果

    def contextual_search(self, query: str, context_window: int = 2):
        """上下文搜索：返回匹配块及其前后文"""
        results = self.collection.query(
            query_texts=[query],
            n_results=10,
            include=["documents", "metadatas", "distances"]
        )

        contextual_results = []
        for i, metadata in enumerate(results["metadatas"][0]):
            document_id = metadata["document_id"]
            chunk_index = metadata["chunk_index"]

            # 获取上下文块
            context_chunks = self._get_context_chunks(
                document_id, chunk_index, context_window
            )

            contextual_results.append({
                "main_chunk": results["documents"][0][i],
                "context_chunks": context_chunks,
                "metadata": metadata,
                "score": 1 - results["distances"][0][i]
            })

        return contextual_results

    def _get_context_chunks(self, document_id: str, chunk_index: int, window: int):
        """获取上下文块"""
        context_results = self.collection.get(
            where={
                "document_id": document_id,
                "chunk_index": {
                    "$gte": max(0, chunk_index - window),
                    "$lte": chunk_index + window
                }
            },
            include=["documents", "metadatas"]
        )

        # 按chunk_index排序
        sorted_chunks = sorted(
            zip(context_results["documents"], context_results["metadatas"]),
            key=lambda x: x[1]["chunk_index"]
        )

        return [{"content": doc, "metadata": meta} for doc, meta in sorted_chunks]
```

#### 11.4.4 向量数据库维护

```python
# 数据库维护和优化
class ChromaDBMaintenance:
    def __init__(self, client: chromadb.Client):
        self.client = client

    def backup_collection(self, collection_name: str, backup_path: str):
        """备份Collection"""
        collection = self.client.get_collection(collection_name)

        # 获取所有数据
        all_data = collection.get(include=["documents", "metadatas", "embeddings"])

        # 保存到文件
        backup_data = {
            "collection_name": collection_name,
            "metadata": collection.metadata,
            "data": all_data,
            "backup_time": datetime.utcnow().isoformat()
        }

        with open(backup_path, 'w', encoding='utf-8') as f:
            json.dump(backup_data, f, ensure_ascii=False, indent=2)

    def restore_collection(self, backup_path: str):
        """恢复Collection"""
        with open(backup_path, 'r', encoding='utf-8') as f:
            backup_data = json.load(f)

        collection_name = backup_data["collection_name"]

        # 删除现有Collection（如果存在）
        try:
            self.client.delete_collection(collection_name)
        except:
            pass

        # 创建新Collection
        collection = self.client.create_collection(
            name=collection_name,
            metadata=backup_data["metadata"]
        )

        # 恢复数据
        data = backup_data["data"]
        if data["ids"]:
            collection.add(
                ids=data["ids"],
                documents=data["documents"],
                metadatas=data["metadatas"],
                embeddings=data["embeddings"] if "embeddings" in data else None
            )

    def optimize_collection(self, collection_name: str):
        """优化Collection性能"""
        collection = self.client.get_collection(collection_name)

        # 获取统计信息
        stats = {
            "total_vectors": collection.count(),
            "collection_metadata": collection.metadata
        }

        # 检查重复向量
        duplicates = self._find_duplicate_vectors(collection)
        if duplicates:
            print(f"Found {len(duplicates)} duplicate vectors")
            # 可以选择删除重复项

        return stats

    def _find_duplicate_vectors(self, collection):
        """查找重复向量"""
        # 这里可以实现重复检测逻辑
        # 由于ChromaDB的限制，这个功能需要特殊实现
        return []

    def migrate_collection(self, old_collection: str, new_collection: str,
                          new_embedding_model: str = None):
        """迁移Collection到新的embedding模型"""
        old_coll = self.client.get_collection(old_collection)

        # 获取所有数据
        all_data = old_coll.get(include=["documents", "metadatas"])

        # 创建新Collection
        new_coll = self.client.create_collection(
            name=new_collection,
            metadata={
                **old_coll.metadata,
                "migrated_from": old_collection,
                "migration_time": datetime.utcnow().isoformat(),
                "embedding_model": new_embedding_model or old_coll.metadata.get("embedding_model")
            }
        )

        # 重新生成embeddings（如果需要）
        if new_embedding_model:
            # 这里需要重新计算embeddings
            pass
        else:
            # 直接复制数据
            new_coll.add(
                ids=all_data["ids"],
                documents=all_data["documents"],
                metadatas=all_data["metadatas"]
            )
```

### 11.5 数据库连接和事务管理

```rust
// Rust 数据库连接管理
use sqlx::{SqlitePool, Row};
use std::time::Duration;
use anyhow::Result;

pub struct DatabaseManager {
    pool: SqlitePool,
}

impl DatabaseManager {
    pub async fn new(database_url: &str) -> Result<Self> {
        let pool = SqlitePool::connect_with(
            sqlx::sqlite::SqliteConnectOptions::new()
                .filename(database_url)
                .create_if_missing(true)
                .journal_mode(sqlx::sqlite::SqliteJournalMode::Wal)
                .synchronous(sqlx::sqlite::SqliteSynchronous::Normal)
                .busy_timeout(Duration::from_secs(30))
        ).await?;

        // 运行迁移
        sqlx::migrate!("./migrations").run(&pool).await?;

        Ok(Self { pool })
    }

    pub async fn execute_transaction<F, T>(&self, f: F) -> Result<T>
    where
        F: FnOnce(&mut sqlx::Transaction<sqlx::Sqlite>) -> Result<T> + Send,
        T: Send,
    {
        let mut tx = self.pool.begin().await?;
        let result = f(&mut tx)?;
        tx.commit().await?;
        Ok(result)
    }

    pub fn get_pool(&self) -> &SqlitePool {
        &self.pool
    }
}

// 数据库迁移管理
pub struct MigrationManager {
    pool: SqlitePool,
}

impl MigrationManager {
    pub fn new(pool: SqlitePool) -> Self {
        Self { pool }
    }

    pub async fn run_migrations(&self) -> Result<()> {
        sqlx::migrate!("./migrations").run(&self.pool).await?;
        Ok(())
    }

    pub async fn get_migration_status(&self) -> Result<Vec<MigrationInfo>> {
        let rows = sqlx::query("SELECT version, description, installed_on FROM _sqlx_migrations ORDER BY version")
            .fetch_all(&self.pool)
            .await?;

        let migrations = rows.into_iter().map(|row| MigrationInfo {
            version: row.get("version"),
            description: row.get("description"),
            installed_on: row.get("installed_on"),
        }).collect();

        Ok(migrations)
    }
}

#[derive(Debug)]
pub struct MigrationInfo {
    pub version: i64,
    pub description: String,
    pub installed_on: chrono::DateTime<chrono::Utc>,
}
```

## 12. 完整REST API文档设计

### 12.1 API架构设计

AI Studio REST API 采用 RESTful 设计原则，提供统一的接口规范：

```yaml
# OpenAPI 3.0 规范
openapi: 3.0.3
info:
  title: AI Studio API
  description: AI Studio 桌面应用后端API接口文档
  version: 1.0.0
  contact:
    name: AI Studio Team
    email: <EMAIL>
  license:
    name: MIT
    url: https://opensource.org/licenses/MIT

servers:
  - url: http://localhost:8080/api/v1
    description: 本地开发服务器
  - url: https://api.ai-studio.com/v1
    description: 生产环境服务器

# 全局安全配置
security:
  - BearerAuth: []
  - ApiKeyAuth: []

# 安全方案定义
components:
  securitySchemes:
    BearerAuth:
      type: http
      scheme: bearer
      bearerFormat: JWT
    ApiKeyAuth:
      type: apiKey
      in: header
      name: X-API-Key

  # 通用响应模式
  schemas:
    ApiResponse:
      type: object
      properties:
        code:
          type: integer
          description: 状态码
          example: 200
        message:
          type: string
          description: 响应消息
          example: "Success"
        data:
          type: object
          description: 响应数据
        timestamp:
          type: string
          format: date-time
          description: 时间戳
        request_id:
          type: string
          description: 请求ID
          example: "req_123456789"

    PaginatedResponse:
      allOf:
        - $ref: '#/components/schemas/ApiResponse'
        - type: object
          properties:
            data:
              type: object
              properties:
                items:
                  type: array
                  items: {}
                total:
                  type: integer
                  description: 总数量
                page:
                  type: integer
                  description: 当前页码
                limit:
                  type: integer
                  description: 每页数量
                has_more:
                  type: boolean
                  description: 是否有更多数据

    ErrorResponse:
      type: object
      properties:
        code:
          type: integer
          description: 错误码
        message:
          type: string
          description: 错误消息
        details:
          type: object
          description: 错误详情
        timestamp:
          type: string
          format: date-time
        request_id:
          type: string
```

### 12.2 聊天模块API详细定义

```yaml
# 聊天相关API
paths:
  /chat/sessions:
    get:
      tags:
        - Chat
      summary: 获取聊天会话列表
      description: 分页获取用户的聊天会话列表
      parameters:
        - name: page
          in: query
          description: 页码
          schema:
            type: integer
            minimum: 1
            default: 1
        - name: limit
          in: query
          description: 每页数量
          schema:
            type: integer
            minimum: 1
            maximum: 100
            default: 20
        - name: search
          in: query
          description: 搜索关键词
          schema:
            type: string
        - name: sort
          in: query
          description: 排序字段
          schema:
            type: string
            enum: [created_at, updated_at, title]
            default: updated_at
        - name: order
          in: query
          description: 排序方向
          schema:
            type: string
            enum: [asc, desc]
            default: desc
      responses:
        '200':
          description: 成功获取会话列表
          content:
            application/json:
              schema:
                allOf:
                  - $ref: '#/components/schemas/PaginatedResponse'
                  - type: object
                    properties:
                      data:
                        type: object
                        properties:
                          items:
                            type: array
                            items:
                              $ref: '#/components/schemas/ChatSession'

    post:
      tags:
        - Chat
      summary: 创建新的聊天会话
      description: 创建一个新的聊天会话
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                title:
                  type: string
                  description: 会话标题
                  example: "新的对话"
                model_id:
                  type: string
                  description: 使用的模型ID
                  example: "llama2-7b-chat"
                system_prompt:
                  type: string
                  description: 系统提示词
                  example: "你是一个有用的AI助手"
                config:
                  $ref: '#/components/schemas/ChatConfig'
      responses:
        '201':
          description: 成功创建会话
          content:
            application/json:
              schema:
                allOf:
                  - $ref: '#/components/schemas/ApiResponse'
                  - type: object
                    properties:
                      data:
                        $ref: '#/components/schemas/ChatSession'

  /chat/sessions/{sessionId}:
    get:
      tags:
        - Chat
      summary: 获取指定会话详情
      parameters:
        - name: sessionId
          in: path
          required: true
          schema:
            type: string
      responses:
        '200':
          description: 成功获取会话详情
          content:
            application/json:
              schema:
                allOf:
                  - $ref: '#/components/schemas/ApiResponse'
                  - type: object
                    properties:
                      data:
                        $ref: '#/components/schemas/ChatSession'

    put:
      tags:
        - Chat
      summary: 更新会话信息
      parameters:
        - name: sessionId
          in: path
          required: true
          schema:
            type: string
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                title:
                  type: string
                system_prompt:
                  type: string
                config:
                  $ref: '#/components/schemas/ChatConfig'
      responses:
        '200':
          description: 成功更新会话
          content:
            application/json:
              schema:
                allOf:
                  - $ref: '#/components/schemas/ApiResponse'
                  - type: object
                    properties:
                      data:
                        $ref: '#/components/schemas/ChatSession'

    delete:
      tags:
        - Chat
      summary: 删除会话
      parameters:
        - name: sessionId
          in: path
          required: true
          schema:
            type: string
      responses:
        '200':
          description: 成功删除会话
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ApiResponse'

  /chat/send:
    post:
      tags:
        - Chat
      summary: 发送聊天消息
      description: 向指定会话发送消息并开始AI推理
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required:
                - session_id
                - message
              properties:
                session_id:
                  type: string
                  description: 会话ID
                message:
                  type: string
                  description: 用户消息内容
                attachments:
                  type: array
                  description: 附件列表
                  items:
                    $ref: '#/components/schemas/MessageAttachment'
                model_config:
                  $ref: '#/components/schemas/ChatConfig'
      responses:
        '200':
          description: 成功发送消息
          content:
            application/json:
              schema:
                allOf:
                  - $ref: '#/components/schemas/ApiResponse'
                  - type: object
                    properties:
                      data:
                        type: object
                        properties:
                          message_id:
                            type: string
                            description: 消息ID
                          status:
                            type: string
                            enum: [success, error]
                          stream_url:
                            type: string
                            description: 流式响应URL

  /chat/stream/{messageId}:
    get:
      tags:
        - Chat
      summary: 获取流式响应
      description: 通过Server-Sent Events获取AI响应的流式输出
      parameters:
        - name: messageId
          in: path
          required: true
          schema:
            type: string
      responses:
        '200':
          description: 流式响应
          content:
            text/event-stream:
              schema:
                type: string
                description: SSE事件流
              examples:
                token_event:
                  summary: Token事件
                  value: |
                    data: {"type": "token", "content": "Hello", "metadata": null}

                done_event:
                  summary: 完成事件
                  value: |
                    data: {"type": "done", "content": "", "metadata": {"tokens_used": 150, "response_time": 2.5}}

                error_event:
                  summary: 错误事件
                  value: |
                    data: {"type": "error", "content": "推理失败", "metadata": null}

  /chat/messages/{sessionId}:
    get:
      tags:
        - Chat
      summary: 获取会话消息列表
      parameters:
        - name: sessionId
          in: path
          required: true
          schema:
            type: string
        - name: page
          in: query
          schema:
            type: integer
            default: 1
        - name: limit
          in: query
          schema:
            type: integer
            default: 50
        - name: before
          in: query
          description: 获取指定消息之前的消息
          schema:
            type: string
        - name: after
          in: query
          description: 获取指定消息之后的消息
          schema:
            type: string
      responses:
        '200':
          description: 成功获取消息列表
          content:
            application/json:
              schema:
                allOf:
                  - $ref: '#/components/schemas/PaginatedResponse'
                  - type: object
                    properties:
                      data:
                        type: object
                        properties:
                          items:
                            type: array
                            items:
                              $ref: '#/components/schemas/ChatMessage'

# 数据模型定义
components:
  schemas:
    ChatSession:
      type: object
      properties:
        id:
          type: string
          description: 会话ID
        user_id:
          type: string
          description: 用户ID
        title:
          type: string
          description: 会话标题
        model_id:
          type: string
          description: 使用的模型ID
        system_prompt:
          type: string
          description: 系统提示词
        config:
          $ref: '#/components/schemas/ChatConfig'
        message_count:
          type: integer
          description: 消息数量
        total_tokens:
          type: integer
          description: 总token数
        created_at:
          type: string
          format: date-time
          description: 创建时间
        updated_at:
          type: string
          format: date-time
          description: 更新时间
        is_archived:
          type: boolean
          description: 是否已归档
        is_pinned:
          type: boolean
          description: 是否置顶

    ChatMessage:
      type: object
      properties:
        id:
          type: string
          description: 消息ID
        session_id:
          type: string
          description: 会话ID
        parent_id:
          type: string
          description: 父消息ID（支持消息树）
        role:
          type: string
          enum: [user, assistant, system]
          description: 消息角色
        content:
          type: string
          description: 消息内容
        content_type:
          type: string
          enum: [text, markdown, code]
          description: 内容类型
        attachments:
          type: array
          items:
            $ref: '#/components/schemas/MessageAttachment'
        metadata:
          type: object
          description: 消息元数据
        tokens_used:
          type: integer
          description: 使用的token数
        response_time:
          type: number
          description: 响应时间（秒）
        model_used:
          type: string
          description: 使用的模型
        created_at:
          type: string
          format: date-time
        updated_at:
          type: string
          format: date-time
        is_deleted:
          type: boolean
          description: 是否已删除

    ChatConfig:
      type: object
      properties:
        model_id:
          type: string
          description: 模型ID
        temperature:
          type: number
          minimum: 0
          maximum: 2
          description: 温度参数
          default: 0.7
        max_tokens:
          type: integer
          minimum: 1
          maximum: 8192
          description: 最大token数
          default: 2048
        top_p:
          type: number
          minimum: 0
          maximum: 1
          description: Top-p参数
          default: 0.9
        frequency_penalty:
          type: number
          minimum: -2
          maximum: 2
          description: 频率惩罚
          default: 0
        presence_penalty:
          type: number
          minimum: -2
          maximum: 2
          description: 存在惩罚
          default: 0

    MessageAttachment:
      type: object
      properties:
        id:
          type: string
          description: 附件ID
        type:
          type: string
          enum: [image, document, audio, video]
          description: 附件类型
        url:
          type: string
          description: 附件URL
        name:
          type: string
          description: 附件名称
        size:
          type: integer
          description: 附件大小（字节）
        mime_type:
          type: string
          description: MIME类型
        metadata:
          type: object
          description: 附件元数据
```

### 12.3 知识库模块API详细定义

```yaml
# 知识库相关API
paths:
  /knowledge:
    get:
      tags:
        - Knowledge Base
      summary: 获取知识库列表
      parameters:
        - name: page
          in: query
          schema:
            type: integer
            default: 1
        - name: limit
          in: query
          schema:
            type: integer
            default: 20
        - name: search
          in: query
          description: 搜索知识库名称
          schema:
            type: string
        - name: status
          in: query
          description: 过滤状态
          schema:
            type: string
            enum: [active, processing, error, archived]
      responses:
        '200':
          description: 成功获取知识库列表
          content:
            application/json:
              schema:
                allOf:
                  - $ref: '#/components/schemas/PaginatedResponse'
                  - type: object
                    properties:
                      data:
                        type: object
                        properties:
                          items:
                            type: array
                            items:
                              $ref: '#/components/schemas/KnowledgeBase'

    post:
      tags:
        - Knowledge Base
      summary: 创建知识库
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required:
                - name
              properties:
                name:
                  type: string
                  description: 知识库名称
                description:
                  type: string
                  description: 知识库描述
                embedding_model:
                  type: string
                  description: 嵌入模型
                  default: "sentence-transformers/all-MiniLM-L6-v2"
                chunk_size:
                  type: integer
                  description: 分块大小
                  default: 512
                chunk_overlap:
                  type: integer
                  description: 分块重叠
                  default: 50
                config:
                  type: object
                  description: 额外配置
      responses:
        '201':
          description: 成功创建知识库
          content:
            application/json:
              schema:
                allOf:
                  - $ref: '#/components/schemas/ApiResponse'
                  - type: object
                    properties:
                      data:
                        $ref: '#/components/schemas/KnowledgeBase'

  /knowledge/{kbId}:
    get:
      tags:
        - Knowledge Base
      summary: 获取知识库详情
      parameters:
        - name: kbId
          in: path
          required: true
          schema:
            type: string
      responses:
        '200':
          description: 成功获取知识库详情
          content:
            application/json:
              schema:
                allOf:
                  - $ref: '#/components/schemas/ApiResponse'
                  - type: object
                    properties:
                      data:
                        $ref: '#/components/schemas/KnowledgeBase'

    put:
      tags:
        - Knowledge Base
      summary: 更新知识库
      parameters:
        - name: kbId
          in: path
          required: true
          schema:
            type: string
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                name:
                  type: string
                description:
                  type: string
                embedding_model:
                  type: string
                chunk_size:
                  type: integer
                chunk_overlap:
                  type: integer
                config:
                  type: object
      responses:
        '200':
          description: 成功更新知识库
          content:
            application/json:
              schema:
                allOf:
                  - $ref: '#/components/schemas/ApiResponse'
                  - type: object
                    properties:
                      data:
                        $ref: '#/components/schemas/KnowledgeBase'

    delete:
      tags:
        - Knowledge Base
      summary: 删除知识库
      parameters:
        - name: kbId
          in: path
          required: true
          schema:
            type: string
      responses:
        '200':
          description: 成功删除知识库
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ApiResponse'

  /knowledge/{kbId}/upload:
    post:
      tags:
        - Knowledge Base
      summary: 上传文档到知识库
      parameters:
        - name: kbId
          in: path
          required: true
          schema:
            type: string
      requestBody:
        required: true
        content:
          multipart/form-data:
            schema:
              type: object
              properties:
                files:
                  type: array
                  items:
                    type: string
                    format: binary
                  description: 上传的文件
                auto_process:
                  type: boolean
                  description: 是否自动处理
                  default: true
                processing_config:
                  type: object
                  description: 处理配置
                  properties:
                    extract_images:
                      type: boolean
                      default: false
                    extract_tables:
                      type: boolean
                      default: true
                    ocr_enabled:
                      type: boolean
                      default: false
      responses:
        '200':
          description: 成功上传文档
          content:
            application/json:
              schema:
                allOf:
                  - $ref: '#/components/schemas/ApiResponse'
                  - type: object
                    properties:
                      data:
                        type: object
                        properties:
                          documents:
                            type: array
                            items:
                              $ref: '#/components/schemas/Document'
                          upload_id:
                            type: string
                            description: 上传任务ID

  /knowledge/{kbId}/documents:
    get:
      tags:
        - Knowledge Base
      summary: 获取知识库文档列表
      parameters:
        - name: kbId
          in: path
          required: true
          schema:
            type: string
        - name: page
          in: query
          schema:
            type: integer
            default: 1
        - name: limit
          in: query
          schema:
            type: integer
            default: 20
        - name: status
          in: query
          description: 文档状态过滤
          schema:
            type: string
            enum: [pending, processing, completed, failed, archived]
        - name: type
          in: query
          description: 文档类型过滤
          schema:
            type: string
            enum: [pdf, docx, txt, md, html]
        - name: search
          in: query
          description: 搜索文档名称
          schema:
            type: string
      responses:
        '200':
          description: 成功获取文档列表
          content:
            application/json:
              schema:
                allOf:
                  - $ref: '#/components/schemas/PaginatedResponse'
                  - type: object
                    properties:
                      data:
                        type: object
                        properties:
                          items:
                            type: array
                            items:
                              $ref: '#/components/schemas/Document'

  /knowledge/{kbId}/documents/{docId}:
    get:
      tags:
        - Knowledge Base
      summary: 获取文档详情
      parameters:
        - name: kbId
          in: path
          required: true
          schema:
            type: string
        - name: docId
          in: path
          required: true
          schema:
            type: string
      responses:
        '200':
          description: 成功获取文档详情
          content:
            application/json:
              schema:
                allOf:
                  - $ref: '#/components/schemas/ApiResponse'
                  - type: object
                    properties:
                      data:
                        $ref: '#/components/schemas/DocumentDetail'

    delete:
      tags:
        - Knowledge Base
      summary: 删除文档
      parameters:
        - name: kbId
          in: path
          required: true
          schema:
            type: string
        - name: docId
          in: path
          required: true
          schema:
            type: string
      responses:
        '200':
          description: 成功删除文档
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ApiResponse'

  /knowledge/{kbId}/search:
    post:
      tags:
        - Knowledge Base
      summary: 语义搜索
      parameters:
        - name: kbId
          in: path
          required: true
          schema:
            type: string
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required:
                - query
              properties:
                query:
                  type: string
                  description: 搜索查询
                limit:
                  type: integer
                  description: 返回结果数量
                  default: 10
                  minimum: 1
                  maximum: 50
                threshold:
                  type: number
                  description: 相似度阈值
                  default: 0.7
                  minimum: 0
                  maximum: 1
                filters:
                  type: object
                  description: 搜索过滤器
                  properties:
                    document_types:
                      type: array
                      items:
                        type: string
                      description: 文档类型过滤
                    date_range:
                      type: object
                      properties:
                        start:
                          type: string
                          format: date-time
                        end:
                          type: string
                          format: date-time
                    document_ids:
                      type: array
                      items:
                        type: string
                      description: 指定文档ID
                search_type:
                  type: string
                  enum: [semantic, hybrid, keyword]
                  description: 搜索类型
                  default: semantic
                include_context:
                  type: boolean
                  description: 是否包含上下文
                  default: false
                context_window:
                  type: integer
                  description: 上下文窗口大小
                  default: 2
      responses:
        '200':
          description: 成功执行搜索
          content:
            application/json:
              schema:
                allOf:
                  - $ref: '#/components/schemas/ApiResponse'
                  - type: object
                    properties:
                      data:
                        type: object
                        properties:
                          results:
                            type: array
                            items:
                              $ref: '#/components/schemas/SearchResult'
                          total:
                            type: integer
                            description: 总结果数
                          query_time:
                            type: number
                            description: 查询耗时（秒）
                          query_id:
                            type: string
                            description: 查询ID

# 知识库相关数据模型
components:
  schemas:
    KnowledgeBase:
      type: object
      properties:
        id:
          type: string
          description: 知识库ID
        user_id:
          type: string
          description: 用户ID
        name:
          type: string
          description: 知识库名称
        description:
          type: string
          description: 知识库描述
        embedding_model:
          type: string
          description: 嵌入模型
        chunk_size:
          type: integer
          description: 分块大小
        chunk_overlap:
          type: integer
          description: 分块重叠
        document_count:
          type: integer
          description: 文档数量
        total_chunks:
          type: integer
          description: 总块数
        total_size:
          type: integer
          description: 总大小（字节）
        status:
          type: string
          enum: [active, processing, error, archived]
          description: 状态
        config:
          type: object
          description: 配置信息
        created_at:
          type: string
          format: date-time
        updated_at:
          type: string
          format: date-time
        last_indexed_at:
          type: string
          format: date-time
          description: 最后索引时间

    Document:
      type: object
      properties:
        id:
          type: string
          description: 文档ID
        kb_id:
          type: string
          description: 知识库ID
        name:
          type: string
          description: 文档名称
        original_name:
          type: string
          description: 原始文件名
        file_type:
          type: string
          description: 文件类型
        mime_type:
          type: string
          description: MIME类型
        file_size:
          type: integer
          description: 文件大小
        file_path:
          type: string
          description: 文件路径
        content_preview:
          type: string
          description: 内容预览
        page_count:
          type: integer
          description: 页数
        word_count:
          type: integer
          description: 字数
        language:
          type: string
          description: 文档语言
        status:
          type: string
          enum: [pending, processing, completed, failed, archived]
          description: 处理状态
        processing_progress:
          type: number
          description: 处理进度
        error_message:
          type: string
          description: 错误信息
        chunks_count:
          type: integer
          description: 分块数量
        metadata:
          type: object
          description: 文档元数据
        created_at:
          type: string
          format: date-time
        updated_at:
          type: string
          format: date-time
        processed_at:
          type: string
          format: date-time

    DocumentDetail:
      allOf:
        - $ref: '#/components/schemas/Document'
        - type: object
          properties:
            chunks:
              type: array
              items:
                $ref: '#/components/schemas/DocumentChunk'
            processing_log:
              type: array
              items:
                type: object
                properties:
                  timestamp:
                    type: string
                    format: date-time
                  level:
                    type: string
                    enum: [info, warning, error]
                  message:
                    type: string

    DocumentChunk:
      type: object
      properties:
        id:
          type: string
          description: 块ID
        document_id:
          type: string
          description: 文档ID
        chunk_index:
          type: integer
          description: 块索引
        content:
          type: string
          description: 块内容
        token_count:
          type: integer
          description: Token数量
        page_number:
          type: integer
          description: 页码
        section_title:
          type: string
          description: 章节标题
        metadata:
          type: object
          description: 块元数据
        created_at:
          type: string
          format: date-time

    SearchResult:
      type: object
      properties:
        document_id:
          type: string
          description: 文档ID
        document_name:
          type: string
          description: 文档名称
        chunk_id:
          type: string
          description: 块ID
        content:
          type: string
          description: 匹配内容
        score:
          type: number
          description: 相似度分数
        metadata:
          type: object
          properties:
            page_number:
              type: integer
            chunk_index:
              type: integer
            section_title:
              type: string
            document_type:
              type: string
        context:
          type: object
          description: 上下文信息
          properties:
            before:
              type: array
              items:
                type: string
            after:
              type: array
              items:
                type: string
        highlights:
          type: array
          items:
            type: object
            properties:
              start:
                type: integer
              end:
                type: integer
              text:
                type: string
```

## 13. 性能优化方案

### 13.1 前端性能优化

#### 13.1.1 代码分割和懒加载

```typescript
// 路由级别的代码分割
const routes = [
  {
    path: '/chat',
    name: 'Chat',
    component: () => import('@/views/Chat/index.vue'),
    meta: { title: '聊天' }
  },
  {
    path: '/knowledge',
    name: 'Knowledge',
    component: () => import('@/views/Knowledge/index.vue'),
    meta: { title: '知识库' }
  },
  {
    path: '/models',
    name: 'Models',
    component: () => import('@/views/Models/index.vue'),
    meta: { title: '模型管理' }
  }
]

// 组件级别的懒加载
const AsyncComponent = defineAsyncComponent({
  loader: () => import('@/components/HeavyComponent.vue'),
  loadingComponent: LoadingSpinner,
  errorComponent: ErrorComponent,
  delay: 200,
  timeout: 3000
})

// 动态导入优化
const loadModule = async (moduleName: string) => {
  const modules = {
    'chat': () => import('@/modules/chat'),
    'knowledge': () => import('@/modules/knowledge'),
    'models': () => import('@/modules/models')
  }

  if (modules[moduleName]) {
    return await modules[moduleName]()
  }

  throw new Error(`Module ${moduleName} not found`)
}
```

#### 13.1.2 虚拟滚动优化

```vue
<!-- 大列表虚拟滚动 -->
<template>
  <div class="virtual-list-container">
    <VirtualList
      :items="messages"
      :item-height="estimatedItemHeight"
      :container-height="containerHeight"
      :buffer-size="5"
      @scroll="handleScroll"
    >
      <template #default="{ item, index }">
        <ChatMessage
          :key="item.id"
          :message="item"
          :index="index"
          @height-change="updateItemHeight"
        />
      </template>
    </VirtualList>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted } from 'vue'
import VirtualList from '@/components/common/VirtualList.vue'

const messages = ref<ChatMessage[]>([])
const containerHeight = ref(600)
const estimatedItemHeight = ref(80)
const itemHeights = ref<Map<string, number>>(new Map())

// 动态高度计算
const updateItemHeight = (itemId: string, height: number) => {
  itemHeights.value.set(itemId, height)

  // 更新平均高度估算
  const heights = Array.from(itemHeights.value.values())
  estimatedItemHeight.value = heights.reduce((sum, h) => sum + h, 0) / heights.length
}

// 滚动优化
const handleScroll = debounce((scrollTop: number) => {
  // 处理滚动事件
  console.log('Scrolled to:', scrollTop)
}, 16) // 60fps

// 内存管理
const cleanupOldItems = () => {
  if (messages.value.length > 1000) {
    // 保留最近的1000条消息
    messages.value = messages.value.slice(-1000)

    // 清理对应的高度缓存
    const keepIds = new Set(messages.value.map(m => m.id))
    for (const [id] of itemHeights.value) {
      if (!keepIds.has(id)) {
        itemHeights.value.delete(id)
      }
    }
  }
}
</script>
```

#### 13.1.3 状态管理优化

```typescript
// Pinia Store 性能优化
import { defineStore } from 'pinia'
import { ref, computed, shallowRef } from 'vue'

export const useChatStore = defineStore('chat', () => {
  // 使用 shallowRef 减少深度响应式开销
  const messages = shallowRef<ChatMessage[]>([])
  const sessions = shallowRef<ChatSession[]>([])

  // 计算属性缓存
  const messagesBySession = computed(() => {
    const map = new Map<string, ChatMessage[]>()
    for (const message of messages.value) {
      if (!map.has(message.session_id)) {
        map.set(message.session_id, [])
      }
      map.get(message.session_id)!.push(message)
    }
    return map
  })

  // 批量更新优化
  const batchUpdateMessages = (updates: Array<{id: string, changes: Partial<ChatMessage>}>) => {
    const newMessages = [...messages.value]

    for (const update of updates) {
      const index = newMessages.findIndex(m => m.id === update.id)
      if (index !== -1) {
        newMessages[index] = { ...newMessages[index], ...update.changes }
      }
    }

    messages.value = newMessages
  }

  // 内存清理
  const cleanup = () => {
    // 清理超过限制的消息
    if (messages.value.length > 10000) {
      messages.value = messages.value.slice(-5000)
    }

    // 清理无效会话
    const validSessionIds = new Set(sessions.value.map(s => s.id))
    messages.value = messages.value.filter(m => validSessionIds.has(m.session_id))
  }

  return {
    messages,
    sessions,
    messagesBySession,
    batchUpdateMessages,
    cleanup
  }
})

// 防抖和节流工具
export const usePerformanceUtils = () => {
  const debounce = <T extends (...args: any[]) => any>(
    func: T,
    wait: number
  ): ((...args: Parameters<T>) => void) => {
    let timeout: NodeJS.Timeout

    return (...args: Parameters<T>) => {
      clearTimeout(timeout)
      timeout = setTimeout(() => func(...args), wait)
    }
  }

  const throttle = <T extends (...args: any[]) => any>(
    func: T,
    limit: number
  ): ((...args: Parameters<T>) => void) => {
    let inThrottle: boolean

    return (...args: Parameters<T>) => {
      if (!inThrottle) {
        func(...args)
        inThrottle = true
        setTimeout(() => inThrottle = false, limit)
      }
    }
  }

  const requestIdleCallback = (callback: () => void) => {
    if ('requestIdleCallback' in window) {
      window.requestIdleCallback(callback)
    } else {
      setTimeout(callback, 1)
    }
  }

  return {
    debounce,
    throttle,
    requestIdleCallback
  }
}
```

#### 13.1.4 资源加载优化

```typescript
// 图片懒加载
export const useLazyImage = () => {
  const imageCache = new Map<string, HTMLImageElement>()

  const loadImage = (src: string): Promise<HTMLImageElement> => {
    if (imageCache.has(src)) {
      return Promise.resolve(imageCache.get(src)!)
    }

    return new Promise((resolve, reject) => {
      const img = new Image()
      img.onload = () => {
        imageCache.set(src, img)
        resolve(img)
      }
      img.onerror = reject
      img.src = src
    })
  }

  const preloadImages = async (urls: string[]) => {
    const promises = urls.map(url => loadImage(url))
    await Promise.allSettled(promises)
  }

  return {
    loadImage,
    preloadImages
  }
}

// 字体预加载
const preloadFonts = () => {
  const fonts = [
    'JetBrains Mono',
    'SF Pro Display',
    'Microsoft YaHei'
  ]

  fonts.forEach(font => {
    const link = document.createElement('link')
    link.rel = 'preload'
    link.as = 'font'
    link.type = 'font/woff2'
    link.crossOrigin = 'anonymous'
    link.href = `/fonts/${font.replace(/\s+/g, '-').toLowerCase()}.woff2`
    document.head.appendChild(link)
  })
}

// 资源预取
const prefetchResources = () => {
  const resources = [
    '/api/models',
    '/api/knowledge',
    '/icons/sprite.svg'
  ]

  resources.forEach(resource => {
    const link = document.createElement('link')
    link.rel = 'prefetch'
    link.href = resource
    document.head.appendChild(link)
  })
}
```

### 13.2 后端性能优化

#### 13.2.1 数据库优化

```rust
// 连接池优化
use sqlx::{SqlitePool, Row};
use std::time::Duration;

pub struct OptimizedDatabase {
    pool: SqlitePool,
    read_pool: SqlitePool,
}

impl OptimizedDatabase {
    pub async fn new(database_url: &str) -> Result<Self, sqlx::Error> {
        // 写连接池
        let pool = SqlitePool::connect_with(
            sqlx::sqlite::SqliteConnectOptions::new()
                .filename(database_url)
                .create_if_missing(true)
                .journal_mode(sqlx::sqlite::SqliteJournalMode::Wal)
                .synchronous(sqlx::sqlite::SqliteSynchronous::Normal)
                .busy_timeout(Duration::from_secs(30))
                .pragma("cache_size", "-64000") // 64MB cache
                .pragma("temp_store", "memory")
                .pragma("mmap_size", "268435456") // 256MB mmap
        ).await?;

        // 只读连接池（用于查询）
        let read_pool = SqlitePool::connect_with(
            sqlx::sqlite::SqliteConnectOptions::new()
                .filename(database_url)
                .read_only(true)
                .busy_timeout(Duration::from_secs(10))
                .pragma("cache_size", "-32000") // 32MB cache
                .pragma("temp_store", "memory")
        ).await?;

        Ok(Self { pool, read_pool })
    }

    // 批量插入优化
    pub async fn batch_insert_messages(&self, messages: Vec<ChatMessage>) -> Result<(), sqlx::Error> {
        let mut tx = self.pool.begin().await?;

        // 使用预编译语句
        let query = sqlx::query(
            "INSERT INTO chat_messages (id, session_id, role, content, created_at) VALUES (?, ?, ?, ?, ?)"
        );

        for chunk in messages.chunks(100) {
            for message in chunk {
                query
                    .bind(&message.id)
                    .bind(&message.session_id)
                    .bind(&message.role)
                    .bind(&message.content)
                    .bind(&message.created_at)
                    .execute(&mut *tx)
                    .await?;
            }
        }

        tx.commit().await?;
        Ok(())
    }

    // 查询优化
    pub async fn get_messages_optimized(&self, session_id: &str, limit: i32, offset: i32) -> Result<Vec<ChatMessage>, sqlx::Error> {
        // 使用只读连接池
        let rows = sqlx::query(
            "SELECT id, session_id, role, content, created_at
             FROM chat_messages
             WHERE session_id = ?
             ORDER BY created_at DESC
             LIMIT ? OFFSET ?"
        )
        .bind(session_id)
        .bind(limit)
        .bind(offset)
        .fetch_all(&self.read_pool)
        .await?;

        let messages = rows.into_iter().map(|row| ChatMessage {
            id: row.get("id"),
            session_id: row.get("session_id"),
            role: row.get("role"),
            content: row.get("content"),
            created_at: row.get("created_at"),
            ..Default::default()
        }).collect();

        Ok(messages)
    }
}

// 查询缓存
use moka::future::Cache;
use std::hash::{Hash, Hasher};

#[derive(Clone, Debug)]
pub struct QueryKey {
    query: String,
    params: Vec<String>,
}

impl Hash for QueryKey {
    fn hash<H: Hasher>(&self, state: &mut H) {
        self.query.hash(state);
        self.params.hash(state);
    }
}

impl PartialEq for QueryKey {
    fn eq(&self, other: &Self) -> bool {
        self.query == other.query && self.params == other.params
    }
}

impl Eq for QueryKey {}

pub struct CachedDatabase {
    db: OptimizedDatabase,
    cache: Cache<QueryKey, serde_json::Value>,
}

impl CachedDatabase {
    pub fn new(db: OptimizedDatabase) -> Self {
        let cache = Cache::builder()
            .max_capacity(1000)
            .time_to_live(Duration::from_secs(300)) // 5分钟缓存
            .time_to_idle(Duration::from_secs(60))  // 1分钟空闲过期
            .build();

        Self { db, cache }
    }

    pub async fn get_cached_query<T>(&self, key: QueryKey, query_fn: impl Future<Output = Result<T, sqlx::Error>>) -> Result<T, sqlx::Error>
    where
        T: serde::Serialize + serde::de::DeserializeOwned + Clone,
    {
        if let Some(cached) = self.cache.get(&key).await {
            if let Ok(result) = serde_json::from_value(cached) {
                return Ok(result);
            }
        }

        let result = query_fn.await?;

        if let Ok(json_value) = serde_json::to_value(&result) {
            self.cache.insert(key, json_value).await;
        }

        Ok(result)
    }
}
```

#### 13.2.2 AI推理优化

```rust
// 模型加载和推理优化
use candle_core::{Device, Tensor};
use candle_nn::VarBuilder;
use std::sync::Arc;
use tokio::sync::{RwLock, Semaphore};

pub struct OptimizedInferenceEngine {
    models: Arc<RwLock<HashMap<String, Arc<dyn Model>>>>,
    device: Device,
    inference_semaphore: Arc<Semaphore>, // 限制并发推理数量
    memory_pool: Arc<MemoryPool>,
}

impl OptimizedInferenceEngine {
    pub fn new(max_concurrent_inferences: usize) -> Result<Self, Box<dyn std::error::Error>> {
        let device = if candle_core::utils::cuda_is_available() {
            Device::new_cuda(0)?
        } else {
            Device::Cpu
        };

        Ok(Self {
            models: Arc::new(RwLock::new(HashMap::new())),
            device,
            inference_semaphore: Arc::new(Semaphore::new(max_concurrent_inferences)),
            memory_pool: Arc::new(MemoryPool::new()),
        })
    }

    // 模型预热
    pub async fn warmup_model(&self, model_id: &str) -> Result<(), Box<dyn std::error::Error>> {
        let models = self.models.read().await;
        if let Some(model) = models.get(model_id) {
            // 执行一次虚拟推理来预热模型
            let dummy_input = Tensor::zeros((1, 1), candle_core::DType::U32, &self.device)?;
            let _ = model.forward(&dummy_input).await;
        }
        Ok(())
    }

    // 批量推理
    pub async fn batch_inference(
        &self,
        model_id: &str,
        inputs: Vec<String>,
        max_batch_size: usize,
    ) -> Result<Vec<String>, Box<dyn std::error::Error>> {
        let _permit = self.inference_semaphore.acquire().await?;

        let models = self.models.read().await;
        let model = models.get(model_id)
            .ok_or("Model not found")?;

        let mut results = Vec::new();

        for batch in inputs.chunks(max_batch_size) {
            let batch_results = self.process_batch(model.clone(), batch.to_vec()).await?;
            results.extend(batch_results);
        }

        Ok(results)
    }

    async fn process_batch(
        &self,
        model: Arc<dyn Model>,
        batch: Vec<String>,
    ) -> Result<Vec<String>, Box<dyn std::error::Error>> {
        // 批量tokenization
        let tokenized = self.batch_tokenize(&batch).await?;

        // 批量推理
        let outputs = model.batch_forward(tokenized).await?;

        // 批量解码
        let decoded = self.batch_decode(outputs).await?;

        Ok(decoded)
    }

    // 内存管理
    async fn cleanup_memory(&self) {
        self.memory_pool.cleanup().await;

        // 强制垃圾回收（如果需要）
        if self.memory_pool.usage_ratio() > 0.8 {
            self.force_gc().await;
        }
    }

    async fn force_gc(&self) {
        // 实现内存清理逻辑
        // 可能包括卸载不常用的模型、清理缓存等
    }
}

// 内存池管理
pub struct MemoryPool {
    allocated: Arc<RwLock<usize>>,
    max_memory: usize,
    buffers: Arc<RwLock<Vec<Vec<u8>>>>,
}

impl MemoryPool {
    pub fn new() -> Self {
        Self {
            allocated: Arc::new(RwLock::new(0)),
            max_memory: 8 * 1024 * 1024 * 1024, // 8GB
            buffers: Arc::new(RwLock::new(Vec::new())),
        }
    }

    pub async fn allocate(&self, size: usize) -> Option<Vec<u8>> {
        let mut allocated = self.allocated.write().await;

        if *allocated + size > self.max_memory {
            return None;
        }

        *allocated += size;
        Some(vec![0; size])
    }

    pub async fn deallocate(&self, buffer: Vec<u8>) {
        let size = buffer.len();
        let mut allocated = self.allocated.write().await;
        *allocated = allocated.saturating_sub(size);

        // 可以选择回收buffer到池中
        let mut buffers = self.buffers.write().await;
        if buffers.len() < 100 { // 限制池大小
            buffers.push(buffer);
        }
    }

    pub async fn usage_ratio(&self) -> f64 {
        let allocated = *self.allocated.read().await;
        allocated as f64 / self.max_memory as f64
    }

    pub async fn cleanup(&self) {
        let mut buffers = self.buffers.write().await;
        buffers.clear();
    }
}
```

## 14. 安全设计方案

### 14.1 数据安全

#### 14.1.1 敏感数据加密

```rust
// 加密服务实现
use aes_gcm::{Aes256Gcm, Key, Nonce, aead::{Aead, NewAead}};
use ring::{rand, pbkdf2, digest};
use std::num::NonZeroU32;

pub struct EncryptionService {
    cipher: Aes256Gcm,
    salt: [u8; 32],
}

impl EncryptionService {
    pub fn new(password: &str) -> Result<Self, Box<dyn std::error::Error>> {
        // 生成随机盐值
        let salt = rand::SystemRandom::new();
        let mut salt_bytes = [0u8; 32];
        salt.fill(&mut salt_bytes)?;

        // 使用PBKDF2派生密钥
        let mut key_bytes = [0u8; 32];
        pbkdf2::derive(
            pbkdf2::PBKDF2_HMAC_SHA256,
            NonZeroU32::new(100_000).unwrap(),
            &salt_bytes,
            password.as_bytes(),
            &mut key_bytes,
        );

        let key = Key::from_slice(&key_bytes);
        let cipher = Aes256Gcm::new(key);

        Ok(Self {
            cipher,
            salt: salt_bytes,
        })
    }

    pub fn encrypt(&self, plaintext: &str) -> Result<Vec<u8>, Box<dyn std::error::Error>> {
        // 生成随机nonce
        let nonce_bytes = rand::SystemRandom::new();
        let mut nonce_array = [0u8; 12];
        nonce_bytes.fill(&mut nonce_array)?;
        let nonce = Nonce::from_slice(&nonce_array);

        // 加密数据
        let ciphertext = self.cipher.encrypt(nonce, plaintext.as_bytes())?;

        // 组合nonce和密文
        let mut result = Vec::new();
        result.extend_from_slice(&nonce_array);
        result.extend_from_slice(&ciphertext);

        Ok(result)
    }

    pub fn decrypt(&self, encrypted_data: &[u8]) -> Result<String, Box<dyn std::error::Error>> {
        if encrypted_data.len() < 12 {
            return Err("Invalid encrypted data".into());
        }

        // 分离nonce和密文
        let (nonce_bytes, ciphertext) = encrypted_data.split_at(12);
        let nonce = Nonce::from_slice(nonce_bytes);

        // 解密数据
        let plaintext = self.cipher.decrypt(nonce, ciphertext)?;

        Ok(String::from_utf8(plaintext)?)
    }
}

// API密钥安全存储
#[derive(Debug, Clone)]
pub struct SecureApiKey {
    pub id: String,
    pub name: String,
    pub provider: String,
    pub encrypted_key: Vec<u8>,
    pub created_at: chrono::DateTime<chrono::Utc>,
    pub last_used_at: Option<chrono::DateTime<chrono::Utc>>,
    pub is_active: bool,
}

impl SecureApiKey {
    pub fn new(
        name: String,
        provider: String,
        api_key: &str,
        encryption_service: &EncryptionService,
    ) -> Result<Self, Box<dyn std::error::Error>> {
        let encrypted_key = encryption_service.encrypt(api_key)?;

        Ok(Self {
            id: uuid::Uuid::new_v4().to_string(),
            name,
            provider,
            encrypted_key,
            created_at: chrono::Utc::now(),
            last_used_at: None,
            is_active: true,
        })
    }

    pub fn decrypt_key(&self, encryption_service: &EncryptionService) -> Result<String, Box<dyn std::error::Error>> {
        encryption_service.decrypt(&self.encrypted_key)
    }
}

// 数据库字段加密
pub trait EncryptedField {
    fn encrypt_before_save(&mut self, encryption_service: &EncryptionService) -> Result<(), Box<dyn std::error::Error>>;
    fn decrypt_after_load(&mut self, encryption_service: &EncryptionService) -> Result<(), Box<dyn std::error::Error>>;
}

#[derive(Debug, Clone)]
pub struct EncryptedUserSettings {
    pub user_id: String,
    pub encrypted_data: Vec<u8>,
    pub settings_hash: String, // 用于完整性验证
}

impl EncryptedUserSettings {
    pub fn new(
        user_id: String,
        settings: &serde_json::Value,
        encryption_service: &EncryptionService,
    ) -> Result<Self, Box<dyn std::error::Error>> {
        let json_str = serde_json::to_string(settings)?;
        let encrypted_data = encryption_service.encrypt(&json_str)?;

        // 计算哈希用于完整性验证
        let settings_hash = digest::digest(&digest::SHA256, &encrypted_data);
        let hash_hex = hex::encode(settings_hash.as_ref());

        Ok(Self {
            user_id,
            encrypted_data,
            settings_hash: hash_hex,
        })
    }

    pub fn decrypt_settings(&self, encryption_service: &EncryptionService) -> Result<serde_json::Value, Box<dyn std::error::Error>> {
        // 验证完整性
        let current_hash = digest::digest(&digest::SHA256, &self.encrypted_data);
        let current_hash_hex = hex::encode(current_hash.as_ref());

        if current_hash_hex != self.settings_hash {
            return Err("Data integrity check failed".into());
        }

        let decrypted_str = encryption_service.decrypt(&self.encrypted_data)?;
        let settings: serde_json::Value = serde_json::from_str(&decrypted_str)?;

        Ok(settings)
    }
}
```

#### 14.1.2 文件安全处理

```rust
// 文件上传安全检查
use std::path::Path;
use std::fs;
use mime_guess;

pub struct FileSecurityChecker {
    allowed_mime_types: Vec<String>,
    max_file_size: u64,
    scan_enabled: bool,
}

impl FileSecurityChecker {
    pub fn new() -> Self {
        Self {
            allowed_mime_types: vec![
                "application/pdf".to_string(),
                "application/vnd.openxmlformats-officedocument.wordprocessingml.document".to_string(),
                "text/plain".to_string(),
                "text/markdown".to_string(),
                "image/jpeg".to_string(),
                "image/png".to_string(),
                "audio/wav".to_string(),
                "audio/mp3".to_string(),
            ],
            max_file_size: 100 * 1024 * 1024, // 100MB
            scan_enabled: true,
        }
    }

    pub async fn validate_file(&self, file_path: &Path) -> Result<FileValidationResult, Box<dyn std::error::Error>> {
        let mut result = FileValidationResult {
            is_valid: true,
            issues: Vec::new(),
            file_info: FileInfo::default(),
        };

        // 检查文件大小
        let metadata = fs::metadata(file_path)?;
        let file_size = metadata.len();

        if file_size > self.max_file_size {
            result.is_valid = false;
            result.issues.push(format!("File size {} exceeds maximum allowed size {}", file_size, self.max_file_size));
        }

        // 检查MIME类型
        let mime_type = mime_guess::from_path(file_path).first_or_octet_stream();
        let mime_str = mime_type.to_string();

        if !self.allowed_mime_types.contains(&mime_str) {
            result.is_valid = false;
            result.issues.push(format!("MIME type {} is not allowed", mime_str));
        }

        // 文件头检查（魔数验证）
        if !self.validate_file_header(file_path, &mime_str).await? {
            result.is_valid = false;
            result.issues.push("File header does not match declared type".to_string());
        }

        // 恶意软件扫描
        if self.scan_enabled {
            if let Some(scan_result) = self.scan_for_malware(file_path).await? {
                result.is_valid = false;
                result.issues.push(format!("Security scan failed: {}", scan_result));
            }
        }

        result.file_info = FileInfo {
            size: file_size,
            mime_type: mime_str,
            hash: self.calculate_file_hash(file_path).await?,
        };

        Ok(result)
    }

    async fn validate_file_header(&self, file_path: &Path, expected_mime: &str) -> Result<bool, Box<dyn std::error::Error>> {
        let mut file = fs::File::open(file_path)?;
        let mut header = [0u8; 16];
        use std::io::Read;
        file.read_exact(&mut header)?;

        match expected_mime {
            "application/pdf" => Ok(header.starts_with(b"%PDF")),
            "image/jpeg" => Ok(header.starts_with(&[0xFF, 0xD8, 0xFF])),
            "image/png" => Ok(header.starts_with(&[0x89, 0x50, 0x4E, 0x47, 0x0D, 0x0A, 0x1A, 0x0A])),
            _ => Ok(true), // 对于其他类型，暂时通过
        }
    }

    async fn scan_for_malware(&self, file_path: &Path) -> Result<Option<String>, Box<dyn std::error::Error>> {
        // 这里可以集成ClamAV或其他反病毒引擎
        // 简化实现：检查文件名中的可疑模式
        let file_name = file_path.file_name()
            .and_then(|n| n.to_str())
            .unwrap_or("");

        let suspicious_patterns = [
            ".exe", ".bat", ".cmd", ".scr", ".vbs", ".js", ".jar"
        ];

        for pattern in &suspicious_patterns {
            if file_name.to_lowercase().contains(pattern) {
                return Ok(Some(format!("Suspicious file extension: {}", pattern)));
            }
        }

        Ok(None)
    }

    async fn calculate_file_hash(&self, file_path: &Path) -> Result<String, Box<dyn std::error::Error>> {
        let content = fs::read(file_path)?;
        let hash = digest::digest(&digest::SHA256, &content);
        Ok(hex::encode(hash.as_ref()))
    }
}

#[derive(Debug, Default)]
pub struct FileValidationResult {
    pub is_valid: bool,
    pub issues: Vec<String>,
    pub file_info: FileInfo,
}

#[derive(Debug, Default)]
pub struct FileInfo {
    pub size: u64,
    pub mime_type: String,
    pub hash: String,
}

// 安全文件存储
pub struct SecureFileStorage {
    base_path: PathBuf,
    encryption_service: EncryptionService,
}

impl SecureFileStorage {
    pub fn new(base_path: PathBuf, encryption_service: EncryptionService) -> Self {
        Self {
            base_path,
            encryption_service,
        }
    }

    pub async fn store_file(&self, file_data: &[u8], file_name: &str) -> Result<String, Box<dyn std::error::Error>> {
        // 生成安全的文件名
        let safe_name = self.generate_safe_filename(file_name);
        let file_path = self.base_path.join(&safe_name);

        // 加密文件内容
        let encrypted_data = self.encryption_service.encrypt(&base64::encode(file_data))?;

        // 写入文件
        fs::write(&file_path, encrypted_data)?;

        // 设置安全权限
        #[cfg(unix)]
        {
            use std::os::unix::fs::PermissionsExt;
            let mut perms = fs::metadata(&file_path)?.permissions();
            perms.set_mode(0o600); // 只有所有者可读写
            fs::set_permissions(&file_path, perms)?;
        }

        Ok(safe_name)
    }

    pub async fn retrieve_file(&self, file_name: &str) -> Result<Vec<u8>, Box<dyn std::error::Error>> {
        let file_path = self.base_path.join(file_name);

        // 读取加密文件
        let encrypted_data = fs::read(&file_path)?;

        // 解密内容
        let decrypted_base64 = self.encryption_service.decrypt(&encrypted_data)?;
        let file_data = base64::decode(decrypted_base64)?;

        Ok(file_data)
    }

    fn generate_safe_filename(&self, original_name: &str) -> String {
        // 移除危险字符
        let safe_chars: String = original_name
            .chars()
            .filter(|c| c.is_alphanumeric() || *c == '.' || *c == '-' || *c == '_')
            .collect();

        // 添加时间戳和随机后缀
        let timestamp = chrono::Utc::now().timestamp();
        let random_suffix: String = (0..8)
            .map(|_| fastrand::alphanumeric())
            .collect();

        format!("{}_{}{}", timestamp, random_suffix, safe_chars)
    }
}
```

### 14.2 网络安全

#### 14.2.1 P2P通信安全

```rust
// P2P安全通信
use tokio_tungstenite::{WebSocketStream, MaybeTlsStream};
use tokio::net::TcpStream;
use x25519_dalek::{EphemeralSecret, PublicKey, SharedSecret};
use chacha20poly1305::{ChaCha20Poly1305, Key, Nonce, aead::{Aead, NewAead}};

pub struct SecureP2PConnection {
    stream: WebSocketStream<MaybeTlsStream<TcpStream>>,
    cipher: ChaCha20Poly1305,
    peer_public_key: PublicKey,
    local_secret: EphemeralSecret,
    shared_secret: SharedSecret,
    nonce_counter: u64,
}

impl SecureP2PConnection {
    pub async fn establish_connection(
        peer_address: &str,
        local_identity: &DeviceIdentity,
    ) -> Result<Self, Box<dyn std::error::Error>> {
        // 建立WebSocket连接
        let (stream, _) = tokio_tungstenite::connect_async(peer_address).await?;

        // 生成临时密钥对
        let local_secret = EphemeralSecret::new(rand::rngs::OsRng);
        let local_public = PublicKey::from(&local_secret);

        // 密钥交换
        let peer_public_key = Self::perform_key_exchange(&stream, local_public, local_identity).await?;

        // 计算共享密钥
        let shared_secret = local_secret.diffie_hellman(&peer_public_key);

        // 派生加密密钥
        let cipher_key = Self::derive_cipher_key(&shared_secret);
        let cipher = ChaCha20Poly1305::new(&cipher_key);

        Ok(Self {
            stream,
            cipher,
            peer_public_key,
            local_secret,
            shared_secret,
            nonce_counter: 0,
        })
    }

    async fn perform_key_exchange(
        stream: &WebSocketStream<MaybeTlsStream<TcpStream>>,
        local_public: PublicKey,
        local_identity: &DeviceIdentity,
    ) -> Result<PublicKey, Box<dyn std::error::Error>> {
        // 创建密钥交换消息
        let key_exchange_msg = KeyExchangeMessage {
            public_key: local_public.as_bytes().to_vec(),
            device_id: local_identity.device_id.clone(),
            signature: local_identity.sign_data(local_public.as_bytes())?,
            timestamp: chrono::Utc::now(),
        };

        // 发送密钥交换消息
        let msg_json = serde_json::to_string(&key_exchange_msg)?;
        stream.send(tokio_tungstenite::tungstenite::Message::Text(msg_json)).await?;

        // 接收对方的密钥交换消息
        if let Some(response) = stream.next().await {
            let response_msg = response?;
            if let tokio_tungstenite::tungstenite::Message::Text(text) = response_msg {
                let peer_exchange: KeyExchangeMessage = serde_json::from_str(&text)?;

                // 验证对方签名
                if !Self::verify_peer_signature(&peer_exchange)? {
                    return Err("Invalid peer signature".into());
                }

                // 返回对方公钥
                let peer_public_bytes: [u8; 32] = peer_exchange.public_key.try_into()
                    .map_err(|_| "Invalid public key length")?;
                return Ok(PublicKey::from(peer_public_bytes));
            }
        }

        Err("Key exchange failed".into())
    }

    fn derive_cipher_key(shared_secret: &SharedSecret) -> Key {
        // 使用HKDF派生加密密钥
        let hkdf = hkdf::Hkdf::<sha2::Sha256>::new(None, shared_secret.as_bytes());
        let mut key_bytes = [0u8; 32];
        hkdf.expand(b"AI_STUDIO_P2P_ENCRYPTION", &mut key_bytes)
            .expect("HKDF expand failed");

        *Key::from_slice(&key_bytes)
    }

    pub async fn send_encrypted_message(&mut self, message: &[u8]) -> Result<(), Box<dyn std::error::Error>> {
        // 生成nonce
        let mut nonce_bytes = [0u8; 12];
        nonce_bytes[..8].copy_from_slice(&self.nonce_counter.to_le_bytes());
        let nonce = Nonce::from_slice(&nonce_bytes);

        // 加密消息
        let encrypted = self.cipher.encrypt(nonce, message)?;

        // 创建安全消息
        let secure_msg = SecureMessage {
            nonce: nonce_bytes.to_vec(),
            encrypted_data: encrypted,
            timestamp: chrono::Utc::now(),
        };

        // 发送消息
        let msg_json = serde_json::to_string(&secure_msg)?;
        self.stream.send(tokio_tungstenite::tungstenite::Message::Text(msg_json)).await?;

        self.nonce_counter += 1;
        Ok(())
    }

    pub async fn receive_encrypted_message(&mut self) -> Result<Vec<u8>, Box<dyn std::error::Error>> {
        if let Some(message) = self.stream.next().await {
            let message = message?;
            if let tokio_tungstenite::tungstenite::Message::Text(text) = message {
                let secure_msg: SecureMessage = serde_json::from_str(&text)?;

                // 验证时间戳（防重放攻击）
                let now = chrono::Utc::now();
                let msg_age = now.signed_duration_since(secure_msg.timestamp);
                if msg_age.num_seconds() > 300 { // 5分钟超时
                    return Err("Message too old".into());
                }

                // 解密消息
                let nonce = Nonce::from_slice(&secure_msg.nonce);
                let decrypted = self.cipher.decrypt(nonce, secure_msg.encrypted_data.as_slice())?;

                return Ok(decrypted);
            }
        }

        Err("No message received".into())
    }

    fn verify_peer_signature(exchange: &KeyExchangeMessage) -> Result<bool, Box<dyn std::error::Error>> {
        // 这里应该实现数字签名验证
        // 简化实现：检查设备ID格式
        Ok(exchange.device_id.len() > 0 && exchange.signature.len() > 0)
    }
}

#[derive(Debug, Clone, serde::Serialize, serde::Deserialize)]
pub struct KeyExchangeMessage {
    pub public_key: Vec<u8>,
    pub device_id: String,
    pub signature: Vec<u8>,
    pub timestamp: chrono::DateTime<chrono::Utc>,
}

#[derive(Debug, Clone, serde::Serialize, serde::Deserialize)]
pub struct SecureMessage {
    pub nonce: Vec<u8>,
    pub encrypted_data: Vec<u8>,
    pub timestamp: chrono::DateTime<chrono::Utc>,
}

// 设备身份管理
pub struct DeviceIdentity {
    pub device_id: String,
    pub private_key: ed25519_dalek::Keypair,
    pub certificate: Option<DeviceCertificate>,
}

impl DeviceIdentity {
    pub fn new() -> Result<Self, Box<dyn std::error::Error>> {
        let mut csprng = rand::rngs::OsRng;
        let keypair = ed25519_dalek::Keypair::generate(&mut csprng);
        let device_id = uuid::Uuid::new_v4().to_string();

        Ok(Self {
            device_id,
            private_key: keypair,
            certificate: None,
        })
    }

    pub fn sign_data(&self, data: &[u8]) -> Result<Vec<u8>, Box<dyn std::error::Error>> {
        use ed25519_dalek::Signer;
        let signature = self.private_key.sign(data);
        Ok(signature.to_bytes().to_vec())
    }

    pub fn verify_signature(&self, data: &[u8], signature: &[u8]) -> Result<bool, Box<dyn std::error::Error>> {
        use ed25519_dalek::{Verifier, Signature};

        let sig = Signature::from_bytes(signature)?;
        match self.private_key.public.verify(data, &sig) {
            Ok(_) => Ok(true),
            Err(_) => Ok(false),
        }
    }
}

#[derive(Debug, Clone, serde::Serialize, serde::Deserialize)]
pub struct DeviceCertificate {
    pub device_id: String,
    pub public_key: Vec<u8>,
    pub issued_by: String,
    pub valid_from: chrono::DateTime<chrono::Utc>,
    pub valid_until: chrono::DateTime<chrono::Utc>,
    pub signature: Vec<u8>,
}
```

## 15. 测试策略

### 15.1 前端测试

#### 15.1.1 单元测试 (Vitest)

```typescript
// 组件单元测试示例
import { describe, it, expect, vi, beforeEach } from 'vitest'
import { mount } from '@vue/test-utils'
import { createPinia, setActivePinia } from 'pinia'
import ChatMessage from '@/components/chat/ChatMessage.vue'
import { useChatStore } from '@/stores/chat'

describe('ChatMessage', () => {
  beforeEach(() => {
    setActivePinia(createPinia())
  })

  it('renders user message correctly', () => {
    const message = {
      id: '1',
      session_id: 'session1',
      role: 'user',
      content: 'Hello, AI!',
      created_at: new Date().toISOString()
    }

    const wrapper = mount(ChatMessage, {
      props: { message }
    })

    expect(wrapper.text()).toContain('Hello, AI!')
    expect(wrapper.classes()).toContain('user-message')
  })

  it('renders assistant message with markdown', () => {
    const message = {
      id: '2',
      session_id: 'session1',
      role: 'assistant',
      content: '```python\nprint("Hello")\n```',
      created_at: new Date().toISOString()
    }

    const wrapper = mount(ChatMessage, {
      props: { message }
    })

    expect(wrapper.find('code').exists()).toBe(true)
    expect(wrapper.classes()).toContain('assistant-message')
  })

  it('emits regenerate event when regenerate button clicked', async () => {
    const message = {
      id: '3',
      session_id: 'session1',
      role: 'assistant',
      content: 'Test response',
      created_at: new Date().toISOString()
    }

    const wrapper = mount(ChatMessage, {
      props: { message }
    })

    await wrapper.find('[data-testid="regenerate-button"]').trigger('click')

    expect(wrapper.emitted('regenerate')).toBeTruthy()
    expect(wrapper.emitted('regenerate')[0]).toEqual(['3'])
  })

  it('copies message content to clipboard', async () => {
    // Mock clipboard API
    Object.assign(navigator, {
      clipboard: {
        writeText: vi.fn().mockResolvedValue(undefined)
      }
    })

    const message = {
      id: '4',
      session_id: 'session1',
      role: 'user',
      content: 'Copy this text',
      created_at: new Date().toISOString()
    }

    const wrapper = mount(ChatMessage, {
      props: { message }
    })

    await wrapper.find('[data-testid="copy-button"]').trigger('click')

    expect(navigator.clipboard.writeText).toHaveBeenCalledWith('Copy this text')
  })
})

// Store单元测试
describe('ChatStore', () => {
  beforeEach(() => {
    setActivePinia(createPinia())
  })

  it('creates new session', async () => {
    const chatStore = useChatStore()

    // Mock API
    vi.mock('@/api/chat', () => ({
      chatAPI: {
        createSession: vi.fn().mockResolvedValue({
          data: {
            id: 'new-session',
            title: 'New Chat',
            created_at: new Date().toISOString()
          }
        })
      }
    }))

    await chatStore.createSession('New Chat')

    expect(chatStore.sessions).toHaveLength(1)
    expect(chatStore.sessions[0].title).toBe('New Chat')
    expect(chatStore.currentSessionId).toBe('new-session')
  })

  it('sends message and updates state', async () => {
    const chatStore = useChatStore()

    // Setup initial state
    chatStore.currentSessionId = 'test-session'

    // Mock API
    vi.mock('@/api/chat', () => ({
      chatAPI: {
        sendMessage: vi.fn().mockResolvedValue({
          data: {
            message_id: 'msg-123',
            status: 'success'
          }
        })
      }
    }))

    await chatStore.sendMessage('Hello')

    expect(chatStore.messages).toHaveLength(1)
    expect(chatStore.messages[0].content).toBe('Hello')
    expect(chatStore.messages[0].role).toBe('user')
  })

  it('handles stream response correctly', async () => {
    const chatStore = useChatStore()

    // Mock EventSource
    const mockEventSource = {
      onmessage: null,
      onerror: null,
      close: vi.fn()
    }

    global.EventSource = vi.fn(() => mockEventSource)

    chatStore.currentSessionId = 'test-session'

    // Start receiving stream
    const streamPromise = chatStore.receiveStreamResponse('msg-123')

    // Simulate stream events
    mockEventSource.onmessage({
      data: JSON.stringify({ type: 'token', content: 'Hello' })
    })
    mockEventSource.onmessage({
      data: JSON.stringify({ type: 'token', content: ' World' })
    })
    mockEventSource.onmessage({
      data: JSON.stringify({ type: 'done', content: '' })
    })

    await streamPromise

    const aiMessage = chatStore.messages.find(m => m.role === 'assistant')
    expect(aiMessage?.content).toBe('Hello World')
  })
})

// 工具函数测试
describe('Utils', () => {
  describe('formatTime', () => {
    it('formats recent time correctly', () => {
      const now = new Date()
      const fiveMinutesAgo = new Date(now.getTime() - 5 * 60 * 1000)

      const result = formatTime(fiveMinutesAgo.toISOString())
      expect(result).toContain('5 minutes ago')
    })

    it('formats old time correctly', () => {
      const yesterday = new Date()
      yesterday.setDate(yesterday.getDate() - 1)

      const result = formatTime(yesterday.toISOString())
      expect(result).toContain('1 day ago')
    })
  })

  describe('debounce', () => {
    it('delays function execution', async () => {
      const fn = vi.fn()
      const debouncedFn = debounce(fn, 100)

      debouncedFn()
      debouncedFn()
      debouncedFn()

      expect(fn).not.toHaveBeenCalled()

      await new Promise(resolve => setTimeout(resolve, 150))

      expect(fn).toHaveBeenCalledTimes(1)
    })
  })
})
```

#### 15.1.2 集成测试

```typescript
// API集成测试
import { describe, it, expect, beforeAll, afterAll } from 'vitest'
import { setupServer } from 'msw/node'
import { rest } from 'msw'
import { chatAPI } from '@/api/chat'

const server = setupServer(
  rest.post('/api/chat/sessions', (req, res, ctx) => {
    return res(ctx.json({
      code: 200,
      data: {
        id: 'session-123',
        title: 'Test Session',
        created_at: new Date().toISOString()
      }
    }))
  }),

  rest.get('/api/chat/sessions', (req, res, ctx) => {
    const page = req.url.searchParams.get('page') || '1'
    const limit = req.url.searchParams.get('limit') || '20'

    return res(ctx.json({
      code: 200,
      data: {
        items: [
          {
            id: 'session-1',
            title: 'Session 1',
            created_at: new Date().toISOString()
          }
        ],
        total: 1,
        page: parseInt(page),
        limit: parseInt(limit),
        has_more: false
      }
    }))
  }),

  rest.post('/api/chat/send', (req, res, ctx) => {
    return res(ctx.json({
      code: 200,
      data: {
        message_id: 'msg-456',
        status: 'success'
      }
    }))
  })
)

describe('Chat API Integration', () => {
  beforeAll(() => server.listen())
  afterAll(() => server.close())

  it('creates session successfully', async () => {
    const response = await chatAPI.createSession({
      title: 'Test Session'
    })

    expect(response.code).toBe(200)
    expect(response.data.id).toBe('session-123')
    expect(response.data.title).toBe('Test Session')
  })

  it('fetches sessions with pagination', async () => {
    const response = await chatAPI.getSessions({
      page: 1,
      limit: 10
    })

    expect(response.code).toBe(200)
    expect(response.data.items).toHaveLength(1)
    expect(response.data.page).toBe(1)
    expect(response.data.limit).toBe(10)
  })

  it('sends message successfully', async () => {
    const response = await chatAPI.sendMessage({
      session_id: 'session-123',
      message: 'Hello AI'
    })

    expect(response.code).toBe(200)
    expect(response.data.status).toBe('success')
    expect(response.data.message_id).toBe('msg-456')
  })

  it('handles API errors gracefully', async () => {
    server.use(
      rest.post('/api/chat/sessions', (req, res, ctx) => {
        return res(ctx.status(400), ctx.json({
          code: 400,
          message: 'Invalid request'
        }))
      })
    )

    await expect(chatAPI.createSession({ title: '' }))
      .rejects.toThrow('Invalid request')
  })
})

// 组件集成测试
describe('Chat Integration', () => {
  it('complete chat workflow', async () => {
    const wrapper = mount(ChatContainer, {
      global: {
        plugins: [createPinia()]
      }
    })

    // Create new session
    await wrapper.find('[data-testid="new-session-btn"]').trigger('click')

    expect(wrapper.find('[data-testid="session-title"]').text())
      .toContain('New Session')

    // Send message
    const input = wrapper.find('[data-testid="message-input"]')
    await input.setValue('Hello AI')
    await wrapper.find('[data-testid="send-btn"]').trigger('click')

    // Check message appears
    expect(wrapper.find('[data-testid="message-list"]').text())
      .toContain('Hello AI')

    // Check loading state
    expect(wrapper.find('[data-testid="loading-indicator"]').exists())
      .toBe(true)
  })
})
```

### 15.2 后端测试

#### 15.2.1 Rust单元测试

```rust
// 聊天服务测试
#[cfg(test)]
mod chat_tests {
    use super::*;
    use tokio_test;
    use sqlx::SqlitePool;
    use tempfile::tempdir;

    async fn setup_test_db() -> SqlitePool {
        let temp_dir = tempdir().unwrap();
        let db_path = temp_dir.path().join("test.db");
        let database_url = format!("sqlite:{}", db_path.display());

        let pool = SqlitePool::connect(&database_url).await.unwrap();
        sqlx::migrate!("./migrations").run(&pool).await.unwrap();

        pool
    }

    #[tokio::test]
    async fn test_create_chat_session() {
        let pool = setup_test_db().await;
        let chat_service = ChatService::new(pool);

        let session = chat_service.create_session(
            Some("Test Session".to_string()),
            None,
        ).await.unwrap();

        assert_eq!(session.title, "Test Session");
        assert!(!session.id.is_empty());
        assert_eq!(session.message_count, 0);
    }

    #[tokio::test]
    async fn test_send_message() {
        let pool = setup_test_db().await;
        let chat_service = ChatService::new(pool);

        // Create session first
        let session = chat_service.create_session(
            Some("Test Session".to_string()),
            None,
        ).await.unwrap();

        // Send message
        let request = SendMessageRequest {
            session_id: session.id.clone(),
            message: "Hello AI".to_string(),
            attachments: None,
            model_config: None,
        };

        let message_id = chat_service.send_message(request).await.unwrap();
        assert!(!message_id.is_empty());

        // Verify message was saved
        let messages = chat_service.get_messages(&session.id, 10, 0).await.unwrap();
        assert_eq!(messages.len(), 1);
        assert_eq!(messages[0].content, "Hello AI");
        assert_eq!(messages[0].role, "user");
    }

    #[tokio::test]
    async fn test_message_validation() {
        let pool = setup_test_db().await;
        let chat_service = ChatService::new(pool);

        let request = SendMessageRequest {
            session_id: "nonexistent".to_string(),
            message: "Hello".to_string(),
            attachments: None,
            model_config: None,
        };

        let result = chat_service.send_message(request).await;
        assert!(result.is_err());
        assert!(result.unwrap_err().to_string().contains("Session not found"));
    }

    #[tokio::test]
    async fn test_concurrent_messages() {
        let pool = setup_test_db().await;
        let chat_service = Arc::new(ChatService::new(pool));

        let session = chat_service.create_session(
            Some("Concurrent Test".to_string()),
            None,
        ).await.unwrap();

        // Send multiple messages concurrently
        let mut handles = vec![];
        for i in 0..10 {
            let service = chat_service.clone();
            let session_id = session.id.clone();

            let handle = tokio::spawn(async move {
                let request = SendMessageRequest {
                    session_id,
                    message: format!("Message {}", i),
                    attachments: None,
                    model_config: None,
                };
                service.send_message(request).await
            });

            handles.push(handle);
        }

        // Wait for all messages
        let results: Vec<_> = futures::future::join_all(handles).await;

        // All should succeed
        for result in results {
            assert!(result.unwrap().is_ok());
        }

        // Verify all messages were saved
        let messages = chat_service.get_messages(&session.id, 20, 0).await.unwrap();
        assert_eq!(messages.len(), 10);
    }
}

// 知识库服务测试
#[cfg(test)]
mod knowledge_tests {
    use super::*;
    use tempfile::tempdir;

    #[tokio::test]
    async fn test_create_knowledge_base() {
        let pool = setup_test_db().await;
        let kb_service = KnowledgeBaseService::new(pool);

        let kb = kb_service.create_knowledge_base(
            "Test KB".to_string(),
            Some("Test Description".to_string()),
            None,
            None,
            None,
        ).await.unwrap();

        assert_eq!(kb.name, "Test KB");
        assert_eq!(kb.description.unwrap(), "Test Description");
        assert_eq!(kb.document_count, 0);
    }

    #[tokio::test]
    async fn test_upload_document() {
        let pool = setup_test_db().await;
        let kb_service = KnowledgeBaseService::new(pool);

        let kb = kb_service.create_knowledge_base(
            "Test KB".to_string(),
            None,
            None,
            None,
            None,
        ).await.unwrap();

        // Create test document
        let temp_dir = tempdir().unwrap();
        let doc_path = temp_dir.path().join("test.txt");
        std::fs::write(&doc_path, "This is a test document content.").unwrap();

        let document = kb_service.upload_document(
            &kb.id,
            doc_path.to_str().unwrap(),
            "test.txt",
            true,
        ).await.unwrap();

        assert_eq!(document.name, "test.txt");
        assert_eq!(document.kb_id, kb.id);
        assert_eq!(document.status, "processing");
    }

    #[tokio::test]
    async fn test_document_processing() {
        let pool = setup_test_db().await;
        let kb_service = KnowledgeBaseService::new(pool);

        let kb = kb_service.create_knowledge_base(
            "Test KB".to_string(),
            None,
            None,
            Some(100), // Small chunk size for testing
            Some(10),
        ).await.unwrap();

        // Upload and process document
        let temp_dir = tempdir().unwrap();
        let doc_path = temp_dir.path().join("test.txt");
        let content = "This is a test document. ".repeat(20); // Create longer content
        std::fs::write(&doc_path, &content).unwrap();

        let document = kb_service.upload_document(
            &kb.id,
            doc_path.to_str().unwrap(),
            "test.txt",
            true,
        ).await.unwrap();

        // Wait for processing to complete
        tokio::time::sleep(tokio::time::Duration::from_secs(2)).await;

        let updated_doc = kb_service.get_document(&document.id).await.unwrap();
        assert_eq!(updated_doc.status, "completed");
        assert!(updated_doc.chunks_count > 0);
    }

    #[tokio::test]
    async fn test_semantic_search() {
        let pool = setup_test_db().await;
        let kb_service = KnowledgeBaseService::new(pool);

        // Setup knowledge base with documents
        let kb = kb_service.create_knowledge_base(
            "Search Test KB".to_string(),
            None,
            None,
            None,
            None,
        ).await.unwrap();

        // Add test documents
        let docs = vec![
            "Artificial intelligence is transforming technology.",
            "Machine learning algorithms improve with data.",
            "Natural language processing enables human-computer interaction.",
        ];

        for (i, content) in docs.iter().enumerate() {
            let temp_dir = tempdir().unwrap();
            let doc_path = temp_dir.path().join(format!("doc{}.txt", i));
            std::fs::write(&doc_path, content).unwrap();

            kb_service.upload_document(
                &kb.id,
                doc_path.to_str().unwrap(),
                &format!("doc{}.txt", i),
                true,
            ).await.unwrap();
        }

        // Wait for processing
        tokio::time::sleep(tokio::time::Duration::from_secs(3)).await;

        // Perform search
        let results = kb_service.search(
            &kb.id,
            "artificial intelligence",
            Some(5),
            Some(0.5),
            None,
        ).await.unwrap();

        assert!(!results.is_empty());
        assert!(results[0].score > 0.5);
        assert!(results[0].content.to_lowercase().contains("artificial"));
    }
}

// 性能测试
#[cfg(test)]
mod performance_tests {
    use super::*;
    use std::time::Instant;

    #[tokio::test]
    async fn test_database_performance() {
        let pool = setup_test_db().await;
        let chat_service = ChatService::new(pool);

        let session = chat_service.create_session(
            Some("Performance Test".to_string()),
            None,
        ).await.unwrap();

        let start = Instant::now();

        // Insert 1000 messages
        for i in 0..1000 {
            let request = SendMessageRequest {
                session_id: session.id.clone(),
                message: format!("Performance test message {}", i),
                attachments: None,
                model_config: None,
            };

            chat_service.send_message(request).await.unwrap();
        }

        let duration = start.elapsed();
        println!("Inserted 1000 messages in {:?}", duration);

        // Should complete within reasonable time
        assert!(duration.as_secs() < 10);

        // Test query performance
        let start = Instant::now();
        let messages = chat_service.get_messages(&session.id, 100, 0).await.unwrap();
        let query_duration = start.elapsed();

        println!("Queried 100 messages in {:?}", query_duration);
        assert_eq!(messages.len(), 100);
        assert!(query_duration.as_millis() < 100);
    }

    #[tokio::test]
    async fn test_concurrent_sessions() {
        let pool = setup_test_db().await;
        let chat_service = Arc::new(ChatService::new(pool));

        let start = Instant::now();

        // Create 100 concurrent sessions
        let mut handles = vec![];
        for i in 0..100 {
            let service = chat_service.clone();
            let handle = tokio::spawn(async move {
                service.create_session(
                    Some(format!("Concurrent Session {}", i)),
                    None,
                ).await
            });
            handles.push(handle);
        }

        let results: Vec<_> = futures::future::join_all(handles).await;
        let duration = start.elapsed();

        println!("Created 100 sessions in {:?}", duration);

        // All should succeed
        for result in results {
            assert!(result.unwrap().is_ok());
        }

        assert!(duration.as_secs() < 5);
    }
}
```

## 16. 监控和日志方案

### 16.1 应用监控系统

#### 16.1.1 性能监控

```rust
// 性能监控服务
use std::sync::Arc;
use std::time::{Duration, Instant};
use tokio::sync::RwLock;
use serde::{Serialize, Deserialize};

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PerformanceMetrics {
    pub timestamp: chrono::DateTime<chrono::Utc>,
    pub cpu_usage: f64,
    pub memory_usage: u64,
    pub memory_total: u64,
    pub gpu_usage: Option<f64>,
    pub gpu_memory_usage: Option<u64>,
    pub disk_usage: u64,
    pub disk_total: u64,
    pub network_io: NetworkIO,
    pub active_connections: u32,
    pub inference_queue_size: u32,
    pub response_times: ResponseTimeMetrics,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct NetworkIO {
    pub bytes_sent: u64,
    pub bytes_received: u64,
    pub packets_sent: u64,
    pub packets_received: u64,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ResponseTimeMetrics {
    pub avg_response_time: f64,
    pub p50_response_time: f64,
    pub p95_response_time: f64,
    pub p99_response_time: f64,
    pub max_response_time: f64,
}

pub struct PerformanceMonitor {
    metrics_history: Arc<RwLock<Vec<PerformanceMetrics>>>,
    response_times: Arc<RwLock<Vec<f64>>>,
    max_history_size: usize,
    collection_interval: Duration,
}

impl PerformanceMonitor {
    pub fn new(max_history_size: usize, collection_interval: Duration) -> Self {
        Self {
            metrics_history: Arc::new(RwLock::new(Vec::new())),
            response_times: Arc::new(RwLock::new(Vec::new())),
            max_history_size,
            collection_interval,
        }
    }

    pub async fn start_monitoring(&self) {
        let metrics_history = self.metrics_history.clone();
        let response_times = self.response_times.clone();
        let max_history_size = self.max_history_size;
        let interval = self.collection_interval;

        tokio::spawn(async move {
            let mut interval_timer = tokio::time::interval(interval);

            loop {
                interval_timer.tick().await;

                match Self::collect_system_metrics().await {
                    Ok(mut metrics) => {
                        // Calculate response time metrics
                        let response_time_metrics = {
                            let times = response_times.read().await;
                            Self::calculate_response_time_metrics(&times)
                        };

                        metrics.response_times = response_time_metrics;

                        // Store metrics
                        let mut history = metrics_history.write().await;
                        history.push(metrics);

                        // Limit history size
                        if history.len() > max_history_size {
                            history.remove(0);
                        }

                        // Clear old response times
                        let mut times = response_times.write().await;
                        times.clear();
                    }
                    Err(e) => {
                        tracing::error!("Failed to collect system metrics: {}", e);
                    }
                }
            }
        });
    }

    async fn collect_system_metrics() -> Result<PerformanceMetrics, Box<dyn std::error::Error>> {
        let timestamp = chrono::Utc::now();

        // CPU usage
        let cpu_usage = Self::get_cpu_usage().await?;

        // Memory usage
        let (memory_usage, memory_total) = Self::get_memory_usage().await?;

        // GPU usage (if available)
        let (gpu_usage, gpu_memory_usage) = Self::get_gpu_usage().await?;

        // Disk usage
        let (disk_usage, disk_total) = Self::get_disk_usage().await?;

        // Network I/O
        let network_io = Self::get_network_io().await?;

        // Application-specific metrics
        let active_connections = Self::get_active_connections().await?;
        let inference_queue_size = Self::get_inference_queue_size().await?;

        Ok(PerformanceMetrics {
            timestamp,
            cpu_usage,
            memory_usage,
            memory_total,
            gpu_usage,
            gpu_memory_usage,
            disk_usage,
            disk_total,
            network_io,
            active_connections,
            inference_queue_size,
            response_times: ResponseTimeMetrics {
                avg_response_time: 0.0,
                p50_response_time: 0.0,
                p95_response_time: 0.0,
                p99_response_time: 0.0,
                max_response_time: 0.0,
            },
        })
    }

    pub async fn record_response_time(&self, duration: Duration) {
        let mut times = self.response_times.write().await;
        times.push(duration.as_secs_f64());

        // 限制响应时间记录数量
        if times.len() > 10000 {
            times.drain(0..5000);
        }
    }

    pub async fn get_current_metrics(&self) -> Option<PerformanceMetrics> {
        let history = self.metrics_history.read().await;
        history.last().cloned()
    }

    pub async fn get_metrics_history(&self, duration: Duration) -> Vec<PerformanceMetrics> {
        let history = self.metrics_history.read().await;
        let cutoff_time = chrono::Utc::now() - chrono::Duration::from_std(duration).unwrap();

        history.iter()
            .filter(|m| m.timestamp > cutoff_time)
            .cloned()
            .collect()
    }

    fn calculate_response_time_metrics(times: &[f64]) -> ResponseTimeMetrics {
        if times.is_empty() {
            return ResponseTimeMetrics {
                avg_response_time: 0.0,
                p50_response_time: 0.0,
                p95_response_time: 0.0,
                p99_response_time: 0.0,
                max_response_time: 0.0,
            };
        }

        let mut sorted_times = times.to_vec();
        sorted_times.sort_by(|a, b| a.partial_cmp(b).unwrap());

        let avg = times.iter().sum::<f64>() / times.len() as f64;
        let p50 = Self::percentile(&sorted_times, 0.5);
        let p95 = Self::percentile(&sorted_times, 0.95);
        let p99 = Self::percentile(&sorted_times, 0.99);
        let max = sorted_times.last().copied().unwrap_or(0.0);

        ResponseTimeMetrics {
            avg_response_time: avg,
            p50_response_time: p50,
            p95_response_time: p95,
            p99_response_time: p99,
            max_response_time: max,
        }
    }

    fn percentile(sorted_data: &[f64], percentile: f64) -> f64 {
        if sorted_data.is_empty() {
            return 0.0;
        }

        let index = (percentile * (sorted_data.len() - 1) as f64).round() as usize;
        sorted_data[index.min(sorted_data.len() - 1)]
    }
}
```

### 16.2 日志管理系统

#### 16.2.1 结构化日志

```rust
// 日志管理系统
use tracing::{info, warn, error, debug, trace};
use tracing_subscriber::{layer::SubscriberExt, util::SubscriberInitExt, EnvFilter};
use tracing_appender::{rolling, non_blocking};
use serde::{Serialize, Deserialize};

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct LogEntry {
    pub id: String,
    pub timestamp: chrono::DateTime<chrono::Utc>,
    pub level: String,
    pub module: String,
    pub message: String,
    pub metadata: serde_json::Value,
    pub request_id: Option<String>,
    pub user_id: Option<String>,
    pub session_id: Option<String>,
}

pub struct LogManager {
    log_buffer: Arc<RwLock<Vec<LogEntry>>>,
    max_buffer_size: usize,
    db_pool: SqlitePool,
}

impl LogManager {
    pub fn new(db_pool: SqlitePool, max_buffer_size: usize) -> Self {
        Self {
            log_buffer: Arc::new(RwLock::new(Vec::new())),
            max_buffer_size,
            db_pool,
        }
    }

    pub fn init_logging(&self) -> Result<(), Box<dyn std::error::Error>> {
        // 创建文件输出
        let file_appender = rolling::daily("logs", "ai-studio.log");
        let (non_blocking_file, _guard) = non_blocking(file_appender);

        // 创建控制台输出
        let console_layer = tracing_subscriber::fmt::layer()
            .with_target(true)
            .with_thread_ids(true)
            .with_file(true)
            .with_line_number(true);

        // 创建文件输出层
        let file_layer = tracing_subscriber::fmt::layer()
            .with_writer(non_blocking_file)
            .with_ansi(false)
            .json();

        // 创建自定义层用于数据库存储
        let db_layer = DatabaseLogLayer::new(self.log_buffer.clone());

        // 组合所有层
        tracing_subscriber::registry()
            .with(EnvFilter::from_default_env().add_directive("ai_studio=debug".parse()?))
            .with(console_layer)
            .with(file_layer)
            .with(db_layer)
            .init();

        // 启动日志持久化任务
        self.start_log_persistence();

        Ok(())
    }

    fn start_log_persistence(&self) {
        let buffer = self.log_buffer.clone();
        let pool = self.db_pool.clone();
        let max_size = self.max_buffer_size;

        tokio::spawn(async move {
            let mut interval = tokio::time::interval(Duration::from_secs(10));

            loop {
                interval.tick().await;

                let logs_to_persist = {
                    let mut buffer_guard = buffer.write().await;
                    if buffer_guard.is_empty() {
                        continue;
                    }

                    let logs = buffer_guard.clone();
                    buffer_guard.clear();
                    logs
                };

                if let Err(e) = Self::persist_logs(&pool, &logs_to_persist).await {
                    error!("Failed to persist logs: {}", e);

                    // 如果持久化失败，将日志放回缓冲区
                    let mut buffer_guard = buffer.write().await;
                    buffer_guard.extend(logs_to_persist);

                    // 限制缓冲区大小
                    if buffer_guard.len() > max_size {
                        buffer_guard.drain(0..buffer_guard.len() - max_size);
                    }
                }
            }
        });
    }

    async fn persist_logs(pool: &SqlitePool, logs: &[LogEntry]) -> Result<(), sqlx::Error> {
        let mut tx = pool.begin().await?;

        for log in logs {
            sqlx::query(
                "INSERT INTO logs (id, timestamp, level, module, message, metadata, request_id, user_id, session_id)
                 VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)"
            )
            .bind(&log.id)
            .bind(&log.timestamp)
            .bind(&log.level)
            .bind(&log.module)
            .bind(&log.message)
            .bind(log.metadata.to_string())
            .bind(&log.request_id)
            .bind(&log.user_id)
            .bind(&log.session_id)
            .execute(&mut *tx)
            .await?;
        }

        tx.commit().await?;
        Ok(())
    }

    pub async fn query_logs(
        &self,
        filters: LogQueryFilters,
    ) -> Result<Vec<LogEntry>, Box<dyn std::error::Error>> {
        let mut query = "SELECT * FROM logs WHERE 1=1".to_string();
        let mut params = Vec::new();

        if let Some(level) = &filters.level {
            query.push_str(" AND level = ?");
            params.push(level.clone());
        }

        if let Some(module) = &filters.module {
            query.push_str(" AND module = ?");
            params.push(module.clone());
        }

        if let Some(start_time) = &filters.start_time {
            query.push_str(" AND timestamp >= ?");
            params.push(start_time.to_rfc3339());
        }

        if let Some(end_time) = &filters.end_time {
            query.push_str(" AND timestamp <= ?");
            params.push(end_time.to_rfc3339());
        }

        if let Some(search) = &filters.search {
            query.push_str(" AND message LIKE ?");
            params.push(format!("%{}%", search));
        }

        query.push_str(" ORDER BY timestamp DESC LIMIT ? OFFSET ?");
        params.push(filters.limit.unwrap_or(100).to_string());
        params.push(filters.offset.unwrap_or(0).to_string());

        let mut sql_query = sqlx::query(&query);
        for param in &params {
            sql_query = sql_query.bind(param);
        }

        let rows = sql_query.fetch_all(&self.db_pool).await?;

        let logs = rows.into_iter().map(|row| LogEntry {
            id: row.get("id"),
            timestamp: row.get("timestamp"),
            level: row.get("level"),
            module: row.get("module"),
            message: row.get("message"),
            metadata: serde_json::from_str(row.get("metadata")).unwrap_or_default(),
            request_id: row.get("request_id"),
            user_id: row.get("user_id"),
            session_id: row.get("session_id"),
        }).collect();

        Ok(logs)
    }
}

#[derive(Debug, Clone)]
pub struct LogQueryFilters {
    pub level: Option<String>,
    pub module: Option<String>,
    pub start_time: Option<chrono::DateTime<chrono::Utc>>,
    pub end_time: Option<chrono::DateTime<chrono::Utc>>,
    pub search: Option<String>,
    pub request_id: Option<String>,
    pub user_id: Option<String>,
    pub session_id: Option<String>,
    pub limit: Option<i32>,
    pub offset: Option<i32>,
}

// 自定义日志层
struct DatabaseLogLayer {
    buffer: Arc<RwLock<Vec<LogEntry>>>,
}

impl DatabaseLogLayer {
    fn new(buffer: Arc<RwLock<Vec<LogEntry>>>) -> Self {
        Self { buffer }
    }
}

impl<S> tracing_subscriber::Layer<S> for DatabaseLogLayer
where
    S: tracing::Subscriber,
{
    fn on_event(
        &self,
        event: &tracing::Event<'_>,
        _ctx: tracing_subscriber::layer::Context<'_, S>,
    ) {
        let metadata = event.metadata();
        let mut visitor = LogVisitor::new();
        event.record(&mut visitor);

        let log_entry = LogEntry {
            id: uuid::Uuid::new_v4().to_string(),
            timestamp: chrono::Utc::now(),
            level: metadata.level().to_string(),
            module: metadata.target().to_string(),
            message: visitor.message,
            metadata: visitor.fields,
            request_id: visitor.request_id,
            user_id: visitor.user_id,
            session_id: visitor.session_id,
        };

        if let Ok(mut buffer) = self.buffer.try_write() {
            buffer.push(log_entry);
        }
    }
}

struct LogVisitor {
    message: String,
    fields: serde_json::Value,
    request_id: Option<String>,
    user_id: Option<String>,
    session_id: Option<String>,
}

impl LogVisitor {
    fn new() -> Self {
        Self {
            message: String::new(),
            fields: serde_json::Value::Object(serde_json::Map::new()),
            request_id: None,
            user_id: None,
            session_id: None,
        }
    }
}

impl tracing::field::Visit for LogVisitor {
    fn record_debug(&mut self, field: &tracing::field::Field, value: &dyn std::fmt::Debug) {
        match field.name() {
            "message" => {
                self.message = format!("{:?}", value);
            }
            "request_id" => {
                self.request_id = Some(format!("{:?}", value));
            }
            "user_id" => {
                self.user_id = Some(format!("{:?}", value));
            }
            "session_id" => {
                self.session_id = Some(format!("{:?}", value));
            }
            _ => {
                if let serde_json::Value::Object(ref mut map) = self.fields {
                    map.insert(field.name().to_string(), serde_json::Value::String(format!("{:?}", value)));
                }
            }
        }
    }
}

// 日志宏扩展
#[macro_export]
macro_rules! log_with_context {
    ($level:ident, $request_id:expr, $user_id:expr, $session_id:expr, $($arg:tt)*) => {
        tracing::$level!(
            request_id = $request_id,
            user_id = $user_id,
            session_id = $session_id,
            $($arg)*
        );
    };
}

// 使用示例
pub fn example_logging() {
    // 基本日志
    info!("Application started");
    warn!("High memory usage detected: {}MB", 1024);
    error!("Failed to connect to database: {}", "connection timeout");

    // 带上下文的日志
    log_with_context!(
        info,
        "req_123",
        "user_456",
        "session_789",
        "User sent message: {}",
        "Hello AI"
    );

    // 结构化日志
    info!(
        user_id = "user_123",
        action = "model_load",
        model_id = "llama2-7b",
        duration_ms = 2500,
        "Model loaded successfully"
    );
}
```

## 17. 国际化方案

### 17.1 前端国际化实现

#### 17.1.1 Vue I18n 配置

```typescript
// i18n配置 (src/i18n/index.ts)
import { createI18n } from 'vue-i18n'
import { ref, computed } from 'vue'

// 语言包导入
import zhCN from './locales/zh-CN'
import enUS from './locales/en-US'
import jaJP from './locales/ja-JP'
import koKR from './locales/ko-KR'
import frFR from './locales/fr-FR'
import deDE from './locales/de-DE'
import esES from './locales/es-ES'
import ruRU from './locales/ru-RU'

// 支持的语言列表
export const SUPPORTED_LOCALES = [
  { code: 'zh-CN', name: '简体中文', flag: '🇨🇳' },
  { code: 'en-US', name: 'English', flag: '🇺🇸' },
  { code: 'ja-JP', name: '日本語', flag: '🇯🇵' },
  { code: 'ko-KR', name: '한국어', flag: '🇰🇷' },
  { code: 'fr-FR', name: 'Français', flag: '🇫🇷' },
  { code: 'de-DE', name: 'Deutsch', flag: '🇩🇪' },
  { code: 'es-ES', name: 'Español', flag: '🇪🇸' },
  { code: 'ru-RU', name: 'Русский', flag: '🇷🇺' },
] as const

export type SupportedLocale = typeof SUPPORTED_LOCALES[number]['code']

// 消息对象
const messages = {
  'zh-CN': zhCN,
  'en-US': enUS,
  'ja-JP': jaJP,
  'ko-KR': koKR,
  'fr-FR': frFR,
  'de-DE': deDE,
  'es-ES': esES,
  'ru-RU': ruRU,
}

// 检测浏览器语言
function detectBrowserLocale(): SupportedLocale {
  const browserLang = navigator.language || navigator.languages[0]

  // 精确匹配
  for (const locale of SUPPORTED_LOCALES) {
    if (browserLang === locale.code) {
      return locale.code
    }
  }

  // 语言代码匹配（忽略地区）
  const langCode = browserLang.split('-')[0]
  for (const locale of SUPPORTED_LOCALES) {
    if (locale.code.startsWith(langCode)) {
      return locale.code
    }
  }

  // 默认返回英语
  return 'en-US'
}

// 从本地存储获取语言设置
function getStoredLocale(): SupportedLocale {
  const stored = localStorage.getItem('ai-studio-locale')
  if (stored && SUPPORTED_LOCALES.some(l => l.code === stored)) {
    return stored as SupportedLocale
  }
  return detectBrowserLocale()
}

// 创建i18n实例
export const i18n = createI18n({
  legacy: false,
  locale: getStoredLocale(),
  fallbackLocale: 'en-US',
  messages,
  globalInjection: true,
  silentTranslationWarn: true,
  silentFallbackWarn: true,
})

// 语言切换功能
export const useI18nSetup = () => {
  const currentLocale = ref<SupportedLocale>(getStoredLocale())

  const setLocale = (locale: SupportedLocale) => {
    currentLocale.value = locale
    i18n.global.locale.value = locale
    localStorage.setItem('ai-studio-locale', locale)

    // 更新HTML lang属性
    document.documentElement.lang = locale

    // 更新页面标题
    updatePageTitle()

    // 触发自定义事件
    window.dispatchEvent(new CustomEvent('locale-changed', { detail: locale }))
  }

  const updatePageTitle = () => {
    const { t } = i18n.global
    document.title = t('app.title')
  }

  const getCurrentLocaleInfo = computed(() => {
    return SUPPORTED_LOCALES.find(l => l.code === currentLocale.value)
  })

  return {
    currentLocale,
    setLocale,
    getCurrentLocaleInfo,
    supportedLocales: SUPPORTED_LOCALES,
  }
}

// 数字和日期格式化
export const useFormatters = () => {
  const { locale } = i18n.global

  const formatNumber = (num: number, options?: Intl.NumberFormatOptions) => {
    return new Intl.NumberFormat(locale.value, options).format(num)
  }

  const formatCurrency = (amount: number, currency = 'USD') => {
    return new Intl.NumberFormat(locale.value, {
      style: 'currency',
      currency,
    }).format(amount)
  }

  const formatDate = (date: Date | string, options?: Intl.DateTimeFormatOptions) => {
    const dateObj = typeof date === 'string' ? new Date(date) : date
    return new Intl.DateTimeFormat(locale.value, options).format(dateObj)
  }

  const formatRelativeTime = (date: Date | string) => {
    const dateObj = typeof date === 'string' ? new Date(date) : date
    const now = new Date()
    const diffInSeconds = Math.floor((now.getTime() - dateObj.getTime()) / 1000)

    const rtf = new Intl.RelativeTimeFormat(locale.value, { numeric: 'auto' })

    if (diffInSeconds < 60) {
      return rtf.format(-diffInSeconds, 'second')
    } else if (diffInSeconds < 3600) {
      return rtf.format(-Math.floor(diffInSeconds / 60), 'minute')
    } else if (diffInSeconds < 86400) {
      return rtf.format(-Math.floor(diffInSeconds / 3600), 'hour')
    } else {
      return rtf.format(-Math.floor(diffInSeconds / 86400), 'day')
    }
  }

  const formatFileSize = (bytes: number) => {
    const units = ['bytes', 'KB', 'MB', 'GB', 'TB']
    let size = bytes
    let unitIndex = 0

    while (size >= 1024 && unitIndex < units.length - 1) {
      size /= 1024
      unitIndex++
    }

    const { t } = i18n.global
    return `${formatNumber(size, { maximumFractionDigits: 1 })} ${t(`common.units.${units[unitIndex]}`)}`
  }

  return {
    formatNumber,
    formatCurrency,
    formatDate,
    formatRelativeTime,
    formatFileSize,
  }
}
```

#### 17.1.2 语言包结构

```typescript
// 中文语言包 (src/i18n/locales/zh-CN/index.ts)
import common from './common.json'
import chat from './chat.json'
import knowledge from './knowledge.json'
import model from './model.json'
import multimodal from './multimodal.json'
import remote from './remote.json'
import network from './network.json'
import plugins from './plugins.json'
import settings from './settings.json'
import errors from './errors.json'

export default {
  common,
  chat,
  knowledge,
  model,
  multimodal,
  remote,
  network,
  plugins,
  settings,
  errors,
}

// 通用翻译 (src/i18n/locales/zh-CN/common.json)
{
  "app": {
    "title": "AI Studio",
    "description": "本地AI助手桌面应用",
    "version": "版本 {version}"
  },
  "actions": {
    "save": "保存",
    "cancel": "取消",
    "delete": "删除",
    "edit": "编辑",
    "create": "创建",
    "update": "更新",
    "refresh": "刷新",
    "search": "搜索",
    "filter": "筛选",
    "sort": "排序",
    "export": "导出",
    "import": "导入",
    "upload": "上传",
    "download": "下载",
    "copy": "复制",
    "paste": "粘贴",
    "cut": "剪切",
    "undo": "撤销",
    "redo": "重做",
    "select": "选择",
    "selectAll": "全选",
    "clear": "清空",
    "reset": "重置",
    "confirm": "确认",
    "submit": "提交",
    "close": "关闭",
    "open": "打开",
    "view": "查看",
    "preview": "预览",
    "settings": "设置",
    "help": "帮助",
    "about": "关于"
  },
  "status": {
    "loading": "加载中...",
    "saving": "保存中...",
    "processing": "处理中...",
    "completed": "已完成",
    "failed": "失败",
    "success": "成功",
    "error": "错误",
    "warning": "警告",
    "info": "信息",
    "pending": "等待中",
    "cancelled": "已取消",
    "paused": "已暂停",
    "resumed": "已恢复",
    "online": "在线",
    "offline": "离线",
    "connected": "已连接",
    "disconnected": "已断开",
    "active": "活跃",
    "inactive": "非活跃",
    "enabled": "已启用",
    "disabled": "已禁用"
  },
  "time": {
    "now": "刚刚",
    "minutesAgo": "{count} 分钟前",
    "hoursAgo": "{count} 小时前",
    "daysAgo": "{count} 天前",
    "weeksAgo": "{count} 周前",
    "monthsAgo": "{count} 个月前",
    "yearsAgo": "{count} 年前",
    "today": "今天",
    "yesterday": "昨天",
    "tomorrow": "明天",
    "thisWeek": "本周",
    "lastWeek": "上周",
    "nextWeek": "下周",
    "thisMonth": "本月",
    "lastMonth": "上月",
    "nextMonth": "下月"
  },
  "units": {
    "bytes": "字节",
    "KB": "KB",
    "MB": "MB",
    "GB": "GB",
    "TB": "TB",
    "seconds": "秒",
    "minutes": "分钟",
    "hours": "小时",
    "days": "天",
    "weeks": "周",
    "months": "月",
    "years": "年"
  },
  "validation": {
    "required": "此字段为必填项",
    "email": "请输入有效的邮箱地址",
    "minLength": "最少需要 {min} 个字符",
    "maxLength": "最多允许 {max} 个字符",
    "min": "最小值为 {min}",
    "max": "最大值为 {max}",
    "pattern": "格式不正确",
    "unique": "此值已存在",
    "fileSize": "文件大小不能超过 {size}",
    "fileType": "不支持的文件类型"
  },
  "pagination": {
    "page": "第 {current} 页，共 {total} 页",
    "items": "共 {total} 项",
    "itemsPerPage": "每页显示",
    "goToPage": "跳转到",
    "previous": "上一页",
    "next": "下一页",
    "first": "首页",
    "last": "末页"
  },
  "theme": {
    "light": "浅色主题",
    "dark": "深色主题",
    "auto": "跟随系统",
    "toggleTheme": "切换主题"
  },
  "language": {
    "current": "当前语言",
    "select": "选择语言",
    "auto": "自动检测"
  }
}

// 聊天模块翻译 (src/i18n/locales/zh-CN/chat.json)
{
  "title": "聊天",
  "newChat": "新建对话",
  "sessions": {
    "title": "对话列表",
    "empty": "暂无对话",
    "search": "搜索对话",
    "create": "创建新对话",
    "rename": "重命名",
    "delete": "删除对话",
    "export": "导出对话",
    "archive": "归档",
    "pin": "置顶",
    "unpin": "取消置顶"
  },
  "messages": {
    "input": "输入消息...",
    "send": "发送",
    "sending": "发送中...",
    "copy": "复制",
    "copySuccess": "复制成功",
    "copyFailed": "复制失败",
    "regenerate": "重新生成",
    "delete": "删除消息",
    "edit": "编辑",
    "reply": "回复",
    "quote": "引用",
    "empty": "暂无消息",
    "thinking": "思考中...",
    "typing": "正在输入...",
    "failed": "发送失败",
    "retry": "重试"
  },
  "roles": {
    "user": "用户",
    "assistant": "助手",
    "system": "系统"
  },
  "attachments": {
    "add": "添加附件",
    "image": "图片",
    "document": "文档",
    "audio": "音频",
    "video": "视频",
    "remove": "移除",
    "preview": "预览",
    "download": "下载",
    "unsupported": "不支持的文件类型"
  },
  "settings": {
    "title": "聊天设置",
    "model": "选择模型",
    "temperature": "创造性",
    "maxTokens": "最大回复长度",
    "systemPrompt": "系统提示词",
    "autoSave": "自动保存",
    "showTimestamp": "显示时间戳",
    "enableSound": "启用声音提示"
  },
  "export": {
    "title": "导出对话",
    "format": "导出格式",
    "markdown": "Markdown",
    "json": "JSON",
    "txt": "纯文本",
    "pdf": "PDF",
    "includeAttachments": "包含附件",
    "dateRange": "日期范围",
    "success": "导出成功",
    "failed": "导出失败"
  }
}
```

### 17.2 后端国际化支持

#### 17.2.1 多语言错误消息

```rust
// 国际化错误处理 (src-tauri/src/i18n/mod.rs)
use std::collections::HashMap;
use serde::{Serialize, Deserialize};

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct LocalizedMessage {
    pub key: String,
    pub params: HashMap<String, String>,
}

impl LocalizedMessage {
    pub fn new(key: &str) -> Self {
        Self {
            key: key.to_string(),
            params: HashMap::new(),
        }
    }

    pub fn with_param(mut self, key: &str, value: &str) -> Self {
        self.params.insert(key.to_string(), value.to_string());
        self
    }

    pub fn with_params(mut self, params: HashMap<String, String>) -> Self {
        self.params.extend(params);
        self
    }
}

#[derive(Debug, Clone)]
pub struct I18nManager {
    messages: HashMap<String, HashMap<String, String>>,
    default_locale: String,
}

impl I18nManager {
    pub fn new(default_locale: String) -> Self {
        let mut manager = Self {
            messages: HashMap::new(),
            default_locale,
        };

        manager.load_messages();
        manager
    }

    fn load_messages(&mut self) {
        // 加载各语言的错误消息
        self.load_locale_messages("zh-CN", include_str!("locales/zh-CN/errors.json"));
        self.load_locale_messages("en-US", include_str!("locales/en-US/errors.json"));
        self.load_locale_messages("ja-JP", include_str!("locales/ja-JP/errors.json"));
        // ... 其他语言
    }

    fn load_locale_messages(&mut self, locale: &str, json_content: &str) {
        if let Ok(messages) = serde_json::from_str::<HashMap<String, String>>(json_content) {
            self.messages.insert(locale.to_string(), messages);
        }
    }

    pub fn get_message(&self, locale: &str, key: &str, params: &HashMap<String, String>) -> String {
        let locale_messages = self.messages.get(locale)
            .or_else(|| self.messages.get(&self.default_locale))
            .unwrap_or(&HashMap::new());

        let template = locale_messages.get(key)
            .unwrap_or(&format!("Missing translation: {}", key));

        self.interpolate_message(template, params)
    }

    fn interpolate_message(&self, template: &str, params: &HashMap<String, String>) -> String {
        let mut result = template.to_string();

        for (key, value) in params {
            let placeholder = format!("{{{}}}", key);
            result = result.replace(&placeholder, value);
        }

        result
    }

    pub fn localize(&self, locale: &str, message: &LocalizedMessage) -> String {
        self.get_message(locale, &message.key, &message.params)
    }
}

// 错误消息定义 (locales/zh-CN/errors.json)
{
  "validation.required": "字段 {field} 是必需的",
  "validation.invalid_email": "邮箱格式无效",
  "validation.min_length": "字段 {field} 最少需要 {min} 个字符",
  "validation.max_length": "字段 {field} 最多允许 {max} 个字符",
  "validation.invalid_format": "字段 {field} 格式无效",

  "auth.invalid_credentials": "用户名或密码错误",
  "auth.session_expired": "会话已过期，请重新登录",
  "auth.access_denied": "访问被拒绝",

  "chat.session_not_found": "找不到对话会话",
  "chat.message_too_long": "消息长度超过限制",
  "chat.model_not_available": "模型 {model} 不可用",
  "chat.inference_failed": "AI推理失败：{error}",

  "knowledge.kb_not_found": "找不到知识库",
  "knowledge.document_not_found": "找不到文档",
  "knowledge.upload_failed": "文件上传失败：{error}",
  "knowledge.processing_failed": "文档处理失败：{error}",
  "knowledge.search_failed": "搜索失败：{error}",

  "model.not_found": "找不到模型 {model}",
  "model.load_failed": "模型加载失败：{error}",
  "model.download_failed": "模型下载失败：{error}",
  "model.insufficient_memory": "内存不足，无法加载模型",

  "network.connection_failed": "网络连接失败",
  "network.device_not_found": "找不到设备",
  "network.transfer_failed": "文件传输失败：{error}",

  "plugin.not_found": "找不到插件 {plugin}",
  "plugin.load_failed": "插件加载失败：{error}",
  "plugin.execution_failed": "插件执行失败：{error}",
  "plugin.permission_denied": "插件权限不足",

  "system.database_error": "数据库错误：{error}",
  "system.file_not_found": "找不到文件：{path}",
  "system.permission_denied": "权限不足",
  "system.disk_full": "磁盘空间不足",
  "system.memory_limit": "内存使用超过限制"
}

// 英文错误消息 (locales/en-US/errors.json)
{
  "validation.required": "Field {field} is required",
  "validation.invalid_email": "Invalid email format",
  "validation.min_length": "Field {field} must be at least {min} characters",
  "validation.max_length": "Field {field} must not exceed {max} characters",
  "validation.invalid_format": "Field {field} has invalid format",

  "auth.invalid_credentials": "Invalid username or password",
  "auth.session_expired": "Session expired, please login again",
  "auth.access_denied": "Access denied",

  "chat.session_not_found": "Chat session not found",
  "chat.message_too_long": "Message exceeds length limit",
  "chat.model_not_available": "Model {model} is not available",
  "chat.inference_failed": "AI inference failed: {error}",

  "knowledge.kb_not_found": "Knowledge base not found",
  "knowledge.document_not_found": "Document not found",
  "knowledge.upload_failed": "File upload failed: {error}",
  "knowledge.processing_failed": "Document processing failed: {error}",
  "knowledge.search_failed": "Search failed: {error}",

  "model.not_found": "Model {model} not found",
  "model.load_failed": "Model loading failed: {error}",
  "model.download_failed": "Model download failed: {error}",
  "model.insufficient_memory": "Insufficient memory to load model",

  "network.connection_failed": "Network connection failed",
  "network.device_not_found": "Device not found",
  "network.transfer_failed": "File transfer failed: {error}",

  "plugin.not_found": "Plugin {plugin} not found",
  "plugin.load_failed": "Plugin loading failed: {error}",
  "plugin.execution_failed": "Plugin execution failed: {error}",
  "plugin.permission_denied": "Insufficient plugin permissions",

  "system.database_error": "Database error: {error}",
  "system.file_not_found": "File not found: {path}",
  "system.permission_denied": "Permission denied",
  "system.disk_full": "Disk space full",
  "system.memory_limit": "Memory usage exceeds limit"
}
```

## 18. 技术架构深度优化

### 18.1 AI推理引擎核心实现

#### 18.1.1 多引擎支持架构

```rust
// AI推理引擎抽象层 (src-tauri/src/ai/engine/mod.rs)
use async_trait::async_trait;
use serde::{Serialize, Deserialize};
use std::collections::HashMap;
use tokio::sync::mpsc;

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct InferenceRequest {
    pub model_id: String,
    pub prompt: String,
    pub system_prompt: Option<String>,
    pub max_tokens: Option<u32>,
    pub temperature: Option<f32>,
    pub top_p: Option<f32>,
    pub stop_sequences: Option<Vec<String>>,
    pub stream: bool,
    pub context: Option<Vec<ChatMessage>>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct InferenceResponse {
    pub content: String,
    pub finish_reason: FinishReason,
    pub usage: TokenUsage,
    pub model_info: ModelInfo,
    pub response_time: f64,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum FinishReason {
    Stop,
    Length,
    ContentFilter,
    Error(String),
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct TokenUsage {
    pub prompt_tokens: u32,
    pub completion_tokens: u32,
    pub total_tokens: u32,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ModelInfo {
    pub name: String,
    pub version: String,
    pub architecture: String,
    pub context_length: u32,
    pub parameter_count: String,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct StreamToken {
    pub content: String,
    pub is_final: bool,
    pub token_id: Option<u32>,
    pub logprobs: Option<f32>,
}

// 推理引擎特征
#[async_trait]
pub trait InferenceEngine: Send + Sync {
    async fn load_model(&self, model_path: &str, config: &ModelConfig) -> Result<(), InferenceError>;
    async fn unload_model(&self, model_id: &str) -> Result<(), InferenceError>;
    async fn is_model_loaded(&self, model_id: &str) -> bool;
    async fn get_model_info(&self, model_id: &str) -> Result<ModelInfo, InferenceError>;

    async fn inference(&self, request: InferenceRequest) -> Result<InferenceResponse, InferenceError>;
    async fn inference_stream(&self, request: InferenceRequest) -> Result<mpsc::Receiver<StreamToken>, InferenceError>;

    async fn get_memory_usage(&self) -> Result<MemoryUsage, InferenceError>;
    async fn get_performance_stats(&self) -> Result<PerformanceStats, InferenceError>;

    fn engine_type(&self) -> EngineType;
    fn supported_architectures(&self) -> Vec<String>;
}

#[derive(Debug, Clone, Copy, PartialEq, Eq)]
pub enum EngineType {
    Candle,
    LlamaCpp,
    Onnx,
    TensorRT,
    OpenVino,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct MemoryUsage {
    pub total_allocated: u64,
    pub model_memory: u64,
    pub cache_memory: u64,
    pub gpu_memory: Option<u64>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PerformanceStats {
    pub tokens_per_second: f64,
    pub average_latency: f64,
    pub total_requests: u64,
    pub successful_requests: u64,
    pub failed_requests: u64,
}

// Candle引擎实现
pub struct CandleEngine {
    models: Arc<RwLock<HashMap<String, Arc<CandleModel>>>>,
    device: candle_core::Device,
    config: CandleConfig,
    stats: Arc<RwLock<PerformanceStats>>,
}

impl CandleEngine {
    pub fn new(config: CandleConfig) -> Result<Self, InferenceError> {
        let device = if config.use_gpu && candle_core::utils::cuda_is_available() {
            candle_core::Device::new_cuda(0)?
        } else {
            candle_core::Device::Cpu
        };

        Ok(Self {
            models: Arc::new(RwLock::new(HashMap::new())),
            device,
            config,
            stats: Arc::new(RwLock::new(PerformanceStats::default())),
        })
    }
}

#[async_trait]
impl InferenceEngine for CandleEngine {
    async fn load_model(&self, model_path: &str, config: &ModelConfig) -> Result<(), InferenceError> {
        let model = CandleModel::load(model_path, &self.device, config).await?;
        let mut models = self.models.write().await;
        models.insert(config.model_id.clone(), Arc::new(model));
        Ok(())
    }

    async fn unload_model(&self, model_id: &str) -> Result<(), InferenceError> {
        let mut models = self.models.write().await;
        models.remove(model_id);
        Ok(())
    }

    async fn is_model_loaded(&self, model_id: &str) -> bool {
        let models = self.models.read().await;
        models.contains_key(model_id)
    }

    async fn inference(&self, request: InferenceRequest) -> Result<InferenceResponse, InferenceError> {
        let start_time = std::time::Instant::now();

        let models = self.models.read().await;
        let model = models.get(&request.model_id)
            .ok_or_else(|| InferenceError::ModelNotFound(request.model_id.clone()))?;

        let response = model.generate(&request).await?;

        let elapsed = start_time.elapsed().as_secs_f64();

        // 更新统计信息
        let mut stats = self.stats.write().await;
        stats.total_requests += 1;
        stats.successful_requests += 1;
        stats.average_latency = (stats.average_latency * (stats.total_requests - 1) as f64 + elapsed) / stats.total_requests as f64;

        Ok(InferenceResponse {
            content: response.content,
            finish_reason: response.finish_reason,
            usage: response.usage,
            model_info: model.get_info(),
            response_time: elapsed,
        })
    }

    async fn inference_stream(&self, request: InferenceRequest) -> Result<mpsc::Receiver<StreamToken>, InferenceError> {
        let models = self.models.read().await;
        let model = models.get(&request.model_id)
            .ok_or_else(|| InferenceError::ModelNotFound(request.model_id.clone()))?;

        let (tx, rx) = mpsc::channel(100);
        let model_clone = model.clone();
        let request_clone = request.clone();

        tokio::spawn(async move {
            if let Err(e) = model_clone.generate_stream(&request_clone, tx).await {
                tracing::error!("Stream generation failed: {}", e);
            }
        });

        Ok(rx)
    }

    async fn get_memory_usage(&self) -> Result<MemoryUsage, InferenceError> {
        // 实现内存使用统计
        Ok(MemoryUsage {
            total_allocated: 0,
            model_memory: 0,
            cache_memory: 0,
            gpu_memory: None,
        })
    }

    async fn get_performance_stats(&self) -> Result<PerformanceStats, InferenceError> {
        let stats = self.stats.read().await;
        Ok(stats.clone())
    }

    fn engine_type(&self) -> EngineType {
        EngineType::Candle
    }

    fn supported_architectures(&self) -> Vec<String> {
        vec![
            "llama".to_string(),
            "mistral".to_string(),
            "qwen".to_string(),
            "phi".to_string(),
            "gemma".to_string(),
        ]
    }
}

// llama.cpp引擎实现
pub struct LlamaCppEngine {
    models: Arc<RwLock<HashMap<String, Arc<LlamaCppModel>>>>,
    config: LlamaCppConfig,
    stats: Arc<RwLock<PerformanceStats>>,
}

impl LlamaCppEngine {
    pub fn new(config: LlamaCppConfig) -> Self {
        Self {
            models: Arc::new(RwLock::new(HashMap::new())),
            config,
            stats: Arc::new(RwLock::new(PerformanceStats::default())),
        }
    }
}

#[async_trait]
impl InferenceEngine for LlamaCppEngine {
    async fn load_model(&self, model_path: &str, config: &ModelConfig) -> Result<(), InferenceError> {
        let model = LlamaCppModel::load(model_path, config).await?;
        let mut models = self.models.write().await;
        models.insert(config.model_id.clone(), Arc::new(model));
        Ok(())
    }

    async fn inference(&self, request: InferenceRequest) -> Result<InferenceResponse, InferenceError> {
        let start_time = std::time::Instant::now();

        let models = self.models.read().await;
        let model = models.get(&request.model_id)
            .ok_or_else(|| InferenceError::ModelNotFound(request.model_id.clone()))?;

        let response = model.generate(&request).await?;
        let elapsed = start_time.elapsed().as_secs_f64();

        Ok(InferenceResponse {
            content: response.content,
            finish_reason: response.finish_reason,
            usage: response.usage,
            model_info: model.get_info(),
            response_time: elapsed,
        })
    }

    // ... 其他方法实现

    fn engine_type(&self) -> EngineType {
        EngineType::LlamaCpp
    }

    fn supported_architectures(&self) -> Vec<String> {
        vec![
            "llama".to_string(),
            "mistral".to_string(),
            "qwen".to_string(),
            "baichuan".to_string(),
            "chatglm".to_string(),
        ]
    }
}

// 引擎管理器
pub struct EngineManager {
    engines: HashMap<EngineType, Box<dyn InferenceEngine>>,
    model_engine_mapping: Arc<RwLock<HashMap<String, EngineType>>>,
    default_engine: EngineType,
}

impl EngineManager {
    pub fn new() -> Self {
        let mut engines: HashMap<EngineType, Box<dyn InferenceEngine>> = HashMap::new();

        // 初始化各种引擎
        if let Ok(candle_engine) = CandleEngine::new(CandleConfig::default()) {
            engines.insert(EngineType::Candle, Box::new(candle_engine));
        }

        engines.insert(EngineType::LlamaCpp, Box::new(LlamaCppEngine::new(LlamaCppConfig::default())));

        Self {
            engines,
            model_engine_mapping: Arc::new(RwLock::new(HashMap::new())),
            default_engine: EngineType::Candle,
        }
    }

    pub async fn load_model(&self, model_path: &str, config: &ModelConfig) -> Result<(), InferenceError> {
        let engine_type = self.select_best_engine(config).await?;

        if let Some(engine) = self.engines.get(&engine_type) {
            engine.load_model(model_path, config).await?;

            let mut mapping = self.model_engine_mapping.write().await;
            mapping.insert(config.model_id.clone(), engine_type);

            Ok(())
        } else {
            Err(InferenceError::EngineNotAvailable(engine_type))
        }
    }

    pub async fn inference(&self, request: InferenceRequest) -> Result<InferenceResponse, InferenceError> {
        let mapping = self.model_engine_mapping.read().await;
        let engine_type = mapping.get(&request.model_id)
            .copied()
            .unwrap_or(self.default_engine);

        if let Some(engine) = self.engines.get(&engine_type) {
            engine.inference(request).await
        } else {
            Err(InferenceError::EngineNotAvailable(engine_type))
        }
    }

    async fn select_best_engine(&self, config: &ModelConfig) -> Result<EngineType, InferenceError> {
        // 根据模型架构和配置选择最佳引擎
        match config.architecture.as_str() {
            "llama" | "mistral" => {
                if self.engines.contains_key(&EngineType::LlamaCpp) {
                    Ok(EngineType::LlamaCpp)
                } else {
                    Ok(EngineType::Candle)
                }
            }
            "qwen" | "phi" => Ok(EngineType::Candle),
            _ => Ok(self.default_engine),
        }
    }
}

// 错误类型定义
#[derive(Debug, thiserror::Error)]
pub enum InferenceError {
    #[error("Model not found: {0}")]
    ModelNotFound(String),

    #[error("Engine not available: {0:?}")]
    EngineNotAvailable(EngineType),

    #[error("Model loading failed: {0}")]
    ModelLoadError(String),

    #[error("Inference failed: {0}")]
    InferenceFailed(String),

    #[error("Out of memory")]
    OutOfMemory,

    #[error("Invalid configuration: {0}")]
    InvalidConfig(String),

    #[error("IO error: {0}")]
    IoError(#[from] std::io::Error),

    #[error("Candle error: {0}")]
    CandleError(#[from] candle_core::Error),
}

// 配置结构
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ModelConfig {
    pub model_id: String,
    pub architecture: String,
    pub quantization: Option<String>,
    pub context_length: u32,
    pub gpu_layers: Option<u32>,
    pub batch_size: Option<u32>,
    pub threads: Option<u32>,
    pub use_mmap: bool,
    pub use_mlock: bool,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CandleConfig {
    pub use_gpu: bool,
    pub gpu_device_id: u32,
    pub dtype: String,
    pub use_flash_attention: bool,
}

impl Default for CandleConfig {
    fn default() -> Self {
        Self {
            use_gpu: true,
            gpu_device_id: 0,
            dtype: "f16".to_string(),
            use_flash_attention: true,
        }
    }
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct LlamaCppConfig {
    pub n_ctx: u32,
    pub n_batch: u32,
    pub n_threads: Option<u32>,
    pub n_gpu_layers: Option<u32>,
    pub use_mmap: bool,
    pub use_mlock: bool,
    pub numa: bool,
}

impl Default for LlamaCppConfig {
    fn default() -> Self {
        Self {
            n_ctx: 4096,
            n_batch: 512,
            n_threads: None,
            n_gpu_layers: None,
            use_mmap: true,
            use_mlock: false,
            numa: false,
        }
    }
}
```

## 19. 项目总结

### 19.1 技术亮点总结

**创新技术应用**
- **多引擎AI推理架构**：集成Candle、llama.cpp等多种推理引擎，支持不同模型架构的最优化运行
- **混合数据库设计**：SQLite主数据库 + ChromaDB向量数据库，实现结构化数据和语义搜索的完美结合
- **跨平台桌面应用**：基于Tauri框架，真正实现一套代码多平台运行，性能接近原生应用
- **完全离线运行**：无需依赖云端服务，保护用户数据隐私和安全
- **P2P网络共享**：基于mDNS的设备发现和安全的点对点通信，实现局域网资源共享
- **WASM插件系统**：安全的沙箱环境，支持第三方扩展开发

**性能优化特色**
- **智能内存管理**：动态模型加载卸载，内存池管理，GPU资源优化
- **并发处理架构**：基于Tokio的异步处理，支持高并发请求
- **多层缓存策略**：查询缓存、响应缓存、文件缓存，全方位提升性能
- **流式处理**：实时流式输出，提升用户体验
- **批量优化**：数据库批量操作，推理批处理，网络批量传输

**安全设计亮点**
- **端到端加密**：敏感数据加密存储，P2P通信加密传输
- **文件安全检查**：文件类型验证、恶意软件扫描、安全存储
- **权限控制**：细粒度权限管理，插件沙箱隔离
- **数据完整性**：哈希验证、数字签名、完整性检查

### 19.2 核心优势分析

**技术优势**
1. **架构先进性**：采用现代化的技术栈，模块化设计，易于扩展和维护
2. **性能卓越**：Rust后端保证高性能，Vue3前端提供流畅体验
3. **兼容性强**：支持多种AI模型格式，多种文档类型，多种操作系统
4. **可扩展性**：插件系统支持功能扩展，API接口支持第三方集成
5. **稳定可靠**：完善的错误处理，自动恢复机制，数据备份策略

**业务优势**
1. **数据安全**：完全本地化部署，用户数据不出本地，符合隐私保护要求
2. **成本效益**：一次部署长期使用，无需持续付费，降低使用成本
3. **定制化强**：支持企业定制开发，满足特定业务需求
4. **协作便利**：局域网共享功能，支持团队协作和资源共享
5. **离线可用**：无需网络连接即可使用，适合各种网络环境

**用户体验优势**
1. **界面友好**：现代化UI设计，支持主题切换，多语言支持
2. **操作简单**：直观的操作流程，丰富的快捷键支持
3. **响应迅速**：本地推理无网络延迟，流式输出实时反馈
4. **功能丰富**：聊天、知识库、多模态处理等多种功能集成
5. **个性化**：支持个人偏好设置，自定义工作流程

### 19.3 应用场景展望

**企业级应用**
- **内部知识管理**：构建企业专属知识库，提供智能问答服务
- **代码开发辅助**：集成开发环境，提供代码生成和技术咨询
- **文档智能处理**：批量文档分析、内容提取、智能分类
- **客服系统**：基于企业知识库的智能客服机器人
- **培训教育**：企业培训材料智能问答，员工学习辅助

**个人用户应用**
- **学习助手**：个人知识库管理，学习资料智能整理
- **写作助手**：文章写作辅助，内容创作支持
- **研究工具**：学术研究资料管理，文献分析工具
- **生活助手**：日常问题咨询，信息查询整理
- **创意工具**：创意写作、头脑风暴、想法记录

**专业领域应用**
- **法律服务**：法律文档检索，案例分析，法规查询
- **医疗辅助**：医学文献查询，诊断辅助，病例分析
- **金融分析**：财务报告分析，投资研究，风险评估
- **教育培训**：智能教学助手，个性化学习方案
- **科研支持**：科研文献管理，实验数据分析

### 19.4 发展路线图

**短期目标 (3-6个月)**
- 完成核心功能开发和系统集成测试
- 发布第一个稳定版本 (v1.0)
- 建立用户社区和反馈收集机制
- 优化性能和用户体验
- 完善文档和教程

**中期目标 (6-12个月)**
- 扩展支持更多AI模型和格式
- 增强多模态处理能力 (图像、音频、视频)
- 完善插件生态系统和开发者工具
- 支持更多操作系统和硬件平台
- 集成更多第三方服务和API

**长期目标 (1-2年)**
- 构建完整的AI工具生态系统
- 支持分布式部署和集群管理
- 集成更多AI能力 (图像生成、语音合成等)
- 建立商业化运营模式
- 拓展国际市场和多语言支持

### 19.5 风险评估与应对

**技术风险**
- **模型兼容性风险**：新模型格式适配挑战
  - 应对：建立模型适配框架，持续跟进技术发展
- **性能瓶颈风险**：大模型推理性能限制
  - 应对：多引擎优化，硬件加速，算法优化
- **安全漏洞风险**：插件系统安全隐患
  - 应对：严格安全审查，沙箱隔离，权限控制

**市场风险**
- **竞争加剧风险**：AI工具市场竞争激烈
  - 应对：持续技术创新，差异化定位，用户体验优化
- **技术变革风险**：AI技术快速发展变化
  - 应对：保持技术敏感度，快速迭代适应
- **用户接受度风险**：本地AI工具接受程度
  - 应对：加强用户教育，提供优质体验，建立信任

**运营风险**
- **资源投入风险**：开发和维护成本高
  - 应对：合理规划资源，分阶段实施，寻求合作
- **人才短缺风险**：AI和系统开发人才稀缺
  - 应对：建立人才培养体系，提供有竞争力的待遇
- **法规变化风险**：AI相关法规政策变化
  - 应对：密切关注政策动向，确保合规运营

### 19.6 成功关键因素

**技术因素**
1. **持续创新**：保持技术领先，快速响应市场需求
2. **质量保证**：严格的测试流程，稳定可靠的产品
3. **性能优化**：持续优化性能，提供流畅体验
4. **安全保障**：确保数据安全，建立用户信任

**产品因素**
1. **用户体验**：简洁易用的界面，直观的操作流程
2. **功能完整**：满足用户核心需求，提供完整解决方案
3. **个性化**：支持用户定制，满足不同使用场景
4. **生态建设**：建立插件生态，扩展产品能力

**市场因素**
1. **精准定位**：明确目标用户群体，提供针对性解决方案
2. **品牌建设**：建立品牌知名度，提升市场影响力
3. **渠道拓展**：多渠道推广，扩大用户覆盖
4. **合作伙伴**：建立合作伙伴关系，共同发展

**组织因素**
1. **团队建设**：组建高效团队，提升执行能力
2. **文化塑造**：建立创新文化，激发团队活力
3. **流程优化**：建立高效流程，提升工作效率
4. **学习成长**：持续学习提升，适应技术发展

---

**文档版本**：v2.0
**最后更新**：2024年12月
**文档状态**：完整版
**维护团队**：AI Studio开发团队
**文档规模**：约50,000字，涵盖19个主要章节

> 本文档详细描述了AI Studio项目的完整技术方案，包括架构设计、功能模块、接口定义、代码实现、数据库设计、API文档、性能优化、安全设计、测试策略、监控日志、国际化支持、部署运维等各个方面。文档将随着项目开发进展持续更新和完善，为项目的成功实施提供全面的技术指导。

#### 16.1.1 性能监控

```rust
// 性能监控服务
use std::sync::Arc;
use std::time::{Duration, Instant};
use tokio::sync::RwLock;
use serde::{Serialize, Deserialize};

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PerformanceMetrics {
    pub timestamp: chrono::DateTime<chrono::Utc>,
    pub cpu_usage: f64,
    pub memory_usage: u64,
    pub memory_total: u64,
    pub gpu_usage: Option<f64>,
    pub gpu_memory_usage: Option<u64>,
    pub disk_usage: u64,
    pub disk_total: u64,
    pub network_io: NetworkIO,
    pub active_connections: u32,
    pub inference_queue_size: u32,
    pub response_times: ResponseTimeMetrics,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct NetworkIO {
    pub bytes_sent: u64,
    pub bytes_received: u64,
    pub packets_sent: u64,
    pub packets_received: u64,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ResponseTimeMetrics {
    pub avg_response_time: f64,
    pub p50_response_time: f64,
    pub p95_response_time: f64,
    pub p99_response_time: f64,
    pub max_response_time: f64,
}

pub struct PerformanceMonitor {
    metrics_history: Arc<RwLock<Vec<PerformanceMetrics>>>,
    response_times: Arc<RwLock<Vec<f64>>>,
    max_history_size: usize,
    collection_interval: Duration,
}

impl PerformanceMonitor {
    pub fn new(max_history_size: usize, collection_interval: Duration) -> Self {
        Self {
            metrics_history: Arc::new(RwLock::new(Vec::new())),
            response_times: Arc::new(RwLock::new(Vec::new())),
            max_history_size,
            collection_interval,
        }
    }

    pub async fn start_monitoring(&self) {
        let metrics_history = self.metrics_history.clone();
        let response_times = self.response_times.clone();
        let max_history_size = self.max_history_size;
        let interval = self.collection_interval;

        tokio::spawn(async move {
            let mut interval_timer = tokio::time::interval(interval);

            loop {
                interval_timer.tick().await;

                match Self::collect_system_metrics().await {
                    Ok(mut metrics) => {
                        // Calculate response time metrics
                        let response_time_metrics = {
                            let times = response_times.read().await;
                            Self::calculate_response_time_metrics(&times)
                        };

                        metrics.response_times = response_time_metrics;

                        // Store metrics
                        let mut history = metrics_history.write().await;
                        history.push(metrics);

                        // Limit history size
                        if history.len() > max_history_size {
                            history.remove(0);
                        }

                        // Clear old response times
                        let mut times = response_times.write().await;
                        times.clear();
                    }
                    Err(e) => {
                        tracing::error!("Failed to collect system metrics: {}", e);
                    }
                }
            }
        });
    }

    async fn collect_system_metrics() -> Result<PerformanceMetrics, Box<dyn std::error::Error>> {
        let timestamp = chrono::Utc::now();

        // CPU usage
        let cpu_usage = Self::get_cpu_usage().await?;

        // Memory usage
        let (memory_usage, memory_total) = Self::get_memory_usage().await?;

        // GPU usage (if available)
        let (gpu_usage, gpu_memory_usage) = Self::get_gpu_usage().await?;

        // Disk usage
        let (disk_usage, disk_total) = Self::get_disk_usage().await?;

        // Network I/O
        let network_io = Self::get_network_io().await?;

        // Application-specific metrics
        let active_connections = Self::get_active_connections().await?;
        let inference_queue_size = Self::get_inference_queue_size().await?;

        Ok(PerformanceMetrics {
            timestamp,
            cpu_usage,
            memory_usage,
            memory_total,
            gpu_usage,
            gpu_memory_usage,
            disk_usage,
            disk_total,
            network_io,
            active_connections,
            inference_queue_size,
            response_times: ResponseTimeMetrics {
                avg_response_time: 0.0,
                p50_response_time: 0.0,
                p95_response_time: 0.0,
                p99_response_time: 0.0,
                max_response_time: 0.0,
            },
        })
    }

    async fn get_cpu_usage() -> Result<f64, Box<dyn std::error::Error>> {
        // 使用系统API获取CPU使用率
        #[cfg(target_os = "windows")]
        {
            // Windows实现
            Ok(0.0) // 简化实现
        }

        #[cfg(target_os = "macos")]
        {
            // macOS实现
            Ok(0.0) // 简化实现
        }

        #[cfg(target_os = "linux")]
        {
            // Linux实现：读取/proc/stat
            let stat_content = tokio::fs::read_to_string("/proc/stat").await?;
            let cpu_line = stat_content.lines().next().unwrap_or("");

            // 解析CPU时间
            let values: Vec<u64> = cpu_line
                .split_whitespace()
                .skip(1)
                .take(4)
                .map(|s| s.parse().unwrap_or(0))
                .collect();

            if values.len() >= 4 {
                let idle = values[3];
                let total: u64 = values.iter().sum();
                let usage = if total > 0 {
                    100.0 * (1.0 - idle as f64 / total as f64)
                } else {
                    0.0
                };
                Ok(usage)
            } else {
                Ok(0.0)
            }
        }
    }

    async fn get_memory_usage() -> Result<(u64, u64), Box<dyn std::error::Error>> {
        #[cfg(target_os = "linux")]
        {
            let meminfo = tokio::fs::read_to_string("/proc/meminfo").await?;
            let mut total = 0u64;
            let mut available = 0u64;

            for line in meminfo.lines() {
                if line.starts_with("MemTotal:") {
                    total = line.split_whitespace().nth(1)
                        .and_then(|s| s.parse().ok())
                        .unwrap_or(0) * 1024; // Convert KB to bytes
                } else if line.starts_with("MemAvailable:") {
                    available = line.split_whitespace().nth(1)
                        .and_then(|s| s.parse().ok())
                        .unwrap_or(0) * 1024;
                }
            }

            let used = total - available;
            Ok((used, total))
        }

        #[cfg(not(target_os = "linux"))]
        {
            // 其他平台的简化实现
            Ok((0, 0))
        }
    }

    async fn get_gpu_usage() -> Result<(Option<f64>, Option<u64>), Box<dyn std::error::Error>> {
        // 尝试获取NVIDIA GPU信息
        match tokio::process::Command::new("nvidia-smi")
            .args(&["--query-gpu=utilization.gpu,memory.used", "--format=csv,noheader,nounits"])
            .output()
            .await
        {
            Ok(output) if output.status.success() => {
                let output_str = String::from_utf8_lossy(&output.stdout);
                let parts: Vec<&str> = output_str.trim().split(',').collect();

                if parts.len() >= 2 {
                    let gpu_usage = parts[0].trim().parse::<f64>().ok();
                    let memory_usage = parts[1].trim().parse::<u64>().map(|mb| mb * 1024 * 1024);
                    return Ok((gpu_usage, memory_usage));
                }
            }
            _ => {}
        }

        Ok((None, None))
    }

    async fn get_disk_usage() -> Result<(u64, u64), Box<dyn std::error::Error>> {
        // 获取当前目录的磁盘使用情况
        let current_dir = std::env::current_dir()?;

        #[cfg(unix)]
        {
            use std::os::unix::fs::MetadataExt;
            let metadata = tokio::fs::metadata(&current_dir).await?;

            // 这里需要使用statvfs系统调用获取磁盘信息
            // 简化实现
            Ok((0, 0))
        }

        #[cfg(windows)]
        {
            // Windows实现
            Ok((0, 0))
        }
    }

    async fn get_network_io() -> Result<NetworkIO, Box<dyn std::error::Error>> {
        #[cfg(target_os = "linux")]
        {
            let net_dev = tokio::fs::read_to_string("/proc/net/dev").await?;
            let mut total_rx_bytes = 0u64;
            let mut total_tx_bytes = 0u64;
            let mut total_rx_packets = 0u64;
            let mut total_tx_packets = 0u64;

            for line in net_dev.lines().skip(2) {
                let parts: Vec<&str> = line.split_whitespace().collect();
                if parts.len() >= 10 {
                    total_rx_bytes += parts[1].parse::<u64>().unwrap_or(0);
                    total_rx_packets += parts[2].parse::<u64>().unwrap_or(0);
                    total_tx_bytes += parts[9].parse::<u64>().unwrap_or(0);
                    total_tx_packets += parts[10].parse::<u64>().unwrap_or(0);
                }
            }

            Ok(NetworkIO {
                bytes_sent: total_tx_bytes,
                bytes_received: total_rx_bytes,
                packets_sent: total_tx_packets,
                packets_received: total_rx_packets,
            })
        }

        #[cfg(not(target_os = "linux"))]
        {
            Ok(NetworkIO {
                bytes_sent: 0,
                bytes_received: 0,
                packets_sent: 0,
                packets_received: 0,
            })
        }
    }

    async fn get_active_connections() -> Result<u32, Box<dyn std::error::Error>> {
        // 获取活跃连接数
        // 这里需要与应用的连接管理器集成
        Ok(0)
    }

    async fn get_inference_queue_size() -> Result<u32, Box<dyn std::error::Error>> {
        // 获取推理队列大小
        // 这里需要与AI推理引擎集成
        Ok(0)
    }

    fn calculate_response_time_metrics(times: &[f64]) -> ResponseTimeMetrics {
        if times.is_empty() {
            return ResponseTimeMetrics {
                avg_response_time: 0.0,
                p50_response_time: 0.0,
                p95_response_time: 0.0,
                p99_response_time: 0.0,
                max_response_time: 0.0,
            };
        }

        let mut sorted_times = times.to_vec();
        sorted_times.sort_by(|a, b| a.partial_cmp(b).unwrap());

        let avg = times.iter().sum::<f64>() / times.len() as f64;
        let p50 = Self::percentile(&sorted_times, 0.5);
        let p95 = Self::percentile(&sorted_times, 0.95);
        let p99 = Self::percentile(&sorted_times, 0.99);
        let max = sorted_times.last().copied().unwrap_or(0.0);

        ResponseTimeMetrics {
            avg_response_time: avg,
            p50_response_time: p50,
            p95_response_time: p95,
            p99_response_time: p99,
            max_response_time: max,
        }
    }

    fn percentile(sorted_data: &[f64], percentile: f64) -> f64 {
        if sorted_data.is_empty() {
            return 0.0;
        }

        let index = (percentile * (sorted_data.len() - 1) as f64).round() as usize;
        sorted_data[index.min(sorted_data.len() - 1)]
    }

    pub async fn record_response_time(&self, duration: Duration) {
        let mut times = self.response_times.write().await;
        times.push(duration.as_secs_f64());

        // 限制响应时间记录数量
        if times.len() > 10000 {
            times.drain(0..5000);
        }
    }

    pub async fn get_current_metrics(&self) -> Option<PerformanceMetrics> {
        let history = self.metrics_history.read().await;
        history.last().cloned()
    }

    pub async fn get_metrics_history(&self, duration: Duration) -> Vec<PerformanceMetrics> {
        let history = self.metrics_history.read().await;
        let cutoff_time = chrono::Utc::now() - chrono::Duration::from_std(duration).unwrap();

        history.iter()
            .filter(|m| m.timestamp > cutoff_time)
            .cloned()
            .collect()
    }
}

// 响应时间中间件
pub struct ResponseTimeMiddleware {
    monitor: Arc<PerformanceMonitor>,
}

impl ResponseTimeMiddleware {
    pub fn new(monitor: Arc<PerformanceMonitor>) -> Self {
        Self { monitor }
    }

    pub async fn measure<F, T>(&self, operation: F) -> T
    where
        F: std::future::Future<Output = T>,
    {
        let start = Instant::now();
        let result = operation.await;
        let duration = start.elapsed();

        self.monitor.record_response_time(duration).await;

        result
    }
}
```

## 11. 安全与隐私设计

### 11.1 数据安全

**本地数据加密**
```rust
use aes_gcm::{Aes256Gcm, Key, Nonce};
use aes_gcm::aead::{Aead, NewAead};

pub struct DataEncryption {
    cipher: Aes256Gcm,
    key: Key<Aes256Gcm>,
}

impl DataEncryption {
    pub fn new() -> Result<Self, CryptoError> {
        let key = Self::derive_key_from_device()?;
        let cipher = Aes256Gcm::new(&key);

        Ok(Self { cipher, key })
    }

    pub fn encrypt(&self, data: &[u8]) -> Result<Vec<u8>, CryptoError> {
        let nonce = Nonce::from_slice(&self.generate_nonce());
        let ciphertext = self.cipher.encrypt(nonce, data)
            .map_err(|_| CryptoError::EncryptionFailed)?;

        // 组合nonce和密文
        let mut result = nonce.to_vec();
        result.extend_from_slice(&ciphertext);

        Ok(result)
    }

    pub fn decrypt(&self, encrypted_data: &[u8]) -> Result<Vec<u8>, CryptoError> {
        if encrypted_data.len() < 12 {
            return Err(CryptoError::InvalidData);
        }

        let (nonce_bytes, ciphertext) = encrypted_data.split_at(12);
        let nonce = Nonce::from_slice(nonce_bytes);

        self.cipher.decrypt(nonce, ciphertext)
            .map_err(|_| CryptoError::DecryptionFailed)
    }

    fn derive_key_from_device() -> Result<Key<Aes256Gcm>, CryptoError> {
        // 基于设备特征生成密钥
        let device_info = get_device_fingerprint()?;
        let mut hasher = Sha256::new();
        hasher.update(device_info.as_bytes());
        hasher.update(b"ai_studio_encryption_salt");

        let hash = hasher.finalize();
        Ok(*Key::<Aes256Gcm>::from_slice(&hash))
    }

    fn generate_nonce(&self) -> [u8; 12] {
        let mut nonce = [0u8; 12];
        getrandom::getrandom(&mut nonce).unwrap();
        nonce
    }
}

// 敏感数据存储
pub struct SecureStorage {
    encryption: DataEncryption,
    storage_path: PathBuf,
}

impl SecureStorage {
    pub async fn store_sensitive_data(&self, key: &str, data: &[u8]) -> Result<()> {
        let encrypted_data = self.encryption.encrypt(data)?;
        let file_path = self.storage_path.join(format!("{}.enc", key));

        tokio::fs::write(file_path, encrypted_data).await?;

        // 设置严格的文件权限
        #[cfg(unix)]
        {
            use std::os::unix::fs::PermissionsExt;
            let mut perms = tokio::fs::metadata(&file_path).await?.permissions();
            perms.set_mode(0o600); // 仅所有者可读写
            tokio::fs::set_permissions(&file_path, perms).await?;
        }

        Ok(())
    }

    pub async fn retrieve_sensitive_data(&self, key: &str) -> Result<Vec<u8>> {
        let file_path = self.storage_path.join(format!("{}.enc", key));
        let encrypted_data = tokio::fs::read(file_path).await?;

        self.encryption.decrypt(&encrypted_data)
    }
}
```

**传输加密**
```rust
use rustls::{ClientConfig, ServerConfig};
use tokio_rustls::{TlsAcceptor, TlsConnector};

pub struct SecureConnection {
    tls_config: ClientConfig,
}

impl SecureConnection {
    pub fn new() -> Result<Self> {
        let mut config = ClientConfig::builder()
            .with_safe_defaults()
            .with_root_certificates(load_root_certificates()?)
            .with_no_client_auth();

        // 强制使用TLS 1.3
        config.versions = vec![&rustls::version::TLS13];

        Ok(Self { tls_config: config })
    }

    pub async fn connect_secure(&self, addr: &str) -> Result<TlsStream<TcpStream>> {
        let connector = TlsConnector::from(Arc::new(self.tls_config.clone()));
        let stream = TcpStream::connect(addr).await?;

        let domain = ServerName::try_from(addr.split(':').next().unwrap())?;
        let tls_stream = connector.connect(domain, stream).await?;

        Ok(tls_stream)
    }
}
```

### 11.2 隐私保护

**数据本地化处理**
```rust
pub struct PrivacyManager {
    local_processing_only: bool,
    data_anonymizer: DataAnonymizer,
    consent_manager: ConsentManager,
}

impl PrivacyManager {
    pub fn new(config: PrivacyConfig) -> Self {
        Self {
            local_processing_only: config.local_processing_only,
            data_anonymizer: DataAnonymizer::new(),
            consent_manager: ConsentManager::new(),
        }
    }

    pub async fn process_user_data(&self, data: UserData) -> Result<ProcessedData> {
        // 检查用户同意
        if !self.consent_manager.has_consent(&data.user_id, &data.data_type) {
            return Err(PrivacyError::NoConsent);
        }

        // 强制本地处理
        if self.local_processing_only {
            return self.process_locally(data).await;
        }

        // 数据匿名化处理
        let anonymized_data = self.data_anonymizer.anonymize(data)?;

        Ok(ProcessedData {
            data: anonymized_data,
            processing_location: ProcessingLocation::Local,
            privacy_level: PrivacyLevel::High,
        })
    }

    async fn process_locally(&self, data: UserData) -> Result<ProcessedData> {
        // 确保所有处理都在本地完成
        let processed = LocalProcessor::process(data).await?;

        Ok(ProcessedData {
            data: processed,
            processing_location: ProcessingLocation::Local,
            privacy_level: PrivacyLevel::Maximum,
        })
    }
}

// 数据匿名化
pub struct DataAnonymizer {
    pii_detector: PIIDetector,
}

impl DataAnonymizer {
    pub fn anonymize(&self, mut data: UserData) -> Result<UserData> {
        // 检测个人身份信息
        let pii_items = self.pii_detector.detect(&data.content)?;

        // 移除或替换PII
        for pii in pii_items {
            match pii.pii_type {
                PIIType::Email => {
                    data.content = data.content.replace(&pii.value, "[EMAIL_REDACTED]");
                }
                PIIType::PhoneNumber => {
                    data.content = data.content.replace(&pii.value, "[PHONE_REDACTED]");
                }
                PIIType::Name => {
                    data.content = data.content.replace(&pii.value, "[NAME_REDACTED]");
                }
                PIIType::Address => {
                    data.content = data.content.replace(&pii.value, "[ADDRESS_REDACTED]");
                }
                _ => {}
            }
        }

        // 移除元数据中的身份信息
        data.metadata.remove("user_id");
        data.metadata.remove("device_id");
        data.metadata.remove("ip_address");

        Ok(data)
    }
}
```

### 11.3 权限控制

**细粒度权限管理**
```rust
pub struct PermissionManager {
    permissions: HashMap<String, UserPermissions>,
    role_definitions: HashMap<String, Role>,
}

#[derive(Debug, Clone)]
pub struct UserPermissions {
    user_id: String,
    roles: Vec<String>,
    specific_permissions: HashSet<Permission>,
    restrictions: Vec<Restriction>,
}

#[derive(Debug, Clone, Hash, Eq, PartialEq)]
pub enum Permission {
    // 数据权限
    ReadChatHistory,
    WriteChatHistory,
    DeleteChatHistory,
    ExportChatHistory,

    // 模型权限
    LoadModel,
    UnloadModel,
    DownloadModel,
    DeleteModel,

    // 知识库权限
    CreateKnowledgeBase,
    ReadKnowledgeBase,
    UpdateKnowledgeBase,
    DeleteKnowledgeBase,
    ShareKnowledgeBase,

    // 网络权限
    NetworkAccess,
    P2PConnection,
    ResourceSharing,

    // 系统权限
    SystemConfiguration,
    PerformanceMonitoring,
    LogAccess,

    // 插件权限
    InstallPlugin,
    ExecutePlugin,
    ConfigurePlugin,
}

impl PermissionManager {
    pub fn check_permission(&self, user_id: &str, permission: Permission) -> bool {
        let user_perms = match self.permissions.get(user_id) {
            Some(perms) => perms,
            None => return false,
        };

        // 检查直接权限
        if user_perms.specific_permissions.contains(&permission) {
            return true;
        }

        // 检查角色权限
        for role_name in &user_perms.roles {
            if let Some(role) = self.role_definitions.get(role_name) {
                if role.permissions.contains(&permission) {
                    return true;
                }
            }
        }

        false
    }

    pub fn check_restrictions(&self, user_id: &str, action: &Action) -> Result<()> {
        let user_perms = match self.permissions.get(user_id) {
            Some(perms) => perms,
            None => return Err(PermissionError::UserNotFound),
        };

        for restriction in &user_perms.restrictions {
            if restriction.applies_to(action) {
                return Err(PermissionError::ActionRestricted(restriction.reason.clone()));
            }
        }

        Ok(())
    }
}
```

## 11. 安全与隐私设计

### 11.1 数据安全

**本地数据加密**
```rust
use aes_gcm::{Aes256Gcm, Key, Nonce};
use aes_gcm::aead::{Aead, NewAead};

pub struct DataEncryption {
    cipher: Aes256Gcm,
    key: Key<Aes256Gcm>,
}

impl DataEncryption {
    pub fn new() -> Result<Self, CryptoError> {
        let key = Self::derive_key_from_device()?;
        let cipher = Aes256Gcm::new(&key);

        Ok(Self { cipher, key })
    }

    pub fn encrypt(&self, data: &[u8]) -> Result<Vec<u8>, CryptoError> {
        let nonce = Nonce::from_slice(&self.generate_nonce());
        let ciphertext = self.cipher.encrypt(nonce, data)
            .map_err(|_| CryptoError::EncryptionFailed)?;

        // 组合nonce和密文
        let mut result = nonce.to_vec();
        result.extend_from_slice(&ciphertext);

        Ok(result)
    }

    pub fn decrypt(&self, encrypted_data: &[u8]) -> Result<Vec<u8>, CryptoError> {
        if encrypted_data.len() < 12 {
            return Err(CryptoError::InvalidData);
        }

        let (nonce_bytes, ciphertext) = encrypted_data.split_at(12);
        let nonce = Nonce::from_slice(nonce_bytes);

        self.cipher.decrypt(nonce, ciphertext)
            .map_err(|_| CryptoError::DecryptionFailed)
    }

    fn derive_key_from_device() -> Result<Key<Aes256Gcm>, CryptoError> {
        // 基于设备特征生成密钥
        let device_info = get_device_fingerprint()?;
        let mut hasher = Sha256::new();
        hasher.update(device_info.as_bytes());
        hasher.update(b"ai_studio_encryption_salt");

        let hash = hasher.finalize();
        Ok(*Key::<Aes256Gcm>::from_slice(&hash))
    }

    fn generate_nonce(&self) -> [u8; 12] {
        let mut nonce = [0u8; 12];
        getrandom::getrandom(&mut nonce).unwrap();
        nonce
    }
}
```

**传输加密**
```rust
use rustls::{ClientConfig, ServerConfig};
use tokio_rustls::{TlsAcceptor, TlsConnector};

pub struct SecureConnection {
    tls_config: ClientConfig,
}

impl SecureConnection {
    pub fn new() -> Result<Self> {
        let mut config = ClientConfig::builder()
            .with_safe_defaults()
            .with_root_certificates(load_root_certificates()?)
            .with_no_client_auth();

        // 强制使用TLS 1.3
        config.versions = vec![&rustls::version::TLS13];

        Ok(Self { tls_config: config })
    }

    pub async fn connect_secure(&self, addr: &str) -> Result<TlsStream<TcpStream>> {
        let connector = TlsConnector::from(Arc::new(self.tls_config.clone()));
        let stream = TcpStream::connect(addr).await?;

        let domain = ServerName::try_from(addr.split(':').next().unwrap())?;
        let tls_stream = connector.connect(domain, stream).await?;

        Ok(tls_stream)
    }
}
```

### 11.2 隐私保护

**数据本地化处理**
```rust
pub struct PrivacyManager {
    local_processing_only: bool,
    data_anonymizer: DataAnonymizer,
    consent_manager: ConsentManager,
}

impl PrivacyManager {
    pub async fn process_user_data(&self, data: UserData) -> Result<ProcessedData> {
        // 检查用户同意
        if !self.consent_manager.has_consent(&data.user_id, &data.data_type) {
            return Err(PrivacyError::NoConsent);
        }

        // 强制本地处理
        if self.local_processing_only {
            return self.process_locally(data).await;
        }

        // 数据匿名化处理
        let anonymized_data = self.data_anonymizer.anonymize(data)?;

        Ok(ProcessedData {
            data: anonymized_data,
            processing_location: ProcessingLocation::Local,
            privacy_level: PrivacyLevel::High,
        })
    }
}
```

### 11.3 安全审计

**操作日志系统**
```rust
pub struct SecurityAuditLogger {
    log_file: Arc<Mutex<File>>,
    encryption: DataEncryption,
}

impl SecurityAuditLogger {
    pub async fn log_security_event(&self, event: SecurityEvent) -> Result<()> {
        let log_entry = AuditLogEntry {
            timestamp: SystemTime::now(),
            event_type: event.event_type,
            user_id: event.user_id,
            resource: event.resource,
            action: event.action,
            result: event.result,
            ip_address: event.ip_address,
            user_agent: event.user_agent,
            risk_level: event.risk_level,
        };

        let serialized = serde_json::to_string(&log_entry)?;
        let encrypted = self.encryption.encrypt(serialized.as_bytes())?;

        let mut file = self.log_file.lock().await;
        file.write_all(&encrypted).await?;
        file.write_all(b"\n").await?;
        file.flush().await?;

        Ok(())
    }
}
```

### 10.4 数据库性能优化

**索引策略**
```sql
-- 聊天相关索引
CREATE INDEX idx_chat_messages_session_id ON chat_messages(session_id);
CREATE INDEX idx_chat_messages_created_at ON chat_messages(created_at);
CREATE INDEX idx_chat_messages_role ON chat_messages(role);
CREATE INDEX idx_chat_sessions_updated_at ON chat_sessions(updated_at);
CREATE INDEX idx_chat_sessions_is_archived ON chat_sessions(is_archived);

-- 知识库相关索引
CREATE INDEX idx_documents_kb_id ON documents(kb_id);
CREATE INDEX idx_documents_status ON documents(status);
CREATE INDEX idx_documents_created_at ON documents(created_at);
CREATE INDEX idx_document_chunks_document_id ON document_chunks(document_id);
CREATE INDEX idx_knowledge_bases_status ON knowledge_bases(status);

-- 模型相关索引
CREATE INDEX idx_models_status ON models(status);
CREATE INDEX idx_models_is_local ON models(is_local);
CREATE INDEX idx_download_tasks_status ON download_tasks(status);
CREATE INDEX idx_download_tasks_model_id ON download_tasks(model_id);

-- 网络相关索引
CREATE INDEX idx_network_nodes_status ON network_nodes(status);
CREATE INDEX idx_shared_resources_node_id ON shared_resources(node_id);
CREATE INDEX idx_transfer_tasks_status ON transfer_tasks(status);

-- 插件相关索引
CREATE INDEX idx_plugins_status ON plugins(status);
CREATE INDEX idx_plugin_configs_plugin_id ON plugin_configs(plugin_id);
CREATE INDEX idx_plugin_logs_plugin_id ON plugin_logs(plugin_id);
CREATE INDEX idx_plugin_logs_level ON plugin_logs(level);

-- 系统相关索引
CREATE INDEX idx_system_logs_level_module ON system_logs(level, module);
CREATE INDEX idx_system_logs_created_at ON system_logs(created_at);
CREATE INDEX idx_performance_metrics_name_time ON performance_metrics(metric_name, recorded_at);
CREATE INDEX idx_backup_records_created_at ON backup_records(created_at);

-- 复合索引
CREATE INDEX idx_chat_messages_session_created ON chat_messages(session_id, created_at);
CREATE INDEX idx_documents_kb_status ON documents(kb_id, status);
CREATE INDEX idx_system_logs_level_time ON system_logs(level, created_at);
```

**数据库维护策略**
```sql
-- 定期清理过期数据
CREATE TRIGGER cleanup_old_logs
AFTER INSERT ON system_logs
WHEN (SELECT COUNT(*) FROM system_logs) > 100000
BEGIN
    DELETE FROM system_logs
    WHERE created_at < datetime('now', '-30 days')
    AND level IN ('debug', 'info');
END;

-- 清理过期的性能指标
CREATE TRIGGER cleanup_old_metrics
AFTER INSERT ON performance_metrics
WHEN (SELECT COUNT(*) FROM performance_metrics) > 50000
BEGIN
    DELETE FROM performance_metrics
    WHERE recorded_at < datetime('now', '-7 days');
END;

-- 清理过期的插件日志
CREATE TRIGGER cleanup_plugin_logs
AFTER INSERT ON plugin_logs
WHEN (SELECT COUNT(*) FROM plugin_logs) > 10000
BEGIN
    DELETE FROM plugin_logs
    WHERE created_at < datetime('now', '-14 days')
    AND level IN ('debug', 'info');
END;

-- 数据库优化设置
PRAGMA journal_mode = WAL;
PRAGMA synchronous = NORMAL;
PRAGMA cache_size = 10000;
PRAGMA temp_store = MEMORY;
PRAGMA mmap_size = 268435456; -- 256MB
```

**查询优化示例**
```rust
// 优化的聊天消息查询
pub async fn get_session_messages_optimized(
    pool: &SqlitePool,
    session_id: &str,
    limit: i32,
    offset: i32
) -> Result<Vec<ChatMessage>, sqlx::Error> {
    sqlx::query_as!(
        ChatMessage,
        r#"
        SELECT id, session_id, role, content, attachments, tokens_used,
               response_time, created_at, status, error_message, is_edited
        FROM chat_messages
        WHERE session_id = ?
        ORDER BY created_at DESC
        LIMIT ? OFFSET ?
        "#,
        session_id, limit, offset
    )
    .fetch_all(pool)
    .await
}

// 批量插入优化
pub async fn batch_insert_messages(
    pool: &SqlitePool,
    messages: &[ChatMessage]
) -> Result<(), sqlx::Error> {
    let mut tx = pool.begin().await?;

    for chunk in messages.chunks(100) {
        let mut query_builder = QueryBuilder::new(
            "INSERT INTO chat_messages (id, session_id, role, content, created_at) "
        );

        query_builder.push_values(chunk, |mut b, message| {
            b.push_bind(&message.id)
             .push_bind(&message.session_id)
             .push_bind(&message.role)
             .push_bind(&message.content)
             .push_bind(&message.created_at);
        });

        query_builder.build().execute(&mut *tx).await?;
    }

    tx.commit().await?;
    Ok(())
}

// 连接池配置
pub async fn create_optimized_pool(database_url: &str) -> Result<SqlitePool, sqlx::Error> {
    SqlitePoolOptions::new()
        .max_connections(20)
        .min_connections(5)
        .acquire_timeout(Duration::from_secs(30))
        .idle_timeout(Duration::from_secs(600))
        .max_lifetime(Duration::from_secs(1800))
        .connect_with(
            SqliteConnectOptions::from_str(database_url)?
                .journal_mode(SqliteJournalMode::Wal)
                .synchronous(SqliteSynchronous::Normal)
                .busy_timeout(Duration::from_secs(30))
                .pragma("cache_size", "10000")
                .pragma("temp_store", "memory")
                .pragma("mmap_size", "268435456")
        )
        .await
}
```

### 10.5 内存管理优化

**智能模型缓存**
```rust
use std::sync::Arc;
use tokio::sync::RwLock;
use lru::LruCache;
use std::sync::atomic::{AtomicUsize, Ordering};

pub struct IntelligentModelCache {
    cache: Arc<RwLock<LruCache<String, Arc<Model>>>>,
    memory_tracker: Arc<MemoryTracker>,
    config: CacheConfig,
}

pub struct MemoryTracker {
    current_usage: AtomicUsize,
    peak_usage: AtomicUsize,
    max_allowed: usize,
}

pub struct CacheConfig {
    max_models: usize,
    memory_threshold: f64,  // 0.0-1.0
    cleanup_threshold: f64, // 0.0-1.0
    min_free_memory: usize,
}

impl IntelligentModelCache {
    pub fn new(config: CacheConfig) -> Self {
        let system_memory = Self::get_system_memory();
        let max_allowed = (system_memory as f64 * config.memory_threshold) as usize;

        Self {
            cache: Arc::new(RwLock::new(LruCache::new(config.max_models))),
            memory_tracker: Arc::new(MemoryTracker {
                current_usage: AtomicUsize::new(0),
                peak_usage: AtomicUsize::new(0),
                max_allowed,
            }),
            config,
        }
    }

    pub async fn get_or_load(&self, model_id: &str) -> Result<Arc<Model>, ModelError> {
        // 检查缓存
        {
            let cache = self.cache.read().await;
            if let Some(model) = cache.get(model_id) {
                return Ok(model.clone());
            }
        }

        // 检查内存使用情况
        self.ensure_memory_available().await?;

        // 加载新模型
        let model = Arc::new(Model::load(model_id).await?);
        let model_size = model.memory_size();

        // 更新内存跟踪
        let new_usage = self.memory_tracker.current_usage.fetch_add(model_size, Ordering::Relaxed) + model_size;
        self.memory_tracker.peak_usage.fetch_max(new_usage, Ordering::Relaxed);

        // 添加到缓存
        {
            let mut cache = self.cache.write().await;
            if let Some((_, evicted_model)) = cache.push(model_id.to_string(), model.clone()) {
                // 有模型被驱逐，更新内存使用
                let evicted_size = evicted_model.memory_size();
                self.memory_tracker.current_usage.fetch_sub(evicted_size, Ordering::Relaxed);
            }
        }

        Ok(model)
    }

    async fn ensure_memory_available(&self) -> Result<(), ModelError> {
        let current = self.memory_tracker.current_usage.load(Ordering::Relaxed);
        let max_allowed = self.memory_tracker.max_allowed;
        let cleanup_threshold = (max_allowed as f64 * self.config.cleanup_threshold) as usize;

        if current > cleanup_threshold {
            self.cleanup_memory().await?;
        }

        // 检查系统可用内存
        let available_memory = Self::get_available_memory();
        if available_memory < self.config.min_free_memory {
            return Err(ModelError::InsufficientMemory);
        }

        Ok(())
    }

    async fn cleanup_memory(&self) -> Result<(), ModelError> {
        let mut cache = self.cache.write().await;
        let target_usage = (self.memory_tracker.max_allowed as f64 * 0.7) as usize;
        let current_usage = self.memory_tracker.current_usage.load(Ordering::Relaxed);

        while current_usage > target_usage && !cache.is_empty() {
            if let Some((_, model)) = cache.pop_lru() {
                let model_size = model.memory_size();
                self.memory_tracker.current_usage.fetch_sub(model_size, Ordering::Relaxed);
            } else {
                break;
            }
        }

        Ok(())
    }

    fn get_system_memory() -> usize {
        // 获取系统总内存
        #[cfg(target_os = "windows")]
        {
            use winapi::um::sysinfoapi::{GetPhysicallyInstalledSystemMemory};
            let mut memory_kb = 0;
            unsafe {
                GetPhysicallyInstalledSystemMemory(&mut memory_kb);
            }
            (memory_kb as usize) * 1024
        }

        #[cfg(target_os = "macos")]
        {
            use libc::{sysctl, CTL_HW, HW_MEMSIZE};
            let mut size = std::mem::size_of::<u64>();
            let mut memory: u64 = 0;
            let mut mib = [CTL_HW, HW_MEMSIZE];

            unsafe {
                sysctl(
                    mib.as_mut_ptr(),
                    2,
                    &mut memory as *mut _ as *mut _,
                    &mut size,
                    std::ptr::null_mut(),
                    0,
                );
            }
            memory as usize
        }

        #[cfg(not(any(target_os = "windows", target_os = "macos")))]
        {
            8 * 1024 * 1024 * 1024 // 默认8GB
        }
    }

    fn get_available_memory() -> usize {
        // 获取可用内存的简化实现
        Self::get_system_memory() / 2 // 简化实现
    }

    pub fn get_memory_stats(&self) -> MemoryStats {
        MemoryStats {
            current_usage: self.memory_tracker.current_usage.load(Ordering::Relaxed),
            peak_usage: self.memory_tracker.peak_usage.load(Ordering::Relaxed),
            max_allowed: self.memory_tracker.max_allowed,
            cached_models: {
                // 这里需要异步访问，简化处理
                0 // 实际实现中需要适当处理
            },
        }
    }
}

pub struct MemoryStats {
    pub current_usage: usize,
    pub peak_usage: usize,
    pub max_allowed: usize,
    pub cached_models: usize,
}
```

## 10. 性能优化策略

### 10.1 前端性能优化

**组件懒加载优化**
```typescript
// 使用 defineAsyncComponent 实现组件懒加载
const ChatContainer = defineAsyncComponent(() => import('@/components/chat/ChatContainer.vue'))
const ModelManager = defineAsyncComponent(() => import('@/components/model/ModelManager.vue'))
const KnowledgeBase = defineAsyncComponent(() => import('@/components/knowledge/KnowledgeBase.vue'))

// 路由懒加载
const routes = [
  {
    path: '/chat',
    component: () => import('@/views/ChatView.vue')
  },
  {
    path: '/models',
    component: () => import('@/views/ModelView.vue')
  }
]
```

**渲染性能优化**
```typescript
// 使用 v-memo 优化列表渲染
<template>
  <div v-for="message in messages" :key="message.id" v-memo="[message.content, message.status]">
    <MessageItem :message="message" />
  </div>
</template>

// 虚拟滚动优化长列表
import { VirtualList } from '@tanstack/vue-virtual'

<VirtualList
  :items="largeDataSet"
  :item-size="60"
  v-slot="{ item }"
>
  <ListItem :data="item" />
</VirtualList>

// 使用 shallowRef 优化大数据对象
const largeDataSet = shallowRef<ModelInfo[]>([])
const modelList = shallowReactive<Model[]>([])
```

**状态管理优化**
```typescript
// Pinia store 优化
export const useChatStore = defineStore('chat', () => {
  const messages = ref<Message[]>([])

  // 使用 computed 缓存计算结果
  const messageCount = computed(() => messages.value.length)
  const unreadCount = computed(() =>
    messages.value.filter(m => !m.read).length
  )

  // 使用防抖优化频繁更新
  const debouncedSaveSession = debounce(saveSession, 1000)
  const throttledUpdateStatus = throttle(updateStatus, 500)

  return {
    messages,
    messageCount,
    unreadCount,
    debouncedSaveSession,
    throttledUpdateStatus
  }
})
```

### 10.2 后端性能优化

**内存管理优化**
```rust
use std::sync::Arc;
use tokio::sync::RwLock;
use lru::LruCache;
use std::sync::atomic::{AtomicUsize, Ordering};

pub struct ModelCache {
    cache: Arc<RwLock<LruCache<String, Arc<Model>>>>,
    max_memory: usize,
    current_memory: Arc<AtomicUsize>,
}

impl ModelCache {
    pub fn new(max_memory_gb: usize) -> Self {
        Self {
            cache: Arc::new(RwLock::new(LruCache::new(5))),
            max_memory: max_memory_gb * 1024 * 1024 * 1024,
            current_memory: Arc::new(AtomicUsize::new(0)),
        }
    }

    pub async fn get_or_load(&self, model_id: &str) -> Result<Arc<Model>, ModelError> {
        // 先尝试从缓存获取
        {
            let cache = self.cache.read().await;
            if let Some(model) = cache.get(model_id) {
                return Ok(model.clone());
            }
        }

        // 检查内存使用
        self.check_memory_usage().await?;

        // 加载新模型
        let model = Arc::new(Model::load(model_id).await?);
        let model_size = model.memory_size();

        {
            let mut cache = self.cache.write().await;
            cache.put(model_id.to_string(), model.clone());
        }

        self.current_memory.fetch_add(model_size, Ordering::Relaxed);

        Ok(model)
    }

    async fn check_memory_usage(&self) -> Result<(), ModelError> {
        while self.current_memory.load(Ordering::Relaxed) > self.max_memory {
            self.evict_least_used().await?;
        }
        Ok(())
    }

    async fn evict_least_used(&self) -> Result<(), ModelError> {
        let mut cache = self.cache.write().await;
        if let Some((_, model)) = cache.pop_lru() {
            let model_size = model.memory_size();
            self.current_memory.fetch_sub(model_size, Ordering::Relaxed);
        }
        Ok(())
    }
}
```

**数据库连接池优化**
```rust
use sqlx::{Pool, Sqlite, SqlitePool, sqlite::SqliteConnectOptions};
use sqlx::sqlite::{SqliteJournalMode, SqliteSynchronous};

pub struct DatabaseManager {
    pool: SqlitePool,
}

impl DatabaseManager {
    pub async fn new() -> Result<Self, sqlx::Error> {
        let pool = SqlitePool::connect_with(
            SqliteConnectOptions::new()
                .filename("ai_studio.db")
                .create_if_missing(true)
                .journal_mode(SqliteJournalMode::Wal)
                .synchronous(SqliteSynchronous::Normal)
                .busy_timeout(Duration::from_secs(30))
                .max_connections(20)
                .min_connections(5)
        ).await?;

        Ok(Self { pool })
    }

    // 批量插入优化
    pub async fn batch_insert_messages(&self, messages: &[Message]) -> Result<(), sqlx::Error> {
        let mut tx = self.pool.begin().await?;

        for chunk in messages.chunks(100) {
            let mut query_builder = QueryBuilder::new(
                "INSERT INTO chat_messages (id, session_id, role, content, created_at) "
            );

            query_builder.push_values(chunk, |mut b, message| {
                b.push_bind(&message.id)
                 .push_bind(&message.session_id)
                 .push_bind(&message.role)
                 .push_bind(&message.content)
                 .push_bind(&message.created_at);
            });

            query_builder.build().execute(&mut *tx).await?;
        }

        tx.commit().await?;
        Ok(())
    }
}
```

### 10.3 并发处理优化

**信号量控制并发**
```rust
use tokio::sync::Semaphore;
use std::sync::Arc;

pub struct ConcurrencyManager {
    inference_semaphore: Arc<Semaphore>,
    download_semaphore: Arc<Semaphore>,
    upload_semaphore: Arc<Semaphore>,
}

impl ConcurrencyManager {
    pub fn new() -> Self {
        Self {
            inference_semaphore: Arc::new(Semaphore::new(4)), // 最多4个并发推理
            download_semaphore: Arc::new(Semaphore::new(2)),  // 最多2个并发下载
            upload_semaphore: Arc::new(Semaphore::new(3)),    // 最多3个并发上传
        }
    }

    pub async fn run_inference<F, T>(&self, task: F) -> Result<T, Box<dyn std::error::Error>>
    where
        F: std::future::Future<Output = Result<T, Box<dyn std::error::Error>>>,
    {
        let _permit = self.inference_semaphore.acquire().await?;
        task.await
    }

    pub async fn run_download<F, T>(&self, task: F) -> Result<T, Box<dyn std::error::Error>>
    where
        F: std::future::Future<Output = Result<T, Box<dyn std::error::Error>>>,
    {
        let _permit = self.download_semaphore.acquire().await?;
        task.await
    }
}
```

### 10.4 缓存策略优化

**多级缓存系统**
```rust
use moka::future::Cache;
use std::time::Duration;

pub struct CacheManager {
    // L1缓存：内存缓存，快速访问
    embedding_cache: Cache<String, Vec<f32>>,
    search_cache: Cache<String, Vec<SearchResult>>,
    model_info_cache: Cache<String, ModelInfo>,

    // L2缓存：磁盘缓存，持久化
    disk_cache_path: PathBuf,
}

impl CacheManager {
    pub fn new(cache_dir: PathBuf) -> Self {
        Self {
            embedding_cache: Cache::builder()
                .max_capacity(10_000)
                .time_to_live(Duration::from_secs(3600))
                .time_to_idle(Duration::from_secs(1800))
                .build(),
            search_cache: Cache::builder()
                .max_capacity(1_000)
                .time_to_live(Duration::from_secs(300))
                .build(),
            model_info_cache: Cache::builder()
                .max_capacity(500)
                .time_to_live(Duration::from_secs(1800))
                .build(),
            disk_cache_path: cache_dir,
        }
    }

    pub async fn get_embedding(&self, text: &str) -> Option<Vec<f32>> {
        // 先从内存缓存获取
        if let Some(embedding) = self.embedding_cache.get(text).await {
            return Some(embedding);
        }

        // 再从磁盘缓存获取
        if let Ok(embedding) = self.load_from_disk_cache(text).await {
            self.embedding_cache.insert(text.to_string(), embedding.clone()).await;
            return Some(embedding);
        }

        None
    }

    pub async fn set_embedding(&self, text: &str, embedding: Vec<f32>) {
        // 同时写入内存和磁盘缓存
        self.embedding_cache.insert(text.to_string(), embedding.clone()).await;
        let _ = self.save_to_disk_cache(text, &embedding).await;
    }

    async fn load_from_disk_cache(&self, key: &str) -> Result<Vec<f32>, std::io::Error> {
        let cache_file = self.disk_cache_path.join(format!("{}.cache", Self::hash_key(key)));
        let data = tokio::fs::read(cache_file).await?;
        Ok(bincode::deserialize(&data).map_err(|e| std::io::Error::new(std::io::ErrorKind::InvalidData, e))?)
    }

    async fn save_to_disk_cache(&self, key: &str, data: &Vec<f32>) -> Result<(), std::io::Error> {
        let cache_file = self.disk_cache_path.join(format!("{}.cache", Self::hash_key(key)));
        let serialized = bincode::serialize(data).map_err(|e| std::io::Error::new(std::io::ErrorKind::InvalidData, e))?;
        tokio::fs::write(cache_file, serialized).await
    }

    fn hash_key(key: &str) -> String {
        use std::collections::hash_map::DefaultHasher;
        use std::hash::{Hash, Hasher};

        let mut hasher = DefaultHasher::new();
        key.hash(&mut hasher);
        format!("{:x}", hasher.finish())
    }
}
```

### 10.5 资源监控和告警

**系统资源监控**
```rust
use sysinfo::{System, SystemExt, ProcessExt, CpuExt};

pub struct ResourceMonitor {
    system: System,
    alert_thresholds: AlertThresholds,
}

#[derive(Clone)]
pub struct AlertThresholds {
    pub cpu_usage: f32,      // CPU使用率阈值 (%)
    pub memory_usage: f32,   // 内存使用率阈值 (%)
    pub disk_usage: f32,     // 磁盘使用率阈值 (%)
    pub response_time: f64,  // 响应时间阈值 (秒)
}

impl Default for AlertThresholds {
    fn default() -> Self {
        Self {
            cpu_usage: 80.0,
            memory_usage: 85.0,
            disk_usage: 90.0,
            response_time: 5.0,
        }
    }
}

impl ResourceMonitor {
    pub fn new() -> Self {
        Self {
            system: System::new_all(),
            alert_thresholds: AlertThresholds::default(),
        }
    }

    pub fn get_system_metrics(&mut self) -> SystemMetrics {
        self.system.refresh_all();

        let cpu_usage = self.system.global_cpu_info().cpu_usage();
        let memory_usage = (self.system.used_memory() as f64 / self.system.total_memory() as f64) * 100.0;
        let disk_usage = self.get_disk_usage_percentage();

        SystemMetrics {
            cpu_usage,
            memory_usage: memory_usage as f32,
            total_memory: self.system.total_memory(),
            used_memory: self.system.used_memory(),
            disk_usage,
            gpu_usage: self.get_gpu_usage(),
            network_io: self.get_network_io(),
            process_count: self.system.processes().len() as u32,
        }
    }

    pub fn check_alerts(&mut self) -> Vec<Alert> {
        let metrics = self.get_system_metrics();
        let mut alerts = Vec::new();

        if metrics.cpu_usage > self.alert_thresholds.cpu_usage {
            alerts.push(Alert {
                level: AlertLevel::Warning,
                message: format!("CPU使用率过高: {:.1}%", metrics.cpu_usage),
                metric_name: "cpu_usage".to_string(),
                current_value: metrics.cpu_usage as f64,
                threshold: self.alert_thresholds.cpu_usage as f64,
            });
        }

        if metrics.memory_usage > self.alert_thresholds.memory_usage {
            alerts.push(Alert {
                level: AlertLevel::Warning,
                message: format!("内存使用率过高: {:.1}%", metrics.memory_usage),
                metric_name: "memory_usage".to_string(),
                current_value: metrics.memory_usage as f64,
                threshold: self.alert_thresholds.memory_usage as f64,
            });
        }

        if metrics.disk_usage > self.alert_thresholds.disk_usage {
            alerts.push(Alert {
                level: AlertLevel::Critical,
                message: format!("磁盘空间不足: {:.1}%", metrics.disk_usage),
                metric_name: "disk_usage".to_string(),
                current_value: metrics.disk_usage as f64,
                threshold: self.alert_thresholds.disk_usage as f64,
            });
        }

        alerts
    }

    fn get_disk_usage_percentage(&self) -> f32 {
        // 获取主磁盘使用率
        if let Some(disk) = self.system.disks().first() {
            let total = disk.total_space();
            let available = disk.available_space();
            let used = total - available;
            (used as f64 / total as f64 * 100.0) as f32
        } else {
            0.0
        }
    }

    fn get_gpu_usage(&self) -> f32 {
        // GPU使用率获取（需要根据具体GPU库实现）
        // 这里返回模拟值
        0.0
    }

    fn get_network_io(&self) -> NetworkIO {
        // 网络IO统计
        NetworkIO {
            bytes_sent: 0,
            bytes_received: 0,
            packets_sent: 0,
            packets_received: 0,
        }
    }
}

#[derive(Debug, Clone)]
pub struct Alert {
    pub level: AlertLevel,
    pub message: String,
    pub metric_name: String,
    pub current_value: f64,
    pub threshold: f64,
}

#[derive(Debug, Clone)]
pub enum AlertLevel {
    Info,
    Warning,
    Critical,
}

#[derive(Debug, Clone)]
pub struct SystemMetrics {
    pub cpu_usage: f32,
    pub memory_usage: f32,
    pub total_memory: u64,
    pub used_memory: u64,
    pub disk_usage: f32,
    pub gpu_usage: f32,
    pub network_io: NetworkIO,
    pub process_count: u32,
}

#[derive(Debug, Clone)]
pub struct NetworkIO {
    pub bytes_sent: u64,
    pub bytes_received: u64,
    pub packets_sent: u64,
    pub packets_received: u64,
}
```

### 10.6 性能基准测试

**基准测试框架**
```rust
use std::time::{Duration, Instant};
use tokio::time::sleep;

pub struct BenchmarkSuite {
    results: Vec<BenchmarkResult>,
}

impl BenchmarkSuite {
    pub fn new() -> Self {
        Self {
            results: Vec::new(),
        }
    }

    pub async fn run_inference_benchmark(&mut self, model_id: &str, test_prompts: &[&str]) -> BenchmarkResult {
        let mut latencies = Vec::new();
        let mut throughputs = Vec::new();

        for prompt in test_prompts {
            let start = Instant::now();

            // 执行推理
            let _result = self.run_inference(model_id, prompt).await;

            let latency = start.elapsed();
            latencies.push(latency.as_millis() as f64);

            // 计算吞吐量 (tokens/second)
            let token_count = prompt.split_whitespace().count() as f64;
            let throughput = token_count / latency.as_secs_f64();
            throughputs.push(throughput);
        }

        BenchmarkResult {
            test_name: format!("inference_benchmark_{}", model_id),
            avg_latency: latencies.iter().sum::<f64>() / latencies.len() as f64,
            p95_latency: Self::percentile(&latencies, 0.95),
            p99_latency: Self::percentile(&latencies, 0.99),
            avg_throughput: throughputs.iter().sum::<f64>() / throughputs.len() as f64,
            success_rate: 100.0, // 假设所有测试都成功
        }
    }

    pub async fn run_memory_benchmark(&mut self) -> BenchmarkResult {
        let initial_memory = self.get_memory_usage();

        // 执行内存密集型操作
        let _large_data = vec![0u8; 100 * 1024 * 1024]; // 100MB
        sleep(Duration::from_secs(1)).await;

        let peak_memory = self.get_memory_usage();
        let memory_increase = peak_memory - initial_memory;

        BenchmarkResult {
            test_name: "memory_benchmark".to_string(),
            avg_latency: memory_increase as f64,
            p95_latency: 0.0,
            p99_latency: 0.0,
            avg_throughput: 0.0,
            success_rate: 100.0,
        }
    }

    fn percentile(data: &[f64], percentile: f64) -> f64 {
        let mut sorted = data.to_vec();
        sorted.sort_by(|a, b| a.partial_cmp(b).unwrap());
        let index = (percentile * (sorted.len() - 1) as f64).round() as usize;
        sorted[index.min(sorted.len() - 1)]
    }

    fn get_memory_usage(&self) -> u64 {
        // 获取当前内存使用量
        0 // 简化实现
    }

    async fn run_inference(&self, _model_id: &str, _prompt: &str) -> String {
        // 模拟推理过程
        sleep(Duration::from_millis(100)).await;
        "测试响应".to_string()
    }
}

#[derive(Debug, Clone)]
pub struct BenchmarkResult {
    pub test_name: String,
    pub avg_latency: f64,      // 平均延迟 (ms)
    pub p95_latency: f64,      // 95分位延迟 (ms)
    pub p99_latency: f64,      // 99分位延迟 (ms)
    pub avg_throughput: f64,   // 平均吞吐量 (tokens/s)
    pub success_rate: f64,     // 成功率 (%)
}
```

## 12. 测试与质量保证

### 12.1 测试策略

**测试类型覆盖**
- **单元测试**：核心功能模块测试，覆盖率 > 85%
- **集成测试**：模块间交互测试
- **端到端测试**：完整用户流程测试
- **性能测试**：响应时间和资源占用测试
- **安全测试**：漏洞扫描和渗透测试

**自动化测试框架**
```rust
#[cfg(test)]
mod tests {
    use super::*;
    use tokio_test;

    #[test]
    fn test_model_loading() {
        let model_manager = ModelManager::new();
        let model = model_manager.load_test_model("test-model");
        assert!(model.is_ok());
        assert!(model.unwrap().is_loaded());
    }

    #[tokio::test]
    async fn test_chat_workflow() {
        let chat_service = ChatService::new().await;
        let session = chat_service.create_session("test-session").await.unwrap();

        let response = chat_service.send_message(
            &session.id,
            "Hello, how are you?",
            None
        ).await;

        assert!(response.is_ok());
        let message = response.unwrap();
        assert!(!message.content.is_empty());
        assert_eq!(message.role, MessageRole::Assistant);
    }

    #[tokio::test]
    async fn test_knowledge_base_search() {
        let kb_service = KnowledgeBaseService::new().await;
        let kb = kb_service.create_knowledge_base("test-kb", "Test KB").await.unwrap();

        // 添加测试文档
        let doc_id = kb_service.add_document(
            &kb.id,
            "test.txt",
            "This is a test document for searching."
        ).await.unwrap();

        // 等待索引完成
        tokio::time::sleep(Duration::from_secs(1)).await;

        // 执行搜索
        let results = kb_service.search(&kb.id, "test document", 5).await.unwrap();
        assert!(!results.is_empty());
        assert!(results[0].score > 0.5);
    }

    #[test]
    fn test_data_encryption() {
        let encryption = DataEncryption::new().unwrap();
        let original_data = b"sensitive user data";

        let encrypted = encryption.encrypt(original_data).unwrap();
        assert_ne!(encrypted, original_data);

        let decrypted = encryption.decrypt(&encrypted).unwrap();
        assert_eq!(decrypted, original_data);
    }

    #[tokio::test]
    async fn test_network_discovery() {
        let network_service = NetworkService::new().await;

        // 启动服务发现
        network_service.start_discovery().await.unwrap();

        // 模拟发现设备
        let devices = network_service.get_discovered_devices().await;

        // 验证设备信息
        for device in devices {
            assert!(!device.id.is_empty());
            assert!(!device.name.is_empty());
            assert!(device.capabilities.len() > 0);
        }
    }
}
```

**性能基准测试**
```rust
use criterion::{black_box, criterion_group, criterion_main, Criterion};

fn benchmark_model_inference(c: &mut Criterion) {
    let rt = tokio::runtime::Runtime::new().unwrap();
    let model_manager = rt.block_on(async {
        ModelManager::new().await
    });

    c.bench_function("model_inference", |b| {
        b.iter(|| {
            rt.block_on(async {
                let result = model_manager.generate_response(
                    black_box("What is the capital of France?"),
                    black_box(&GenerationConfig::default())
                ).await;
                black_box(result)
            })
        })
    });
}

fn benchmark_knowledge_search(c: &mut Criterion) {
    let rt = tokio::runtime::Runtime::new().unwrap();
    let kb_service = rt.block_on(async {
        let service = KnowledgeBaseService::new().await;
        // 预加载测试数据
        service.load_test_data().await.unwrap();
        service
    });

    c.bench_function("knowledge_search", |b| {
        b.iter(|| {
            rt.block_on(async {
                let results = kb_service.search(
                    black_box("test-kb"),
                    black_box("artificial intelligence"),
                    black_box(10)
                ).await;
                black_box(results)
            })
        })
    });
}

criterion_group!(benches, benchmark_model_inference, benchmark_knowledge_search);
criterion_main!(benches);
```

### 12.2 质量指标

**代码质量指标**
```toml
# Cargo.toml 测试配置
[dev-dependencies]
tokio-test = "0.4"
criterion = "0.5"
proptest = "1.0"
mockall = "0.11"

[[bench]]
name = "performance"
harness = false

[package.metadata.coverage]
exclude = ["tests/*", "benches/*"]
target-coverage = 85.0
```

**质量标准**
- **代码覆盖率**：> 85%
- **崩溃率**：< 0.1%
- **API响应时间**：< 500ms (P95)
- **内存泄漏**：24小时测试 < 2MB增长
- **安全漏洞**：0个高危漏洞
- **性能回归**：< 5%性能下降

## 13. 部署与更新策略

### 13.1 跨平台部署

**Windows部署**
```rust
// Windows特定功能
#[cfg(target_os = "windows")]
mod windows {
    use winapi::um::winuser::{MessageBoxW, MB_OK};
    use std::ffi::OsStr;
    use std::os::windows::ffi::OsStrExt;

    pub fn show_notification(title: &str, message: &str) {
        let title_wide: Vec<u16> = OsStr::new(title).encode_wide().chain(Some(0)).collect();
        let message_wide: Vec<u16> = OsStr::new(message).encode_wide().chain(Some(0)).collect();

        unsafe {
            MessageBoxW(
                std::ptr::null_mut(),
                message_wide.as_ptr(),
                title_wide.as_ptr(),
                MB_OK
            );
        }
    }

    pub fn register_file_associations() -> Result<(), Box<dyn std::error::Error>> {
        use winreg::enums::*;
        use winreg::RegKey;

        let hkcu = RegKey::predef(HKEY_CURRENT_USER);
        let classes = hkcu.open_subkey("Software\\Classes")?;

        // 注册.aiproj文件关联
        let aiproj_key = classes.create_subkey(".aiproj")?;
        aiproj_key.0.set_value("", &"AIStudio.Project")?;

        let aiproj_class = classes.create_subkey("AIStudio.Project")?;
        aiproj_class.0.set_value("", &"AI Studio Project File")?;

        let shell_key = aiproj_class.0.create_subkey("shell\\open\\command")?;
        let exe_path = std::env::current_exe()?;
        shell_key.0.set_value("", &format!("\"{}\" \"%1\"", exe_path.display()))?;

        Ok(())
    }
}

// macOS特定功能
#[cfg(target_os = "macos")]
mod macos {
    use cocoa::appkit::{NSApp, NSApplication, NSApplicationActivationPolicy};
    use cocoa::base::{id, nil};
    use objc::{msg_send, sel, sel_impl};

    pub fn setup_macos_app() {
        unsafe {
            let app = NSApp();
            app.setActivationPolicy_(NSApplicationActivationPolicy::NSApplicationActivationPolicyRegular);
        }
    }

    pub fn setup_dock_menu() -> Result<(), Box<dyn std::error::Error>> {
        // 设置Dock菜单项
        Ok(())
    }

    pub fn handle_url_scheme(url: &str) -> Result<(), Box<dyn std::error::Error>> {
        // 处理自定义URL协议 aistudio://
        println!("Handling URL: {}", url);
        Ok(())
    }
}
```

**打包配置**
```json
// tauri.conf.json 部署配置
{
  "bundle": {
    "active": true,
    "targets": ["msi", "dmg", "appimage"],
    "identifier": "com.aistudio.app",
    "icon": [
      "icons/32x32.png",
      "icons/128x128.png",
      "icons/<EMAIL>",
      "icons/icon.icns",
      "icons/icon.ico"
    ],
    "resources": ["models/", "plugins/"],
    "externalBin": [],
    "copyright": "Copyright © 2024 AI Studio Team",
    "category": "DeveloperTool",
    "shortDescription": "AI Studio - 中文AI助手桌面应用",
    "longDescription": "AI Studio 是一个功能强大的本地AI助手桌面应用，支持聊天、知识库、模型管理等功能。",
    "deb": {
      "depends": ["libwebkit2gtk-4.0-37", "libgtk-3-0"],
      "section": "utils",
      "priority": "optional"
    },
    "macOS": {
      "frameworks": [],
      "minimumSystemVersion": "10.15",
      "exceptionDomain": "",
      "signingIdentity": null,
      "hardenedRuntime": true,
      "entitlements": "entitlements.plist"
    },
    "windows": {
      "certificateThumbprint": null,
      "digestAlgorithm": "sha256",
      "timestampUrl": "",
      "wix": {
        "language": ["zh-CN", "en-US"],
        "template": "installer.wxs"
      }
    }
  }
}
```

### 13.2 自动更新系统

**更新检查机制**
```rust
use serde::{Deserialize, Serialize};
use reqwest::Client;
use semver::Version;

#[derive(Debug, Serialize, Deserialize)]
pub struct UpdateInfo {
    pub version: String,
    pub release_notes: String,
    pub download_url: String,
    pub signature: String,
    pub file_size: u64,
    pub release_date: String,
    pub minimum_version: Option<String>,
    pub critical: bool,
}

pub struct UpdateManager {
    client: Client,
    current_version: Version,
    update_url: String,
    auto_check: bool,
}

impl UpdateManager {
    pub fn new() -> Result<Self, UpdateError> {
        let current_version = Version::parse(env!("CARGO_PKG_VERSION"))?;

        Ok(Self {
            client: Client::new(),
            current_version,
            update_url: "https://api.aistudio.com/updates".to_string(),
            auto_check: true,
        })
    }

    pub async fn check_for_updates(&self) -> Result<Option<UpdateInfo>, UpdateError> {
        let response = self.client
            .get(&format!("{}/check", self.update_url))
            .query(&[
                ("current_version", self.current_version.to_string()),
                ("platform", std::env::consts::OS),
                ("arch", std::env::consts::ARCH),
            ])
            .send()
            .await?;

        if response.status().is_success() {
            let update_info: UpdateInfo = response.json().await?;
            let latest_version = Version::parse(&update_info.version)?;

            if latest_version > self.current_version {
                Ok(Some(update_info))
            } else {
                Ok(None)
            }
        } else {
            Err(UpdateError::CheckFailed(response.status()))
        }
    }

    pub async fn download_update(&self, update_info: &UpdateInfo) -> Result<PathBuf, UpdateError> {
        let temp_dir = std::env::temp_dir();
        let file_name = format!("ai_studio_update_{}.exe", update_info.version);
        let temp_path = temp_dir.join(file_name);

        let response = self.client
            .get(&update_info.download_url)
            .send()
            .await?;

        if !response.status().is_success() {
            return Err(UpdateError::DownloadFailed(response.status()));
        }

        let mut file = tokio::fs::File::create(&temp_path).await?;
        let mut stream = response.bytes_stream();

        while let Some(chunk) = stream.next().await {
            let chunk = chunk?;
            file.write_all(&chunk).await?;
        }

        // 验证文件完整性
        self.verify_update_file(&temp_path, update_info).await?;

        Ok(temp_path)
    }

    async fn verify_update_file(&self, file_path: &Path, update_info: &UpdateInfo) -> Result<(), UpdateError> {
        // 验证文件大小
        let metadata = tokio::fs::metadata(file_path).await?;
        if metadata.len() != update_info.file_size {
            return Err(UpdateError::InvalidFileSize);
        }

        // 验证数字签名
        let file_content = tokio::fs::read(file_path).await?;
        if !self.verify_signature(&file_content, &update_info.signature) {
            return Err(UpdateError::InvalidSignature);
        }

        Ok(())
    }

    fn verify_signature(&self, content: &[u8], signature: &str) -> bool {
        // 实现数字签名验证
        // 这里应该使用实际的公钥验证逻辑
        true // 简化实现
    }

    pub async fn apply_update(&self, update_path: &Path) -> Result<(), UpdateError> {
        #[cfg(target_os = "windows")]
        {
            // Windows更新逻辑
            let current_exe = std::env::current_exe()?;
            let backup_path = current_exe.with_extension("exe.backup");

            // 备份当前可执行文件
            tokio::fs::copy(&current_exe, &backup_path).await?;

            // 替换可执行文件
            tokio::fs::copy(update_path, &current_exe).await?;

            // 重启应用
            std::process::Command::new(&current_exe)
                .spawn()?;

            std::process::exit(0);
        }

        #[cfg(target_os = "macos")]
        {
            // macOS更新逻辑
            let current_app = std::env::current_exe()?
                .ancestors()
                .nth(3) // 从 .app/Contents/MacOS/binary 到 .app
                .ok_or(UpdateError::InvalidAppStructure)?;

            let backup_path = current_app.with_extension("app.backup");

            // 备份当前应用
            self.copy_dir_recursive(current_app, &backup_path).await?;

            // 解压新版本
            self.extract_dmg(update_path, current_app.parent().unwrap()).await?;

            // 重启应用
            std::process::Command::new("open")
                .arg(current_app)
                .spawn()?;

            std::process::exit(0);
        }

        Ok(())
    }

    #[cfg(target_os = "macos")]
    async fn copy_dir_recursive(&self, src: &Path, dst: &Path) -> Result<(), UpdateError> {
        tokio::fs::create_dir_all(dst).await?;

        let mut entries = tokio::fs::read_dir(src).await?;
        while let Some(entry) = entries.next_entry().await? {
            let src_path = entry.path();
            let dst_path = dst.join(entry.file_name());

            if src_path.is_dir() {
                self.copy_dir_recursive(&src_path, &dst_path).await?;
            } else {
                tokio::fs::copy(&src_path, &dst_path).await?;
            }
        }

        Ok(())
    }

    #[cfg(target_os = "macos")]
    async fn extract_dmg(&self, dmg_path: &Path, target_dir: &Path) -> Result<(), UpdateError> {
        // 挂载DMG文件
        let output = std::process::Command::new("hdiutil")
            .args(&["attach", "-nobrowse", "-mountpoint", "/tmp/ai_studio_update"])
            .arg(dmg_path)
            .output()?;

        if !output.status.success() {
            return Err(UpdateError::DmgMountFailed);
        }

        // 复制应用
        let src_app = Path::new("/tmp/ai_studio_update/AI Studio.app");
        let dst_app = target_dir.join("AI Studio.app");

        self.copy_dir_recursive(src_app, &dst_app).await?;

        // 卸载DMG
        std::process::Command::new("hdiutil")
            .args(&["detach", "/tmp/ai_studio_update"])
            .output()?;

        Ok(())
    }
}

#[derive(Debug, thiserror::Error)]
pub enum UpdateError {
    #[error("Version parse error: {0}")]
    VersionParse(#[from] semver::Error),
    #[error("HTTP error: {0}")]
    Http(#[from] reqwest::Error),
    #[error("IO error: {0}")]
    Io(#[from] std::io::Error),
    #[error("Update check failed: {0}")]
    CheckFailed(reqwest::StatusCode),
    #[error("Download failed: {0}")]
    DownloadFailed(reqwest::StatusCode),
    #[error("Invalid file size")]
    InvalidFileSize,
    #[error("Invalid signature")]
    InvalidSignature,
    #[error("Invalid app structure")]
    InvalidAppStructure,
    #[error("DMG mount failed")]
    DmgMountFailed,
}
```

## 14. 错误处理和日志系统

### 14.1 统一错误处理

**错误类型定义**
```rust
use thiserror::Error;
use serde::{Serialize, Deserialize};

#[derive(Error, Debug, Serialize, Deserialize)]
pub enum AppError {
    #[error("数据库错误: {message}")]
    Database { message: String, code: String },

    #[error("模型错误: {message}")]
    Model { message: String, model_id: String },

    #[error("网络错误: {message}")]
    Network { message: String, url: Option<String> },

    #[error("文件系统错误: {message}")]
    FileSystem { message: String, path: Option<String> },

    #[error("插件错误: {message}")]
    Plugin { message: String, plugin_id: String },

    #[error("配置错误: {message}")]
    Configuration { message: String, key: Option<String> },

    #[error("权限错误: {message}")]
    Permission { message: String, resource: String },

    #[error("验证错误: {message}")]
    Validation { message: String, field: Option<String> },

    #[error("外部服务错误: {message}")]
    ExternalService { message: String, service: String, status_code: Option<u16> },

    #[error("内部错误: {message}")]
    Internal { message: String, context: Option<String> },
}

impl AppError {
    pub fn error_code(&self) -> &'static str {
        match self {
            AppError::Database { .. } => "DB_ERROR",
            AppError::Model { .. } => "MODEL_ERROR",
            AppError::Network { .. } => "NETWORK_ERROR",
            AppError::FileSystem { .. } => "FS_ERROR",
            AppError::Plugin { .. } => "PLUGIN_ERROR",
            AppError::Configuration { .. } => "CONFIG_ERROR",
            AppError::Permission { .. } => "PERMISSION_ERROR",
            AppError::Validation { .. } => "VALIDATION_ERROR",
            AppError::ExternalService { .. } => "EXTERNAL_ERROR",
            AppError::Internal { .. } => "INTERNAL_ERROR",
        }
    }

    pub fn severity(&self) -> ErrorSeverity {
        match self {
            AppError::Database { .. } => ErrorSeverity::High,
            AppError::Model { .. } => ErrorSeverity::Medium,
            AppError::Network { .. } => ErrorSeverity::Low,
            AppError::FileSystem { .. } => ErrorSeverity::Medium,
            AppError::Plugin { .. } => ErrorSeverity::Low,
            AppError::Configuration { .. } => ErrorSeverity::High,
            AppError::Permission { .. } => ErrorSeverity::High,
            AppError::Validation { .. } => ErrorSeverity::Low,
            AppError::ExternalService { .. } => ErrorSeverity::Medium,
            AppError::Internal { .. } => ErrorSeverity::Critical,
        }
    }

    pub fn is_recoverable(&self) -> bool {
        match self {
            AppError::Network { .. } => true,
            AppError::ExternalService { .. } => true,
            AppError::Plugin { .. } => true,
            AppError::Validation { .. } => true,
            _ => false,
        }
    }

    pub fn user_message(&self) -> String {
        match self {
            AppError::Database { .. } => "数据库操作失败，请稍后重试".to_string(),
            AppError::Model { .. } => "模型操作失败，请检查模型状态".to_string(),
            AppError::Network { .. } => "网络连接失败，请检查网络设置".to_string(),
            AppError::FileSystem { .. } => "文件操作失败，请检查文件权限".to_string(),
            AppError::Plugin { .. } => "插件执行失败，请检查插件配置".to_string(),
            AppError::Configuration { .. } => "配置错误，请检查设置".to_string(),
            AppError::Permission { .. } => "权限不足，请联系管理员".to_string(),
            AppError::Validation { .. } => "输入数据无效，请检查输入".to_string(),
            AppError::ExternalService { .. } => "外部服务不可用，请稍后重试".to_string(),
            AppError::Internal { .. } => "系统内部错误，请联系技术支持".to_string(),
        }
    }
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum ErrorSeverity {
    Low,
    Medium,
    High,
    Critical,
}

// 错误上下文
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ErrorContext {
    pub timestamp: String,
    pub user_id: Option<String>,
    pub session_id: Option<String>,
    pub request_id: Option<String>,
    pub component: String,
    pub operation: String,
    pub parameters: Option<serde_json::Value>,
    pub stack_trace: Option<String>,
    pub system_info: SystemInfo,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SystemInfo {
    pub os: String,
    pub arch: String,
    pub app_version: String,
    pub memory_usage: u64,
    pub cpu_usage: f32,
}
```

**错误处理中间件**
```rust
use std::sync::Arc;
use tokio::sync::RwLock;

pub struct ErrorHandler {
    logger: Arc<Logger>,
    metrics: Arc<RwLock<ErrorMetrics>>,
    recovery_strategies: HashMap<String, Box<dyn RecoveryStrategy>>,
}

impl ErrorHandler {
    pub fn new(logger: Arc<Logger>) -> Self {
        let mut recovery_strategies: HashMap<String, Box<dyn RecoveryStrategy>> = HashMap::new();

        // 注册恢复策略
        recovery_strategies.insert("NETWORK_ERROR".to_string(), Box::new(NetworkRecoveryStrategy));
        recovery_strategies.insert("MODEL_ERROR".to_string(), Box::new(ModelRecoveryStrategy));
        recovery_strategies.insert("DB_ERROR".to_string(), Box::new(DatabaseRecoveryStrategy));

        Self {
            logger,
            metrics: Arc::new(RwLock::new(ErrorMetrics::new())),
            recovery_strategies,
        }
    }

    pub async fn handle_error(&self, error: AppError, context: ErrorContext) -> ErrorHandlingResult {
        // 记录错误
        self.log_error(&error, &context).await;

        // 更新指标
        self.update_metrics(&error).await;

        // 尝试恢复
        let recovery_result = self.attempt_recovery(&error, &context).await;

        // 通知用户
        let user_notification = self.create_user_notification(&error, &recovery_result);

        ErrorHandlingResult {
            error: error.clone(),
            context,
            recovery_attempted: recovery_result.is_some(),
            recovery_successful: recovery_result.as_ref().map_or(false, |r| r.successful),
            user_notification,
            should_retry: error.is_recoverable() && recovery_result.as_ref().map_or(false, |r| r.should_retry),
        }
    }
}

// 恢复策略接口
#[async_trait::async_trait]
pub trait RecoveryStrategy: Send + Sync {
    async fn recover(&self, error: &AppError, context: &ErrorContext) -> RecoveryResult;
}

#[derive(Debug, Clone)]
pub struct RecoveryResult {
    pub successful: bool,
    pub should_retry: bool,
    pub recovery_message: String,
}

#[derive(Debug, Clone)]
pub struct ErrorHandlingResult {
    pub error: AppError,
    pub context: ErrorContext,
    pub recovery_attempted: bool,
    pub recovery_successful: bool,
    pub user_notification: UserNotification,
    pub should_retry: bool,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct UserNotification {
    pub message: String,
    pub notification_type: NotificationType,
    pub actions: Vec<SuggestedAction>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum NotificationType {
    Info,
    Warning,
    Error,
    Success,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SuggestedAction {
    pub label: String,
    pub action_type: ActionType,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum ActionType {
    Retry,
    CheckNetwork,
    ReloadModel,
    SelectAlternativeModel,
    ContactSupport,
    ViewLogs,
}
```

### 14.2 分级日志系统

**日志配置**
```rust
use tracing::{Level, Subscriber};
use tracing_subscriber::{
    fmt::{self, time::ChronoUtc},
    layer::SubscriberExt,
    util::SubscriberInitExt,
    EnvFilter, Registry,
};

pub struct LoggerConfig {
    pub level: Level,
    pub file_path: PathBuf,
    pub max_file_size: u64,
    pub max_files: usize,
    pub enable_console: bool,
    pub enable_file: bool,
    pub structured_logging: bool,
}

impl Default for LoggerConfig {
    fn default() -> Self {
        Self {
            level: Level::INFO,
            file_path: PathBuf::from("logs/ai_studio.log"),
            max_file_size: 10 * 1024 * 1024, // 10MB
            max_files: 7, // 保留7个文件
            enable_console: cfg!(debug_assertions),
            enable_file: true,
            structured_logging: true,
        }
    }
}

pub struct Logger {
    config: LoggerConfig,
    file_appender: Option<tracing_appender::rolling::RollingFileAppender>,
}

impl Logger {
    pub fn new(config: LoggerConfig) -> Result<Self, std::io::Error> {
        // 创建日志目录
        if let Some(parent) = config.file_path.parent() {
            std::fs::create_dir_all(parent)?;
        }

        let file_appender = if config.enable_file {
            Some(tracing_appender::rolling::daily(
                config.file_path.parent().unwrap_or(&PathBuf::from(".")),
                config.file_path.file_name().unwrap_or(std::ffi::OsStr::new("app.log"))
            ))
        } else {
            None
        };

        Ok(Self {
            config,
            file_appender,
        })
    }

    pub fn init(&self) -> Result<(), Box<dyn std::error::Error>> {
        let env_filter = EnvFilter::try_from_default_env()
            .unwrap_or_else(|_| EnvFilter::new(self.config.level.to_string()));

        let registry = Registry::default().with(env_filter);

        // 控制台输出层
        let console_layer = if self.config.enable_console {
            Some(
                fmt::layer()
                    .with_timer(ChronoUtc::rfc_3339())
                    .with_target(true)
                    .with_thread_ids(true)
                    .with_thread_names(true)
                    .pretty()
            )
        } else {
            None
        };

        // 文件输出层
        let file_layer = if let Some(ref appender) = self.file_appender {
            Some(
                fmt::layer()
                    .with_timer(ChronoUtc::rfc_3339())
                    .with_target(true)
                    .with_thread_ids(true)
                    .with_thread_names(true)
                    .with_ansi(false)
                    .with_writer(appender)
                    .json() // 结构化日志
            )
        } else {
            None
        };

        // 组合所有层
        match (console_layer, file_layer) {
            (Some(console), Some(file)) => {
                registry.with(console).with(file).init();
            }
            (Some(console), None) => {
                registry.with(console).init();
            }
            (None, Some(file)) => {
                registry.with(file).init();
            }
            (None, None) => {
                return Err("至少需要启用一种日志输出方式".into());
            }
        }

        Ok(())
    }
}

// 错误指标
#[derive(Debug, Default)]
pub struct ErrorMetrics {
    pub total_errors: HashMap<String, u64>,
    pub critical_errors: u64,
    pub last_error_time: Option<chrono::DateTime<chrono::Utc>>,
    pub error_rate: f64,
}

impl ErrorMetrics {
    pub fn new() -> Self {
        Self::default()
    }

    pub fn increment_error_count(&mut self, error_code: &str) {
        *self.total_errors.entry(error_code.to_string()).or_insert(0) += 1;
    }

    pub fn increment_critical_errors(&mut self) {
        self.critical_errors += 1;
    }

    pub fn update_last_error_time(&mut self) {
        self.last_error_time = Some(chrono::Utc::now());
    }
}
```

## 15. 项目总结与展望

### 15.1 技术方案总结

**AI Studio** 是一个基于现代技术栈构建的企业级AI助手桌面应用，具备以下核心特性：

**技术架构优势：**
- **前端技术栈**：Vue 3.4+ + TypeScript 5.0+ + Vite 7.0+ + Naive UI 2.0+ + Tailwind CSS 3.0+
- **后端技术栈**：Tauri 2.x + Rust 1.75+ + SQLite 3.45+ + ChromaDB
- **跨平台支持**：原生支持 Windows 和 macOS，性能优异
- **安全设计**：AES-256加密、TLS 1.3传输、本地化处理
- **模块化架构**：高内聚低耦合，易于维护和扩展

**核心功能模块：**
1. **智能聊天系统**：支持流式响应、多模态输入、RAG集成
2. **知识库管理**：多格式文档解析、向量化存储、语义搜索
3. **模型管理**：本地模型加载、HuggingFace集成、性能监控
4. **多模态处理**：OCR、TTS、ASR、图像分析、视频处理
5. **远程配置**：API密钥管理、代理设置、配置同步
6. **局域网共享**：P2P通信、资源共享、安全认证
7. **插件系统**：WASM插件、JavaScript脚本、联网功能
8. **系统管理**：性能监控、日志管理、自动更新

**数据安全保障：**
- **本地优先**：所有敏感数据本地处理和存储
- **加密保护**：AES-256加密算法保护用户数据
- **权限控制**：细粒度权限管理和访问控制
- **隐私保护**：数据匿名化处理和PII检测

**性能优化策略：**
- **智能缓存**：多级缓存系统，LRU算法优化内存使用
- **并发控制**：信号量控制并发任务，避免资源竞争
- **数据库优化**：索引策略、连接池、批量操作
- **前端优化**：组件懒加载、虚拟滚动、状态管理优化

### 15.2 质量保证体系

**测试覆盖：**
- **单元测试**：覆盖率 > 85%，确保核心功能正确性
- **集成测试**：模块间交互测试，验证系统协调性
- **端到端测试**：完整用户流程测试，保证用户体验
- **性能测试**：响应时间 < 500ms，内存使用优化
- **安全测试**：漏洞扫描、渗透测试、数据保护验证

**质量指标：**
- **可靠性**：崩溃率 < 0.1%，24小时稳定运行
- **性能**：API响应时间 < 500ms (P95)，内存泄漏 < 2MB/24h
- **安全性**：0个高危漏洞，数据加密保护
- **可用性**：99.9%正常运行时间，自动错误恢复
- **可维护性**：模块化设计，代码质量评分 > 8.0

**持续集成：**
- **自动化构建**：GitHub Actions CI/CD流水线
- **代码质量**：ESLint、Clippy静态分析
- **安全扫描**：依赖漏洞检测、代码安全审计
- **性能监控**：基准测试、性能回归检测

### 15.3 部署和运维

**跨平台部署：**
- **Windows**：MSI安装包，注册表集成，系统通知
- **macOS**：DMG镜像，App Store兼容，原生菜单栏
- **自动更新**：增量更新、数字签名验证、回滚机制

**运维监控：**
- **系统监控**：CPU、内存、磁盘使用率实时监控
- **应用监控**：错误率、响应时间、用户行为分析
- **日志管理**：结构化日志、分级记录、自动清理
- **告警机制**：异常检测、邮件通知、自动恢复

### 15.4 技术创新点

**AI集成创新：**
- **本地AI优先**：支持完全离线的AI推理，保护用户隐私
- **多模态融合**：文本、图像、音频、视频的统一处理
- **RAG增强**：知识库与AI模型深度集成，提供准确答案
- **智能插件**：AI驱动的插件推荐和自动配置

**架构设计创新：**
- **Rust + Vue混合架构**：结合Rust的性能和Vue的开发效率
- **P2P资源共享**：局域网内设备间的智能资源共享
- **插件生态系统**：支持WASM、JavaScript多种插件类型
- **自适应性能**：根据系统资源动态调整处理策略

### 15.5 未来发展规划

**短期目标（3-6个月）：**
- **功能完善**：完成所有核心功能的开发和测试
- **性能优化**：达到企业级性能和稳定性要求
- **用户体验**：完善UI/UX设计，提升用户满意度
- **插件生态**：建立基础插件库，支持第三方开发

**中期目标（6-12个月）：**
- **AI能力增强**：集成更多先进AI模型和算法
- **企业功能**：添加团队协作、权限管理、审计日志
- **云端集成**：可选的云端同步和备份功能
- **移动端支持**：开发配套的移动端应用

**长期愿景（1-2年）：**
- **AI助手生态**：构建完整的AI助手生态系统
- **行业解决方案**：针对特定行业的定制化解决方案
- **开源社区**：建立活跃的开源社区和贡献者网络
- **国际化**：支持多语言，拓展国际市场

### 15.6 项目价值

**技术价值：**
- **技术栈先进性**：采用最新的技术栈和最佳实践
- **架构设计优秀**：模块化、可扩展、高性能的系统架构
- **安全性保障**：企业级的安全设计和隐私保护
- **开发效率**：现代化的开发工具链和自动化流程

**商业价值：**
- **市场需求**：满足企业和个人对AI助手的迫切需求
- **竞争优势**：本地化处理、隐私保护的差异化优势
- **可扩展性**：插件生态系统提供无限扩展可能
- **商业模式**：多元化的商业模式和盈利渠道

**社会价值：**
- **隐私保护**：推动AI应用的隐私保护标准
- **技术普及**：降低AI技术的使用门槛
- **开源贡献**：为开源社区贡献高质量代码
- **人才培养**：培养AI和Rust技术人才

---

**AI Studio** 项目代表了现代AI助手应用的技术前沿，通过创新的架构设计、严格的质量保证和完善的安全机制，为用户提供了一个功能强大、安全可靠、易于使用的AI助手平台。项目不仅具有重要的技术价值和商业价值，更承载着推动AI技术普及和隐私保护的社会责任。

随着项目的不断发展和完善，AI Studio将成为AI助手领域的标杆产品，引领行业发展方向，为构建更加智能、安全、便民的数字化社会贡献力量。